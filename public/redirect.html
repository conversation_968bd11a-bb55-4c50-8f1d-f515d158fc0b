<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳转中...</title>
    <script name="API">window._apiUrl = 'https://tapi.hupozhidao.com'</script>
    <script>
        // 获取项目参数
        function getProjectParam () {
            const query = window.location.search.substring(1)
            const params = new URLSearchParams(query.replace(/,/g, '&'))
            return Object.fromEntries(params.entries()) || null
        }

        async function callApi (url) {
            try {
                const response = await fetch(url)
                if (!response.ok) {
                    throw new Error(response.msg)
                }
                const data = await response.json()
                console.log('data', data)
                return data
            } catch (error) {
                console.error(error)
                return null
            }
        }



        // 根据条件判断跳转到不同的项目地址
        async function redirectToProject () {
            let query = getProjectParam()
            if (!query?.state) {
                alert('缺少必要的 state 参数，无法跳转')
                return
            }
            const apiUrl = `${window._apiUrl}/manage/ad/back-url?state=${query.state}`
            try {
                const { data } = await callApi(apiUrl)
                if (data?.url) {
                    if (query.state === 'a') {
                        window.location.href = `${data.url}?state=${query.state}&auth_code=${query.auth_code}`
                    } else {
                        window.location.href = `${data.url}?state=${query.state}&auth_code=${query.auth_code}&type=${query.state}`
                    }

                } else {
                    alert('获取跳转地址失败，请稍后再试')
                }
            } catch (error) {
                console.error(error)

            }

        }

        // 页面加载完成后执行跳转逻辑
        window.onload = redirectToProject;
    </script>
</head>

<body>
    跳转中...
</body>

</html>