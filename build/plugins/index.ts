import unplugin from './icon'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import babel from 'rollup-plugin-babel'
import { vitePluginRouterType } from './router'
import { getRootPath, getSetting, getSrcPath } from '../utils'
import path from 'path'
import { Setting, SettingEnv } from '../../types'
import { replaceFile } from './replaceFile'
import { uploadCosPlugin } from './vite-plugin-upload-cos'
import vitePluginUploadOss from './vite-plugin-upload-oss'
import vitePluginUploadSsh from './vite-plugin-upload-ssh'
import fs from "fs"

/**
 * vite插件
 */
export async function setupVitePlugins(env: SettingEnv, mode: string) {
  let setting = await getSetting(mode)
  const redirectHtmlPath = path.resolve(process.cwd(), 'public/redirect.html')
  let redirectHtml = fs.readFileSync(redirectHtmlPath, 'utf-8')
  //正则匹配 script name为API的替换window._apiUrl的值
  const updatedHtml = redirectHtml.replace(
    /<script\s+name=["']API["']>.*?<\/script>/s,
    `<script name="API">window._apiUrl = '${env.API_URL}'</script>`
  );
  fs.writeFileSync(redirectHtmlPath, updatedHtml)


  let injectHtmlObj = {
    APP_INJECT_SCRIPT: ''
  }

  const plugin = [
    vueJsx(),
    vue(),
    UnoCSS(),
    ...unplugin(env),
    vitePluginRouterType(
      path.resolve(getSrcPath(), 'views'),
      path.resolve(getRootPath(), 'types/cache/routerType.d.ts')
    ),
    createHtmlPlugin({
      APP_TITLE: env.TITLE,
      ...injectHtmlObj,
      link: setting.exetrnal?.css || [],
      script: setting.exetrnal?.js || []
    }),
    babel({ runtimeHelpers: true }),
    replaceFile({ env: env, filePath: setting.files })
  ]
  // 正式 测试需要的操作
  if (['test', 'prod'].includes(env.NODE_ENV)) {
    console.warn('当前选择的上传类型：', env.UPLOAD_TYPE)
    if (env.UPLOAD_TYPE == 'ssh') {
      // 测试环境使用ssh上传到宝塔
      plugin.push(
        vitePluginUploadSsh({
          SSH_USERNAME: env.COS.put.SSH_USERNAME,
          SSH_HOST: env.COS.put.SSH_HOST,
          SSH_PORT: env.COS.put.SSH_PORT,
          SSH_KEY: env.COS.put.SSH_KEY,
          SSH_DIR: env.COS.put.SSH_DIR
        })
      )
    } else if (env.UPLOAD_TYPE == 'oss') {
      plugin.push(
        vitePluginUploadOss({
          ignore: '*',
          base: env.COS.url,
          baseUrl: env.BASE_URL,
          buildPath: 'dist',
          overwrite: true,
          timeout: '50000',
          region: env.COS.put.region,
          accessKeyId: env.COS.put.secretId,
          accessKeySecret: env.COS.put.secretKey,
          refreshSecretId: env.COS.put.refreshSecretId,
          refreshSecretKey: env.COS.put.refreshSecretKey,
          bucket: env.COS.put.buket
        })
      )
    } else if (env.UPLOAD_TYPE == 'cos') {
      plugin.push(
        uploadCosPlugin({
          cosBaseDir: `/`,
          bucket: env.COS.put.buket,
          region: env.COS.put.region,
          SecretId: env.COS.put.secretId,
          SecretKey: env.COS.put.secretKey,
          uploadDir: 'dist',
          refreshCdn: true,
          webUrl: env.BASE_URL,
          extraFiles: [...setting.files.map((v) => `${v.name}.${v.type}`), '违禁词导入模版.xlsx', 'dount.js', 'robots.txt', 'generalMerchantProcess.doc']
        })
      )
    }
  }
  return plugin
}

interface HtmlPluginType {
  /**
   * 单独注入的js dount
   */
  APP_INJECT_SCRIPT: string
  /**
   * title
   */
  APP_TITLE: string
  /**
   * csse外链
   */
  link: Setting['exetrnal']['css']
  /**
   * js外链
   */
  script: Setting['exetrnal']['js']
}
function createHtmlPlugin(params: HtmlPluginType) {
  return {
    name: 'create-html-plugin',
    transformIndexHtml(html) {
      return html
        .replace('APP_TITLE', params['APP_TITLE'] || '')
        .replace('APP_INJECT_SCRIPT', params['APP_INJECT_SCRIPT'] || '')
    },
    transform(code: string, id: string) {
      if (id.endsWith('main.ts')) {
        const cssFiles = params.link || []
        const jsFiles = params.script || []
        const tags = [...cssFiles, ...jsFiles].map((file) => `import "@/${file.src}"`).join('\n')
        return tags + ' \n' + code
      }
    }
  }
}
