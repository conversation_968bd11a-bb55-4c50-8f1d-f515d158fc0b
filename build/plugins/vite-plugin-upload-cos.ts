import COS from 'cos-nodejs-sdk-v5'
import fs from 'fs'
import tencentcloud from 'tencentcloud-sdk-nodejs-cdn'
const CdnClient = tencentcloud.cdn.v20180606.Client

const pluginRefreshCdn = async (options) => {
  const clientConfig = {
    credential: {
      secretId: options.SecretId,
      secretKey: options.SecretKey
    },
    region: '',
    profile: {
      httpProfile: {
        endpoint: 'cdn.tencentcloudapi.com'
      }
    }
  }
  console.log(options)
  // 实例化要请求产品的client对象,clientProfile是可选的
  try {
    const client = new CdnClient(clientConfig)
    const params = {
      Paths: [options.endpoint],
      FlushType: 'delete'
    }
    await client.PurgePathCache(params)
    console.log('刷新cdn成功')
  } catch (error) {
    console.error('刷新cdn失败', error)
  }
}

/**
 * vite-plugin-Upload-cos 静态资源上传cos rollup插件
 * @SecretId String 腾讯云cos SecretId
 * @SecretKey String 腾讯云cos SecretKey
 * @bucket String 腾讯云cos bucket
 * @region String 腾讯云cos region
 * @cosBaseDir String 腾讯云cos存放的资源的地址
 * @uploadDir String 选填，腾讯云cos存放的资源的地址
 * @excludes []String 选填，不需要上传的文件数组
 *
 * @returns
 */
export function uploadCosPlugin({
  SecretId,
  SecretKey,
  bucket,
  region,
  cosBaseDir,
  uploadDir = 'dist',
  excludes = [],
  refreshCdn = false,
  webUrl = '',
  extraFiles = []
}) {
  const cosObject = new COS({
    SecretId,
    SecretKey
  })
  return {
    name: 'vite-plugin-upload-cos',
    async writeBundle(_option, bundles) {
      const uploadfiles = Object.keys(bundles).filter((item) => !excludes.includes(item))
      //   uploadfiles.push('favicon.ico')
      uploadfiles.push(...(extraFiles || []))
      function upload2CosPromise({ url }) {
        return new Promise((resolve) => {
          cosObject.putObject(
            {
              Bucket: bucket,
              Region: region,
              Key: `${cosBaseDir}/${url}`,
              StorageClass: 'STANDARD',
              Body: fs.createReadStream(`./${uploadDir}/${url}`),
              onProgress: function () {
                console.log(`\x1B[36m[vite-plugin-upload-cos]Uploading ${url}\x1B[0m`)
              }
            },
            function (err) {
              if (err) {
                resolve(`Upload ${url} error: ${JSON.stringify(err)}`)
              } else {
                resolve(`${url} Uploaded!`)
              }
            }
          )
        })
      }

      const files = []
      uploadfiles.forEach((item) => {
        const promiseFile = upload2CosPromise({
          url: item
        })
        files.push(promiseFile)
      })
      ;(async () => {
        await Promise.all(files)
          .then((res) => {
            console.log('[vite-plugin-Upload-cos]Upload to COS finish: ', res)
          })
          .catch((err) => {
            console.log('[vite-plugin-Upload-cos]Upload to COS fails: ', err)
          })
        if (refreshCdn && webUrl) {
          await pluginRefreshCdn({
            SecretId,
            SecretKey,
            endpoint: webUrl
          })
        }
      })()
    }
  }
}
