//tab切换的样式

.common_after_border {
  .ant-tabs-nav {
    margin-bottom: 4px;
  }
  .ant-tabs-nav-list {
    .ant-tabs-tab {
      margin-left: 40px;
    }
    .ant-tabs-tab::before {
      content: '';
      display: block;
      width: 1px;
      height: 8px;
      background: #e6e8ed;
      margin-right: 16px;
    }
    .ant-tabs-tab:first-child:before {
      height: 0;
      width: 0;
      margin-right: 0;
    }
    .ant-tabs-tab:first-child {
      margin-left: 0;
    }

    .ant-tabs-nav .ant-tabs-tab-active {
      .ant-badge {
        color: var(--primary-color) !important;
      }
    }
    .ant-tabs-ink-bar {
      display: none;
      height: 0 !important;
    }
  }
  .ant-tabs-nav::before {
    border: none;
  }
  .ant-tabs-nav .ant-tabs-tab-active {
    .css-dev-only-do-not-override-1r9rp2d {
      color: var(--primary-color);
    }
  }

  .ant-tabs-tab {
    .ant-tabs-tab-btn {
      color: #5e6584;
    }
  }
  .ant-badge {
    color: #5e6584;
  }
  .ant-tabs-tab-active {
    .tab_item {
      .ant-badge {
        color: var(--primary-color);
      }
    }
  }
}
.title_line {
  display: block;
  width: 3px;
  height: 18px;
  background: var(--primary-color);
  border-radius: 2px;
  margin-right: 8px;
}
.hyd_mb_10px {
  margin-bottom: 10px;
}
.ant-table-wrapper {
  .ant-table-thead > tr > th {
    background-color: #f8f8f9 !important;
    &::before {
      display: none;
    }
  }
}
.ant-btn-link {
  color: #647dff;
}
.ant-btn-link:hover {
  color: #8ca3ff;
}
.comm_header_title,
.common_card_wrapper .ant-card-head-title,
.comm_space_wrapper .ant-descriptions-title {
  position: relative;
  display: flex;
  align-items: center;
  line-height: 18px;
  &::before {
    content: '';
    display: block;
    width: 3px;
    height: 18px;
    background: var(--primary-color);
    border-radius: 2px;
    margin-right: 8px;
  }
}
.common_card_wrapper .ant-card-body {
  padding-top: 16px;
}
