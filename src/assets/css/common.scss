html,
body,
#app {
  // --sidebar-width: 260px;
  height: 100%;
  color: var(--text-color-base);
}

.text_overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.text_overflow_row1 {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.text_overflow_row2 {
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.page_main {
  min-height: calc(100vh - 76px);

  &.ant-card {
    .ant-card-head {
      padding: 16px 24px 0;
      border-bottom: none;
      min-height: auto;
    }
  }

  // .page_table_toolbar {
  //   margin-top: 20px;
  // }
  .ant-table-wrapper {
    .ant-table-thead > tr > th {
      background-color: var(--header-bg-color);

      &::before {
        display: none;
      }
    }
  }

  .ant-table-wrapper.is-striped {
    .ant-table-tbody {
      .ant-table-row:nth-child(2n) {
        background-color: #fafcfe;
      }

      .ant-table-row:hover {
        td {
          background-color: #f4fafc;
        }
      }
    }
  }
}

/* 滚动条样式 */
.common_scroll {
  &::-webkit-scrollbar {
    width: 4px;
    height: 8px;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
  }

  &::-webkit-scrollbar-track {
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    display: block;
    background-color: #e0e0e0;
  }
}

.common_scroll_y {
  @extend .common_scroll;
  overflow-y: auto;
}

.common_scroll_x {
  @extend .common_scroll;
  overflow-x: auto;
}

.common_scroll_y_hover {
  @extend .common_scroll_y;

  &::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: #e0e0e0;
    }
  }
}

.common_scroll_x_hover {
  @extend .common_scroll_x;

  &::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: #e0e0e0;
    }
  }
}

.flex {
  display: flex;
}

.flex_center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex_align_center {
  display: flex;
  align-items: center;
}

.flex_ju_sp {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex_column {
  flex-direction: column;
}
.align_start {
  align-items: flex-start;
}
.flex_1 {
  flex: 1;
}

.ant-btn + .ant-btn {
  margin-left: 10px;
}

.text_overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  padding: 0;
  background-color: #fff;
  border-radius: 4px;

  .card_title {
    padding: 32px 24px 0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 16px;
  }

  .card_body {
    display: block;
    box-sizing: border-box;
    height: 100%;
    padding: 24px;
  }
}

.text-helper {
  color: #939599;
  font-size: 12px;
}

// :where(.css-dev-only-do-not-override-h38ooi).ant-popover .ant-popover-inner {
//   padding: 0;
// }
// .ant-popover-title {
//   padding: 12px 16px 0px;
//   border: none;
// }
// .ant-popover-inner-content {
//   // padding: 0 0 10px;
// }

.ant-btn.ant-btn-sm {
  height: 24px;
}

// 全屏弹窗
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }

  .ant-modal-body {
    flex: 1;
  }
}

.ant-table-container > .ant-table-content > table > tbody > tr > td,
.ant-table.ant-table-small .ant-table-tbody > tr > td {
  // vertical-align: top;
}

.number-id {
  font-size: var(--font-size-mini) !important;
}

.modalWrapClassName .ant-modal-content {
  padding: 0;

  .close {
    background: rgba(255, 255, 255, 0.69);
    position: absolute;
    top: 16px;
    right: 16px;
    height: 22px;
    width: 47px;
    text-align: center;
    line-height: 22px;
    cursor: pointer;
    color: #313233;
    z-index: 10;
    border-radius: 4px;
    backdrop-filter: blur(2px);
  }
}

.layout--badge {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  transform: translate(50%, -50%);
  transform-origin: 100% 0%;
}
.common_page_warp .ant-card-head-wrapper .ant-card-head-title,
.common_page_warp .ant-card-head-wrapper {
  align-items: flex-start;
}
.ant-card .ant-card-head-wrapper {
  align-items: flex-start;
}
.ant-card .ant-card-head {
  padding: 16px 24px 0 24px;
  line-height: 1;
  min-height: auto;
}
.ant-pagination {
  margin-top: 16px;
}
