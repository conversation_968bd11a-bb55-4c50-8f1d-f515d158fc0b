<template>
  <div>
    <div class="coupon_details">
      <div class="model_block">
        <a-breadcrumb separator="/">
          <a-breadcrumb-item>
            <router-link :to="{ name: 'AdvSetting' }">广告位管理</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ route.query.id ? '编辑广告' : '新建广告' }}</a-breadcrumb-item>
        </a-breadcrumb>
        <div>
          <div class="title">广告信息</div>
          <a-form ref="formRef" :model="state.from" :rules="rules" :label-col="{ style: { width: '80px' } }">
            <a-form-item label="广告位置" name="ad_site" required>
              <a-select
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                v-model:value="state.from.ad_site"
                :options="siteEditOptions"
                :maxlength="100"
                placeholder="请输入广告位置"
                style="width: 350px"
              >
              </a-select>
            </a-form-item>
            <a-form-item label="广告标题" name="ad_title" required>
              <a-input
                v-model:value="state.from.ad_title"
                :maxlength="100"
                placeholder="请输入广告标题"
                style="width: 350px"
              ></a-input>
            </a-form-item>
            <a-form-item label="图片" name="ad_img" required>
              <Upload v-model="state.from.ad_img" :max="1" :size="2" use="static" @change="onUpload"></Upload>
            </a-form-item>
            <a-form-item label="跳转类型" name="jump_type">
              <a-radio-group v-model:value="state.from.jump_type" @change="state.from.jump_url = ''">
                <a-radio
                  v-for="item in state.jumptypeOptions"
                  :key="item.label"
                  :value="item.value"
                  @click.prevent="onCancel(item.value)"
                >
                  {{ item.label }}
                </a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item
              v-if="state.from.jump_type == 3"
              label="图广告链接(URL)"
              name="jump_url"
              :rules="[
                {
                  required: true,
                  pattern: /https:\/\//g,
                  message: '仅支持输入https://开头的链接格式'
                }
              ]"
            >
              <a-input
                v-model:value="state.from.jump_url"
                :maxlength="100"
                placeholder="请输入图广告链接（URL）"
                style="width: 350px"
              ></a-input>
              <div class="tip">仅支持输入https://开头的链接格式</div>
            </a-form-item>
            <a-form-item v-if="state.from.jump_type == 1" label="商品ID">
              <!-- <a-input v-model:value="state.from.jump_url" :maxlength="100" placeholder="请输入商品ID"></a-input> -->
              <GoodsNo v-model:value="state.from.jump_url"></GoodsNo>
            </a-form-item>
            <a-form-item v-if="state.from.jump_type == 2" label="店铺ID">
              <!-- <a-input v-model:value="state.from.jump_url" :maxlength="100" placeholder="请输入店铺ID"></a-input> -->
              <ShopNo v-model:value="state.from.jump_url"></ShopNo>
            </a-form-item>
            <a-form-item v-if="state.from.jump_type == 4" label="appid" name="jump_app_id">
              <a-input
                v-model:value="state.from.jump_app_id"
                :maxlength="100"
                placeholder="请输入小程序appid"
                style="width: 350px"
              ></a-input>
            </a-form-item>
            <a-form-item
              v-if="state.from.jump_type == 4"
              label="path"
              name="jump_url"
              :rules="[
                {
                  required: true,
                  pattern: /^[^\s\u4e00-\u9fa5]*$/, // 正则表达式，确保字符串中不包含空格或汉字
                  message: '请输入不含空格或汉字的小程序路径'
                }
              ]"
            >
              <a-input
                v-model:value="state.from.jump_url"
                :maxlength="100"
                placeholder="请输入小程序路径"
                style="width: 350px"
              ></a-input>
              <div class="tip">格式示例：/pages/order/list</div>
            </a-form-item>
            <a-form-item
              v-if="state.from.jump_type == 5"
              label="图片"
              name="jump_img"
              :rules="[
                {
                  required: true,
                  message: '请上传图片'
                }
              ]"
            >
              <Upload
                v-model="state.from.jump_img"
                :max="1"
                :size="50"
                use="static"
                accept=".jpg,.png,.jpeg,.gif"
                @change="onUploadJumpImg"
              ></Upload>
            </a-form-item>

            <a-form-item label="展示时间" name="show_status">
              <a-radio-group v-model:value="state.from.show_status">
                <a-radio :value="1">立即启用</a-radio>
                <a-radio :value="2">
                  <a-space>
                    <span>定时启用</span>
                    <a-date-picker
                      v-model:value="state.from.show_time"
                      :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }"
                      placeholder="请选择启用时间"
                    />
                  </a-space>
                </a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="停用时间" name="stop_time">
              <a-date-picker
                v-model:value="state.from.stop_time"
                :disabled-date="disabledDate"
                :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }"
                valueFormat="X"
                placeholder="请选择停用时间"
              />
            </a-form-item>
            <a-form-item label="排序" name="sort">
              <a-input-number v-model:value="state.from.sort" :precision="0" :min="0" :max="1000"></a-input-number>
              <div class="tip">支持输入0-1000内的整数，数值越小排序越靠前</div>
            </a-form-item>
            <a-form-item label="启用状态" name="status">
              <a-radio-group v-model:value="state.from.status">
                <a-radio :value="1">启用</a-radio>
                <a-radio :value="2">禁用</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item
              :wrapper-col="{
                sm: {
                  offset: 8,
                  span: 16
                },
                md: {
                  offset: 4,
                  span: 10
                },
                lg: {
                  offset: 4,
                  span: 10
                },
                xl: {
                  offset: 3,
                  span: 10
                },
                xxl: {
                  offset: 2,
                  span: 10
                }
              }"
            >
              <a-space style="margin-top: 30px">
                <a-button type="primary" :loading="state.submitLoading" @click="submitForm(formRef)">确认提交</a-button>
                <a-button @click="router.back()">取消</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
  import { reactive, ref } from 'vue'
  import Upload from '@/components/ui/common/upload/index.vue'
  import GoodsNo from './components/Goods.vue'
  import ShopNo from './components/Shop.vue'
  import { LocationQueryValue, useRoute, useRouter } from 'vue-router'
  import datas from './src/datas'
  import moment from 'moment'
  import dayjs from 'dayjs'
  import { saveAdSetApi, updateAdSetApi, getAdSetDetailApi } from './index.api'
  import { message } from 'ant-design-vue'
  import { useTheme, useApp } from '@/hooks'
  const { rules, siteEditOptions, jumptypeOptions } = datas()
  const { themeVar } = useTheme()
  import { useRouterBack } from '@/hooks'
  const { routerBack } = useRouterBack()

  const route = useRoute()
  const router = useRouter()
  const formRef = ref(null)
  const state = reactive({
    submitLoading: false,
    loading: false,
    jumptypeOptions: jumptypeOptions,
    from: {
      ad_site: '',
      ad_title: '',
      ad_img: [],
      jump_img: [],
      show_time: '',
      stop_time: '',
      sort: 0,
      status: 1,
      jump_type: 0,
      jump_url: '',
      show_status: 1,
      jump_app_id: ''
    }
  })

  // 停用时间限制
  const disabledDate = (current) => {
    if (!state.from.show_time) return false
    return current && current < state.from.show_time
    // return moment(date).diff(Number(state.from.show_time) * 1000, 'months') >= 3
  }

  // 跳转类型选择
  const onCancel = (val) => {
    state.from.jump_type = val === state.from.jump_type ? 0 : val
    state.from.jump_url = ''
  }

  async function submitForm(formName) {
    try {
      formName
        .validate()
        .then(async () => {
          if (state.from.show_status === 2 && Number(state.from.show_time) == 0) {
            return message.warning('请选择启用时间')
          }
          if (
            state.from.show_status === 2 &&
            Number(dayjs(state.from.show_time).unix()) >= Number(state.from.stop_time)
          ) {
            return message.warning('停用时间不能小于启用时间')
          }
          state.submitLoading = true
          let obj = {
            ad_img: state.from.ad_img[0]?.url || '',
            jump_img: state.from.jump_img[0]?.url || '',
            show_time:
              state.from.show_time && state.from.show_status === 1
                ? null
                : Number(dayjs(state.from.show_time).unix()) || null,
            stop_time: Number(state.from.stop_time) || null
          }
          let res = route.query.id
            ? await updateAdSetApi({ id: Number(route.query.id), ...state.from, ...obj })
            : await saveAdSetApi({ ...state.from, ...obj })
          message.success('提交成功')
          // routerBack({ name: 'AdSet', params: { page: route.query.id ? 'edit' : 'add' } })
          routerBack({ name: 'AdvSetting', params: { page: route.query.id ? 'edit' : 'add' } })
        })
        .catch((err) => {
          state.submitLoading = false
          console.log('error', err)
        })
    } catch (error) {
      console.error(error)
    } finally {
      state.submitLoading = false
    }
  }
  // 获取详情
  async function getDetail() {
    if (!route.query.id) return
    try {
      state.loading = true
      let res = await getAdSetDetailApi({ id: route.query.id })
      for (let key in state.from) {
        if (key == 'ad_img') {
          state.from[key] = [{ url: res.data[key] }]
        } else if (key == 'jump_img') {
          state.from[key] = res.data[key] ? [{ url: res.data[key] }] : []
        } else if (key == 'show_status') {
          state.from[key] = res.data[key] || 1
        } else if (key == 'show_time') {
          state.from[key] = res.data[key] ? dayjs.unix(res.data[key]) : null
        } else {
          state.from[key] = res.data[key]
        }
      }
      console.log(state.from)
    } catch (error) {
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  getDetail()

  // 上传
  const onUpload = (data) => {
    formRef.value.clearValidate(['ad_img'])
  }
  const onUploadJumpImg = () => {
    formRef.value.clearValidate(['jump_img'])
  }
</script>

<style lang="scss" scoped>
  .model_block {
    background-color: #fff;
    padding: 24px;
    margin-bottom: 10px;
    border-radius: var(--border-radius-huge);
    .title {
      line-height: 18px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #080f1e;
      margin: 20px 0;
      display: flex;
      align-items: center;
    }
    .title::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 18px;
      background: var(--primary-color);
      border-radius: 2px;
      margin-right: 8px;
    }
    .search {
      margin-bottom: 20px;
    }
  }
  .tip {
    margin-top: 10px;
  }
</style>
