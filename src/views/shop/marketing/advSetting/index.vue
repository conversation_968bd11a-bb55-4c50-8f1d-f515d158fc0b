<template>
  <!-- <div class="marketing_ad">
    <a-card class="page_main" title="广告位管理">
      <template #title>广告位管理</template>
      <template #extra>
        <a-button v-auth="['shopAdAdd']" type="primary" @click="router.push({ path: '/shop/marketing/adEdit' })"
          >新建广告</a-button
        >
      </template> -->

  <DesTablePage class="marketing_ad page_main common_card_wrapper">
    <template #title>
      <div class="flex flex-justify-between flex-items-center">
        <span>广告位管理1</span>
      </div>
    </template>
    <template #extra>
      <a-button v-auth="['shopAdAdd']" type="primary" @click="router.push({ path: '/shop/marketing/adEdit' })"
        >新建广告</a-button
      >
    </template>
    <template #search>
      <SearchBaseLayout
        :data="searchData"
        @changeValue="searchForm"
        :actions="{
          foldNum: 0
        }"
        class="mb-16px"
      />
    </template>

    <template #tableWarp>
      <a-space class="page_table_toolbar" :size="[12, 8]">
        <template v-for="(v, i) in btnsData" :key="i">
          <a-button
            v-auth="[v.auth]"
            :type="v.type"
            :disabled="data.selectionItem.length == 0"
            @click="del(data.selectionItem.map((v) => v.id))"
            >{{ v.name }}</a-button
          >
        </template>
      </a-space>
      <TableZebraCrossing
        :data="data.tableConfigOptions"
        class="mt-16px"
        :row-selection="{ selectedRowKeys: data.selectedRowKeys, onChange: onSelectChange }"
      >
        <template #bodyCell="{ scope: { record, column } }">
          <template v-if="column.key === 'ad_img'">
            <a-image :src="record.ad_img" class="ad-img"></a-image>
          </template>
          <template v-if="column.key === 'show_time'">
            <div v-if="record.show_time || record.stop_time">
              <a-space direction="vertical">
                <div>{{ record.show_status == 1 ? record.created_at : record.show_time }}</div>
                <div>至</div>
                <div>{{ record.stop_time }}</div>
              </a-space>
            </div>
            <div v-else>--</div>
          </template>
          <template v-if="column.key === 'state'">
            <span> {{ record.status == 1 ? '启用' : '禁用' }}</span>
          </template>
          <template v-if="column.key === 'handle'">
            <a-space :size="[10, 0]" class="handle_btns">
              <span
                v-auth="['shopAdEdit']"
                @click="router.push({ path: '/shop/marketing/adEdit', query: { id: record.id } })"
              >
                编辑
              </span>

              <span v-auth="['shopAdChange']" @click="enable(record)">
                {{ record.status == 1 ? '禁用' : '启用' }}
              </span>
              <span v-auth="['shopAdDel']" class="del" @click="del(record.id)">删除</span>
            </a-space>
          </template>
        </template>
      </TableZebraCrossing>
      <Pagination
        v-model:page="data.params.page"
        v-model:pageSize="data.params.page_size"
        :total="data.total"
        @change="getList"
      ></Pagination>
    </template>
  </DesTablePage>
  <!-- </a-card> -->
  <!-- </div> -->
</template>
<script setup lang="tsx">
  defineOptions({ name: 'AdvSetting' })
  import Pagination from '@/components/ui/common/PaginationComponent/index.vue'
  import datas from './src/datas'
  import moment from 'moment'
  import { reactive, createVNode, onActivated } from 'vue'
  import { useRouter } from 'vue-router'
  import { formatDate } from '@/utils'
  import { message, Modal } from 'ant-design-vue'
  import { getAdSetListApi, deleteAdSetApi, batchDelAdSetApi, updateStatusAdSetApi } from './index.api'
  import { getShopListApi } from '@/api/common'
  import { useRouterBack } from '@/hooks'
  const { routeParams, resetRouteParams } = useRouterBack()
  const router = useRouter()
  const { searchData, columns, siteOptions, jumptypeOptions } = datas()

  const data = reactive({
    params: {
      page: 1,
      page_size: 10,
      shelf_time: '',
      show_time_start: '',
      show_time_end: ''
    },
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: [],
    tableConfigOptions: {
      bordered: true,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 'max-content'
      },
      dataSource: [],
      columns: columns,
      pagination: false
    },
    total: 0
  })
  // 批量按钮
  const btnsData = reactive([
    {
      name: '批量删除',
      auth: 'shopAdBatchDel',
      confirm: {
        message: '你确定批量删除吗？'
      }
    }
  ])
  // 获取列表数据
  const getList = async () => {
    try {
      let res = await getAdSetListApi(data.params)
      data.tableConfigOptions.dataSource = (res.data?.list || []).map((v) => {
        return {
          ...v,
          ad_site: siteOptions.find((g) => g.value == v.ad_site)?.label || '',
          jump_type: jumptypeOptions.find((g) => g.value == v.jump_type)?.label || '--',
          created_at: moment(v.created_at * 1000).format('YYYY-MM-DD HH:mm:ss'),
          show_time: v.show_time ? moment(v.show_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '',
          stop_time: v.stop_time ? moment(v.stop_time * 1000).format('YYYY-MM-DD HH:mm:ss') : ''
        }
      })
      data.total = res.data?.total_num || 0
    } catch (error) {
      console.error(error)
    }
  }
  getList()
  onActivated(() => {
    if (routeParams.params?.page) {
      if (routeParams.params?.page === 'add') {
        data.params.page = 1
        data.params.page_size = 10
      }
      getList()
      resetRouteParams()
    }
  })
  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    data.selectedRowKeys = selectedRowKeys
    data.selectionItem = selectedRows
  }
  const searchForm = (v: { formData: { page: number; page_size: number; shelf_time: any } }) => {
    data.params = {
      ...data.params,
      ...v.formData
    }
    console.log(v.formData, 'v.formData')

    if (v.formData.shelf_time?.length) {
      data.params.show_time_start = moment(v.formData.shelf_time[0]).format('X')
      data.params.show_time_end = moment(v.formData.shelf_time[1]).format('X')
    } else {
      data.params.show_time_start = ''
      data.params.show_time_end = ''
    }

    getList()
  }

  // 查看详情
  const lookOver = (row: { id: any; coupon_type: any }) => {
    router.push({ path: 'MarketingCouponDetail', query: { id: row.id, type: row.coupon_type } })
  }

  async function enable(row) {
    try {
      let text = row.status == 1 ? '禁用' : '启用'
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, `是否${text}当前广告？`),
        async onOk() {
          await updateStatusAdSetApi({ id: row.id, status: row.status == 1 ? 2 : 1 })
          message.success(`${text}成功`)
          await getList()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }

  async function del(id) {
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '是否删除当前广告？'),
        async onOk() {
          Array.isArray(id) ? await batchDelAdSetApi({ ids: id.join(',') }) : await deleteAdSetApi({ id })
          message.success('删除成功')
          await getList()
          // tableRef.value.clearSelection()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
</script>
<style lang="scss" scoped>
  .marketing_ad {
    :deep(.ant-image-img.ad-img) {
      width: 100px;
      height: 50px;
      object-fit: contain;
    }

    .round {
      width: 8px;
      height: 8px;
      background: #404040;
      border-radius: 50%;
      display: inline-block;
    }
    .line {
      border-bottom: 1px solid #f3f6fc;
    }
    .handle_btns {
      user-select: none;
      span {
        color: var(--primary-color);
        cursor: pointer;
      }
      span:nth-last-of-type(1) {
        margin-right: 0;
      }
    }
    .card {
      font-family: PingFang SC;
      font-weight: 400;
      // width: 320px;
      // height: 145px;
      background: #f5f7f9;
      border-radius: var(--border-radius-huge);
      box-sizing: border-box;
      padding: 24px;
      &_left {
        &_title {
          line-height: 16px;
          font-size: 16px;
          color: #080f1e;
          margin-top: -1px;
        }
        .line {
          width: 18px;
          height: 2px;
          margin: 9px 0 10px;
        }
        &_content {
          line-height: 14px;
          margin-top: 4px;
          font-size: 14px;
          color: #404040;
          // font-family: PingFangSC-Regular;
          margin-bottom: 20px;
        }
      }
      .card_title {
        margin-bottom: 18px;
        font-weight: 500;
        font-size: 14px;
        font-family: PingFang SC;
        font-style: normal;
        line-height: 24px;
        letter-spacing: 0.5px;
        text-align: center;
      }
      .card_content {
        margin-bottom: 18px;
        color: #97a0c3;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFang SC;
        font-style: normal;
        line-height: 24px;
        letter-spacing: 0.5px;
        text-align: center;
      }
    }

    .pd20 {
      padding: 20px;
    }

    .pd_20 {
      padding: 20px 30px 4px 30px;
    }
    .pd_30 {
      padding: 30px;
      &.pb_30 {
        padding-bottom: 30px;
      }
    }
    .flex_justify_space_bet {
      justify-content: space-between;
    }
    :deep(.comp_searchForm .item-el-select .el-select) {
      width: 100%;
    }
    .disBtn {
      color: #999 !important;
      cursor: not-allowed;
      pointer-events: none;
    }

    // 按钮样式
    .card_btn {
      text-align: center;
    }
    .mt_10 {
      margin-top: 10px;
    }
    .mb_24 {
      margin-bottom: 24px;
    }
    .p_0 {
      padding: 0;
    }
  }
</style>
