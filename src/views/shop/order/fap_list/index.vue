<template>
  <CardBaseLayout>
    <template #title>
      <div>问答管理</div>
    </template>
    <template #content>
      <a-space direction="vertical" class="w-full">
        <SearchBaseLayout
          :data="state.searchConfig.data"
          @changeValue="changeValue"
          :actions="state.searchConfig.options"
        />
        <TableZebraCrossing :data="state.tableConfig" @change="pageChange">
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'product_info'">
              <GoodDetail
                :image="scope.record.product_img"
                :name="scope.record.product_name"
                :code="scope.record.product_code"
              />
            </template>
            <template v-if="scope.column.key === 'question'">
              <div class="question">{{ scope.record.question }}</div>
            </template>
            <template v-if="scope.column.key === 'all_resp_num'">
              <a-button type="link" @click="handleActions('fapNum', scope.record)">{{
                scope.record.all_resp_num
              }}</a-button>
            </template>
            <template v-if="scope.column.key === 'is_vaild'">
              <a-space>
                <div class="status_dot" :style="{ background: statusType(scope.record.is_vaild).color }"></div>
                <div class="status_label">{{ statusType(scope.record.is_vaild).text }}</div>
              </a-space>
            </template>
            <template v-if="scope.column.key === 'app_name'">
              <div>
                <span>{{ scope.record.app_name || '--' }}</span>
              </div>
            </template>
            <template v-if="scope.column.key === 'createdAt'">
              <div>{{ secToTime(scope.record.createdAt) }}</div>
            </template>

            <template v-if="scope.column.key === 'action'">
              <!--                <a-button-->
              <!--                  v-auth="['shopWenDaHHUifu']"-->
              <!--                  type="link"-->
              <!--                  size="small"-->
              <!--                  class="cu_po"-->
              <!--                  @click="handleActions('recover', scope.record)"-->
              <!--                  v-if="scope.record.is_resp === 0"-->
              <!--                  >回复问答</a-button-->
              <!--                >-->
              <!-- v-auth="['shopWenDaYes']" -->
              <div class="flex flex-col">
                <a-button
                  v-auth="['shopWenDaYes']"
                  type="link"
                  size="small"
                  @click="handleActions('pass', scope.record)"
                  class="cu_po pa-0! ma-0! text-left"
                  v-if="[-1, 2].includes(scope.record.is_vaild)"
                  >审核通过</a-button
                >
                <!-- v-auth="['shopWenDaNo']" -->
                <a-button
                  v-auth="['shopWenDaNo']"
                  type="link"
                  class="cu_po pa-0! ma-0! text-left"
                  @click="handleActions('noPass', scope.record)"
                  v-if="[-1, 1].includes(scope.record.is_vaild)"
                  >审核不通过</a-button
                >
              </div>
            </template>
          </template>
        </TableZebraCrossing>

        <a-modal
          v-model:open="state.modalConfig.isVisible"
          :title="state.modalConfig.title"
          centered
          :width="state.modalConfig.width"
          :closable="false"
          @cancel="modalCancel"
          @ok="modalOk"
        >
          <div v-if="['pass'].includes(state.modalConfig.warpKey)">确定要将该问答设为审核通过吗？</div>
          <div v-if="['noPass'].includes(state.modalConfig.warpKey)">确定要将该问答设为审核不通过吗？</div>
          <Recover v-if="['recover'].includes(state.modalConfig.warpKey)" v-model:resp="state.modalConfig.form.resp" />
          <FapNum v-if="['fapNum'].includes(state.modalConfig.warpKey)" :id="state.id" />
        </a-modal>
      </a-space>
    </template>
  </CardBaseLayout>
</template>
<script setup lang="tsx">
  import Recover from './components/Recover.vue'
  import { onMounted, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  import { message } from 'ant-design-vue'
  import FapNum from './components/FapNum.vue'
  import { get_question_list, post_question_update, post_question_resp } from './index.api'
  import { SEARCH_CONFIG_DATA, columns, statusType, modalConfigTitle } from './data'
  import { secToTime } from '@/utils'
  import { getShopList, getWechatConfAllList } from '@/views/shop/order/list/index.api'
  const router = useRouter()
  const state = reactive({
    id: '',
    initParams: {
      page: 1,
      page_size: 10,
      type: undefined,
      status: undefined,
      product: undefined,
      issue_time: undefined
    },
    searchConfig: SEARCH_CONFIG_DATA,
    tableConfig: {
      bordered: true,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 1200
      },
      dataSource: [],
      columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    },
    modalConfig: {
      isVisible: false,
      title: '',
      warpKey: '',
      data: '',
      width: '500',
      form: {
        resp: ''
      }
    }
  })
  // 搜索
  const changeValue = (data) => {
    state.initParams = { ...state.initParams, ...data.formData }
    if (data.formData && data.formData.issue_time && data.formData.issue_time[0] && data.formData.issue_time[1]) {
      state.initParams.issue_time = data.formData.issue_time.join('_')
    } else {
      state.initParams.issue_time = undefined
    }
    initPageData(state.initParams)
  }

  onMounted(() => {
    initPageData(state.initParams)
    initGetWechatConfAllList()
    initGetShopList()
  })
  const initGetWechatConfAllList = async () => {
    const result = await getWechatConfAllList({ page_size: 1000 })
    if (result.code === 0) {
      state.searchConfig.data[5].props.options = (result.data.list || []).map((v) => {
        return {
          value: v.app_id,
          label: v.app_name
        }
      })
    }
  }
  const initGetShopList = async () => {
    const result = await getShopList({ page_size: 9999 })
    if (result.code === 0) {
      if (result?.data?.list?.length) {
        state.searchConfig.data[4].props.options = result.data.list.map((item) => {
          return {
            value: item.shop_info?.id || '',
            label: item.shop_info?.name || ''
          }
        })
      }
    }
  }
  const initPageData = async (data) => {
    try {
      state.tableConfig.loading = true
      const result = await get_question_list(data)
      if (result.code === 0) {
        if (result.data) {
          state.tableConfig.dataSource = result.data.list
          state.tableConfig.pagination.total = result.data.total_num
          state.tableConfig.pagination.current = result.data.page || 1
        } else {
          state.tableConfig.dataSource = []
          state.tableConfig.pagination.total = 0
        }
      }
    } catch (error) {
    } finally {
      state.tableConfig.loading = false
    }
  }

  const pageChange = (pagination) => {
    state.initParams.page = pagination.current
    state.initParams.page_size = pagination.pageSize
    state.tableConfig.pagination.pageSize = pagination.pageSize
    initPageData(state.initParams)
  }
  // 集中处理 操作事件
  const handleActions = (type, data) => {
    state.id = data.id
    state.modalConfig.isVisible = true
    state.modalConfig.title = modalConfigTitle[type]
    state.modalConfig.warpKey = type
    state.modalConfig.data = data
  }
  const modalCancel = () => {
    state.modalConfig.isVisible = false
    setTimeout(() => {
      state.modalConfig.title = ''
      state.modalConfig.warpKey = ''
      state.modalConfig.data = ''
      state.modalConfig.form.resp = ''
    }, 200)
  }
  const modalOk = async () => {
    let result = null
    if (['pass'].includes(state.modalConfig.warpKey)) {
      result = await post_question_update({ id: state.modalConfig.data.id, is_vaild: 1 })
    }
    if (['noPass'].includes(state.modalConfig.warpKey)) {
      result = await post_question_update({ id: state.modalConfig.data.id, is_vaild: 2 })
    }
    if (['recover'].includes(state.modalConfig.warpKey)) {
      result = await post_question_resp({ id: state.modalConfig.data.id, resp: state.modalConfig.form.resp })
    }
    if (result?.code === 0) {
      message.success(result.msg)
      initPageData(state.initParams)
    }
    modalCancel()
  }
</script>
<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn.scss';
  img {
    display: block;
    width: 100%;
    height: 100%;
  }
  .question {
    word-break: break-word;
    @include text_overflow(3);
  }
  .status_dot {
    @include set_border_radius(50%);
    @include set_node_whb(10px, 10px);
  }
</style>
