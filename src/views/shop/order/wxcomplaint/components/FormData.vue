<template>
  <a-form ref="formRef" name="dynamic_form_item" :model="data" v-bind="formItemLayoutWithOutLabel">
    <a-form-item
      name="name"
      label="规则名称"
      :rules="{
        required: true,
        message: '请输入规则名称'
      }"
    >
      <a-input v-model:value="data.name" placeholder="请输入规则名称" autocomplete="off" :maxLength="20" @blur="() => {
        !data.name.trim() && (data.name = '')
      }" />
    </a-form-item>
    <a-form-item name="desc" label="规则描述">
      <a-input v-model:value="data.desc" placeholder="请输入规则描述" autocomplete="off" :maxLength="20" @blur="() => {
        !data.desc.trim() && (data.desc = '')
      }" />
    </a-form-item>

    <a-form-item
      v-for="(item, index) in data.keywords"
      :key="index"
      v-bind="index === 0 ? formItemLayout : formItemLayoutOffest"
      :label="index === 0 ? '关键词' : ''"
      :rules="{
        required: true,
        message: ''
      }"
    >
      <a-space size="small">
        <a-form-item :name="['keywords', index, 'Type']">
          <a-select
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="item.Type"
            style="width: 90px"
          >
            <a-select-option :value="1">全包含</a-select-option>
            <a-select-option :value="2">半包含</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          :name="['keywords', index, 'Keywords']"
          :rules="[
            {
              validator: (_: any, value: any, callback: Function) => {
                if (!value) {
                  callback('请输入关键词')
                } else if (item.Type==1 && value.length<10) {
                  callback('请至少输入十个字')
                } else {
                  const allKeywords = data.keywords.map((item:any) => item.Keywords);
                  const hasDuplicates = new Set(allKeywords).size !== allKeywords.length;
                  if (hasDuplicates) {
                    callback('存在重复的关键词，请检查');
                  } else {
                    callback();
                  }
                }
              },
              trigger: ['change', 'blur']
            }
          ]"
        >
          <a-input
            v-model:value="item.Keywords"
            oninput="value=value.replace(/[, ]/g,'')"
            onkeyup="value=value.replace(/[, ]/g,'')"
            maxlength="20"
            placeholder="请输入关键词"
          />
        </a-form-item>
        <a-form-item>
          <MinusOutlined
            v-if="(data.keywords.length > 1 && data.keywords.length - 1 !== index) || data.keywords.length >= 10"
            @click="removeDomain(index)"
          />
          <PlusOutlined @click="addDomain" v-else />
        </a-form-item>
      </a-space>
    </a-form-item>
    <a-form-item
      label="回复内容"
      name="reply_content"
      :rules="{
        required: true,
        message: '请输入回复内容'
      }"
    >
      <a-input v-model:value="data.reply_content" :maxLength="200" placeholder="请输入回复内容" autocomplete="off" @blur="() => {
        !data.reply_content.trim() && (data.reply_content = '')
      }" />
    </a-form-item>
    <a-form-item label="留言图片：" name="images" label-width="50">
      <div class="flex mt-10px">
        <Upload
          :modelValue="images"
          :max="4"
          use="complaint"
          multiple
          accept=".JPG,.JPEG,.PNG,.jpg,.jpeg,.png,.bmp,.BMP"
          :size="2"
        ></Upload>
      </div>
      <div class="tip font-300">媒体图片只支持jpg、jpeg、png、bmp格式</div>
      <div class="tip font-300">最多上传4张图片，文件大小不能超过2M</div>
    </a-form-item>
    <a-form-item
      label="回复后动作"
      name="rules"
      :rules="{
        required: true,
        message: '请选择回复后动作'
      }"
    >
      <div class="mt-6px">
        <a-radio-group v-model:value="rulesCurrent" @change="rulesChange">
          <a-radio :value="1">等待人工介入</a-radio>
          <a-radio :value="3">
            <div class="flex flex-items-center">
              <span>退款</span>
              <a-tooltip>
                <template #title>
                  <div>
                    <div>1.退款只有在订单待发货状态下触发</div>
                    <div>2.退款后该条投诉自动处理完成</div>
                  </div>
                </template>
                <ExclamationCircleOutlined class="m-l-8px c-#faad14" />
              </a-tooltip>
            </div>
          </a-radio>
          <a-radio :value="2" class="mt-6px"
            v-if="['1','3'].includes(route.query.channels)"
          >
            <div class="flex flex-items-center">
              <div class="flex flex-items-center">
                <span>自动回复后</span>
                <a-input-number class="mx-4px" v-model:value="autoCompleteTime" :min="0" :max="72" @change="autoCompleteTimeChange" @blur="() => {
                  !autoCompleteTime && (autoCompleteTime = 0)
                }" :precision="0"></a-input-number>
                <span>小时后无新消息，将进行自动触发处理完成</span>
              </div>
              <a-tooltip>
                <template #title>输入“0”时，则回复后会立即进行处理完成</template>
                <ExclamationCircleOutlined class="m-l-8px c-#faad14" />
              </a-tooltip>
            </div>
          </a-radio>
        </a-radio-group>
      </div>
    </a-form-item>
  </a-form>
</template>
<script lang="ts" setup>
  const props = defineProps(['data'])
  const emits = defineEmits(['addItem'])
  import { ref, watch } from 'vue'
  import { MinusOutlined, PlusOutlined, ExclamationCircleOutlined, CloseCircleFilled } from '@ant-design/icons-vue'
  import { useRoute } from 'vue-router'
  const route = useRoute()
  const formRef = ref()
  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 }
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 }
    }
  }
  const formItemLayoutOffest = {
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18, offset: 6 }
    }
  }
  const formItemLayoutWithOutLabel = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 }
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 }
    }
  }

  const removeDomain = (item) => {
    emits('remove', item)
  }
  const addDomain = () => {
    emits('addItem')
  }
  const validateFields = async () => {
    return await formRef.value.validateFields()
  }
  const images = ref(props.data.images ? props.data.images.split(',').map((item:string) => {
    return { url: item }
  }) : [])
  watch(() => images.value.length, (v) => {
    props.data.images = images.value.map((v:any) => v?.url).join(',')
  })
  const channelsArr = ref<number[]>(props.data?.channels ? props.data?.channels.split(',').map((v:string) => Number(v)) : [])
  const checkboxIndex = ref<string>('')
  const rulesCurrent = ref<number>(props.data?.rules?.[2]?.reply_after || 0)
  const autoCompleteTime = ref<number>(0)
  function channelsArrChange(v:number[]) {
    props.data.channels = v.join(',')
    if (v.length === 1) {
      checkboxIndex.value = `${v[0]}`
    }
    if (checkboxIndex.value && props.data.channels.indexOf(checkboxIndex.value) === -1) {
      checkboxIndex.value = ''
    }
    if (checkboxIndex.value) {
      rulesCurrent.value = props.data.rules?.[checkboxIndex.value]?.reply_after || 1
      rulesChange({ target: { value: rulesCurrent.value } })
    }
  }
  function checkboxText(v:number) {
    checkboxIndex.value = `${v}`
    if (!props.data?.channels || props.data?.channels?.indexOf(checkboxIndex.value) === -1) {
      channelsArr.value.push(v)
      props.data.channels = channelsArr.value.join(',')
    } else {
    }
    rulesCurrent.value = props.data.rules?.[checkboxIndex.value]?.reply_after || 1
    rulesChange({ target: { value: rulesCurrent.value } })
  }
  checkboxText(props.data?.channels || route.query.channels)
  function rulesChange(v:any) {
    if (props.data.rules?.[checkboxIndex.value]) {
      props.data.rules[checkboxIndex.value].reply_after = v.target.value
    } else {
      props.data.rules = {
        ...props.data.rules,
        [checkboxIndex.value]: {
          reply_after: v.target.value
        }
      }
    }
    if (['1','3'].includes(checkboxIndex.value)) {
      if (props.data.rules?.[checkboxIndex.value]?.auto_complete_time) {
        autoCompleteTime.value = props.data.rules?.[checkboxIndex.value]?.auto_complete_time
      } else {
        autoCompleteTime.value = 0
        props.data.rules[checkboxIndex.value].auto_complete_time = 0
      }
    }
  }
  function autoCompleteTimeChange(v:number) {
    props.data.rules[checkboxIndex.value].auto_complete_time = v
  }
  defineExpose({
    validateFields
  })
</script>
<style scoped lang="scss">
  :deep(.ant-form-item) {
    margin-bottom: 5px;
  }
  .img_item {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 4px;
    .del_icon {
      position: absolute;
      display: block;
      top: -8px;
      right: -8px;
      cursor: pointer;
    }
  }
</style>
