<template>
  <a-card
    :bodyStyle="{
      paddingTop: '0px'
    }"
    :headStyle="{ border: 'none' }"
  >
    <template #title>
      <a-breadcrumb>
        <!-- <a-breadcrumb-item
          ><router-link :to="{ name: 'ShopComplaint', query: { status: 2 } }">投诉查询</router-link></a-breadcrumb-item
        > -->
        <a-breadcrumb-item>投诉详情</a-breadcrumb-item>
      </a-breadcrumb>
    </template>
    <a-space class="w-full" direction="vertical" size="large" v-if="state.data">
      <a-card>
        <a-space class="w-full flex flex-justify-between">
          <div class="status-title">{{ complaintType(state.data.status) }}</div>
          <a-button
            v-if="
              state.data.status == 1 ||
              (state.data.status == 5 && state.data.shop_refuse == 0 && state.data.boss_refuse == 0)
            "
            type="primary"
            @click="handleActions('handle')"
          >
            填写处理结果
          </a-button>
        </a-space>
        <a-space class="w-full flex flex-justify-between">
          <div class="steps flex_align_center">
            <div class="flex_align_center">
              <span class="flex_align_center steps_item">
                <span class="font_round_active">1</span>
                用户发起投诉
              </span>
              <img class="separator" :src="requireImg('sales/right.png')" alt="" />
              <span class="flex_align_center steps_item" :style="{ color: state.data.status < 1 ? '#85878A' : '' }">
                <span :class="[state.data.status >= 1 ? 'font_round_active' : 'font_round']">2</span>
                待处理
              </span>
              <img class="separator" :src="requireImg('sales/right.png')" alt="" />
              <span class="flex_align_center steps_item" :style="{ color: state.data.status < 2 ? '#85878A' : '' }">
                <span :class="[state.data.status >= 2 ? 'font_round_active' : 'font_round']">3</span>
                处理中
              </span>
              <img class="separator" :src="requireImg('sales/right.png')" alt="" v-if="state.data.shop_refuse == 1" />
              <span
                class="flex_align_center steps_item"
                :style="{ color: state.data.status < 3 ? '#85878A' : '' }"
                v-if="state.data.shop_refuse == 1"
              >
                <span
                  :class="[state.data.status >= 3 && state.data.shop_refuse == 1 ? 'font_round_active' : 'font_round']"
                  >4</span
                >
                拒绝和解
              </span>
              <img class="separator" :src="requireImg('sales/right.png')" alt="" />
              <span class="flex_align_center steps_item" :style="{ color: state.data.status < 3 ? '#85878A' : '' }">
                <span
                  :class="[
                    state.data.status == 3 ||
                    (state.data.status == 5 && state.data.shop_refuse == 1 && state.data.boss_refuse == 1) ||
                    state.data.boss_refuse == 1
                      ? 'font_round_active'
                      : 'font_round'
                  ]"
                >
                  <span>{{ state.data.shop_refuse == 1 ? 5 : 4 }}</span>
                </span>
                处理完成
              </span>
            </div>
          </div>
        </a-space>
      </a-card>
      <a-row :gutter="[50, 20]">
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-card title="投诉信息" :headStyle="{ border: 'none' }">
            <a-descriptions :column="1">
              <a-descriptions-item label="订单编号">{{ state.data.order_sn }}</a-descriptions-item>
              <a-descriptions-item label="商品名称">{{ state.data.product_name }}</a-descriptions-item>
              <a-descriptions-item label="实付金额">¥{{ state.data.total_money }}</a-descriptions-item>
              <a-descriptions-item label="支付时间">{{ state.data.pay_time }}</a-descriptions-item>
              <a-descriptions-item label="买家手机号">
                {{ state.data.phone_number }}
                <span v-auth="['aplateLookPhone']">
                  <DataEncipher
                    v-model:data="state.data"
                    showKey="checkPhone"
                    goalkey="phone_number"
                    :type="4"
                    argKey="id"
                  />
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="收货人">{{ state.data.receive_name }}</a-descriptions-item>
              <a-descriptions-item label="配送方式">
                <span>{{ state.data?.order_spread?.express_name || '--' }}</span>
                <span class="ml-10px">{{ state.data?.order_spread?.express_number || '--' }}</span>
                <a-button
                  type="link"
                  class="ml-10px p-0! h-auto"
                  v-if="state.data?.order_spread?.express_name || state.data?.order_spread?.express_number"
                  @click="handleActions('lookWl')"
                  >查看物流</a-button
                >
              </a-descriptions-item>
              <a-descriptions-item label="发货时间">
                {{ state.data?.order_info?.deliver_time || '--' }}</a-descriptions-item
              >

              <a-descriptions-item label="联系方式"
                >{{ state.data.receive_phone }}
                <span v-auth="['aplateLookPhone']">
                  <DataEncipher
                    v-model:data="state.data"
                    showKey="showPhone"
                    goalkey="receive_phone"
                    :type="1"
                    argKey="order_id"
                  />
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="售后编号">{{ state.data.refund_sn || '--' }}</a-descriptions-item>
              <a-descriptions-item label="售后状态">{{
                saleStatusType(state.data?.after_sale_status) || '--'
              }}</a-descriptions-item>
              <a-descriptions-item label="退款金额">￥{{ centsToYuan(state.data.return_money) }}</a-descriptions-item>
              <a-descriptions-item label="反馈问题">{{ state.data.content }}</a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
          <a-card title="处理记录" :headStyle="{ border: 'none' }">
            <a-timeline>
              <a-timeline-item v-for="item in state.logList">
                <div class="flex">
                  <div>
                    <span v-if="state.data.type != 1" :class="'status-type-' + typeRenderName(item.type).type">
                      {{ typeRenderName(item.type).name }}
                    </span>
                    <span v-else :class="'status-type-' + platformTypeName(item.platform_type).type">
                      {{ platformTypeName(item.platform_type).name }}
                    </span>
                    <span
                      class="font-bold"
                      v-if="
                        typeRenderName(item.type).type === 'seller' ||
                        platformTypeName(item.platform_type).type === 'seller'
                      "
                    >
                      【{{ item.admin_name }}】
                    </span>
                  </div>
                  <div class="title ml-10px">
                    <span v-if="[1].includes(state.data.type)">
                      <span v-if="item.platform_type == 3 && state.data.boss_refuse == 1">平台拒绝和解</span>
                      <span v-else-if="item.platform_type == 3 && state.data.boss_refuse == 0">平台同意和解</span>
                      <!-- <span v-else>{{ statusType(item) }}</span> -->
                    </span>
                    <span v-else>
                      <span v-if="[31, 32].includes(item.type)">
                        <span v-if="item.blameResult == 0"> 平台已核实此投诉为商家责任，待上传处理凭证 </span>
                        <span v-else> 平台已核实此投诉为商家责任，待用户退货中 </span>
                      </span>
                      <span v-else class="default">{{ miniStatus(item) }}</span>
                    </span>
                  </div>
                </div>

                <div class="mt-10px" v-if="item.content">{{ item.content }}</div>
                <div class="mt-10px" v-if="item.media_id_list">
                  <a-image-preview-group>
                    <a-image v-for="it in item.media_id_list.split(',')" :width="80" :src="it" />
                  </a-image-preview-group>
                </div>
                <div class="mt-10px">{{ item.created_at }}</div>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>
      </a-row>
    </a-space>
    <a-modal
      v-model:open="state.modalConfigData.open"
      :title="state.modalConfigData.title"
      @cancel="modalCancel"
      centered
      @ok="handleOk"
    >
      <Editor v-if="['handle'].includes(state.modalConfigData.type)" v-model:data="state.editorData" ref="formRef" />
      <look-wl v-if="state.modalConfigData.type === 'lookWl'" :data="state.modalConfigData.data"></look-wl>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
  import {
    get_minicomplaint_info,
    get_minicomplaint_log,
    get_minicomplaint_mini_type,
    get_minicomplaint_status,
    post_minicomplaint_update,
    saleStatus
  } from './index.api'
  import { onMounted, reactive, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import useShopData from '../../order/complaint/shop_data'
  import { modalConfigData, modalConfigTitle, editorData } from './data'
  import Editor from './components/Editor.vue'
  import LookWl from './components/LookWl.vue'
  import { EyeOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { requireImg, centsToYuan } from '@/utils'
  const { complaintType } = useShopData()
  const route = useRoute()
  const formRef = ref(null)
  const state = reactive({
    data: null,
    logList: [],
    miniData: {},
    complaintStatus: {},
    initQuery: {
      id: ''
    },
    modalConfigData,
    editorData: JSON.parse(JSON.stringify(editorData)),
    saleStatusInfo: {}
  })
  onMounted(() => {
    initMiniType()
    initStatus()
    initData()
    initLog()
  })
  const typeRenderName = (type: any) => {
    // {{
    //   [1, 2, 7, 11, 12, 14, 102, 104, 108, 111].includes(item.type)
    //     ? '买家'
    //     : [3, 8, 16, 101, 107, 109, 110, 112, 203, 204, 208].includes(item.type)
    //       ? '商家'
    //       : '平台'
    // }}
    let nameObj = {
      name: '平台',
      type: 'platform'
    }
    if ([1, 2, 7, 11, 12, 14, 102, 104, 108, 111].includes(type)) {
      nameObj.name = '买家'
      nameObj.type = 'buyer'
    } else if ([3, 8, 16, 101, 107, 109, 110, 112, 203, 204, 208].includes(type)) {
      nameObj.name = '商家'
      nameObj.type = 'seller'
    }
    return nameObj
  }
  const platformTypeName = (type: number) => {
    // {{ item.platform_type == 1 ? '买家' : item.platform_type == 2 ? '商家' : '平台' }}
    let nameObj = {
      name: '平台',
      type: 'platform'
    }
    if (type === 1) {
      nameObj.name = '买家'
      nameObj.type = 'buyer'
    } else if (type === 2) {
      nameObj.name = '商家'
      nameObj.type = 'seller'
    }
    return nameObj
  }
  const initData = async () => {
    const { id } = route.query
    state.initQuery.id = id

    const result = await get_minicomplaint_info(state.initQuery)
    if (result.code === 0) {
      result.data.checkPhone = 0
      result.data.showPhone = 0
      state.data = result.data
    }
  }
  const initLog = async () => {
    const { id } = route.query
    state.initQuery.id = id
    const result = await get_minicomplaint_log(state.initQuery)

    if (result.code === 0) {
      state.logList = result.data?.list || []
    }
  }
  const initStatus = async () => {
    const result = await get_minicomplaint_status()
    state.complaintStatus = result.data?.list || {}
    for (let i in state.complaintStatus) {
      if (state.complaintStatus[i] == '待处理') {
        state.complaintStatus[i] = '买家提交投诉'
      }
      if (state.complaintStatus[i] == '处理中') {
        state.complaintStatus[i] = '商家处理中'
      }
      if (state.complaintStatus[i] == '处理完成') {
        state.complaintStatus[i] = '商家同意和解'
      }
      if (state.complaintStatus[i] == '拒绝和解') {
        state.complaintStatus[i] = '商家拒绝和解'
      }
    }
  }
  const initMiniType = async () => {
    const result = await get_minicomplaint_mini_type()
    if (result.code === 0) {
      state.miniData = result.data?.list || {}
    }
  }
  const statusType = (item) => {
    let str = state.complaintStatus[item.type]
    if (item.admin_name) {
      str = str + '【' + item.admin_name + '】'
    }
    return str
  }
  const miniStatus = (item) => {
    let str = state.miniData[item.type]
    // if (item.admin_name) {
    //   str = str + '【' + item.admin_name + '】'
    // }
    return str
  }
  const handleActions = (type) => {
    state.modalConfigData.open = true
    state.modalConfigData.type = type
    state.modalConfigData.title = modalConfigTitle[type]
    if (type === 'lookWl') {
      state.modalConfigData.data = {
        ...state.data?.order_spread,
        order_id: state.data?.order_id,
        pay_time: state.data?.order_info?.pay_time,
        created_at: state.data?.order_info?.created_at
      }
    }
  }
  const handleOk = async () => {
    let result = null
    if (['handle'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      if (values) {
        let arr = []
        if (values.media_id_list.length) {
          values.media_id_list.forEach((item) => arr.push(item.url))
        }
        result = await post_minicomplaint_update({
          id: +state.initQuery.id,
          refund_sn: state.data.refund_sn,
          media_id_list: arr.length > 0 ? arr.join(',') : undefined,
          content: values.content,
          status: values.status
        })
        if (result.code === 0) {
          message.success(result.msg || '操作成功')
          initData()
          initLog()
        }
      }
    }

    modalCancel()
  }
  const modalCancel = () => {
    state.modalConfigData.open = false
    setTimeout(() => {
      state.modalConfigData.data = ''
      state.modalConfigData.type = ''
      state.modalConfigData.title = ''
      state.editorData.status = 1
      state.editorData.content = ''
      state.editorData.media_id_list = []
    }, 300)
  }
  // 获取售后状态
  const getSaleStatus = async () => {
    try {
      let res = await saleStatus()
      state.saleStatusInfo = res.data.list || {}
    } catch (error) {}
  }
  getSaleStatus()
  const saleStatusType = (type: any) => {
    return state.saleStatusInfo[type]
  }
</script>

<style scoped lang="scss">
  @import '../../../../assets/css/mixin_scss_fn';
  .status-title {
    @include set_font_config(--font-size-huge, --text-color-base);
    font-weight: 600;
  }
  .title {
    @include set_font_config(--font-size-small, --text-color-base);
    font-weight: 600;
  }
  .status-type-buyer {
    color: #647dff;
    border-radius: 5px;
    padding: 1px 5px;
    border: 1px solid #647dff;
  }
  .admin-name {
    font: bold;
  }
  .status-type-seller {
    padding: 1px 5px;
    border: 1px solid #ffccc7;
    border-radius: 5px;
    color: rgba(248, 113, 113, 1);
  }
  .status-type-platform {
    padding: 1px 5px;
    border-radius: 5px;
    padding: 1px 5px;
    border: 1px solid #dcdde1;
  }
  .font_round_active {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #1677ff;
    color: #fff;
    text-align: center;
    line-height: 18px;
    margin-right: 5px;
  }

  .font_round {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #fff;
    color: #85878a;
    border: 1px solid #dbe0ea;
    text-align: center;
    line-height: 18px;
    margin-right: 5px;
  }
  .steps {
    font-size: 14px;
    color: #080f1e;
    margin-top: 24px;
    font-weight: 400;
    justify-content: space-between;

    .separator {
      display: inline-block;
      margin: 0 15px 0 10px;
    }

    &_item {
      .img {
        width: 18px;
        height: 18px;
        margin-right: 5px;
      }
    }
  }
</style>
