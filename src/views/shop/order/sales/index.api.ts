import http from '@/utils/request'

export const get_after_sale_list = (data) => {
  return http('get', `/manage/after-sale/list`, data)
}

export const get_after_sale_count = (data) => {
  return http('get', `/manage/after-sale/count`, data)
}

// 获取物流信息
export const get_order_express = (data) => {
  return http('get', `/manage/order/express`, data)
}
// 批量统
export const post_after_sale_batch_return_apply = (data) => {
  return http('post', `/shop-boss/after_sale/batch_return_apply`, data)
}
export const post_after_sale_edit = (data) => {
  return http('post', `/manage/after-sale/edit`, data)
}

export const get_after_sale_status = (data) => {
  return http('get', `/manage/after-sale/status`, data)
}

export const post_exportdata_create = (data) => {
  return http('post', `/public/export-data/create`, data)
}

export const saleStatus = (data) => {
  return http('get', `/manage/after-sale/status`, data)
}

export const get_order_get_express = (data) => {
  return http('get', `/shop-boss/order/get_express`, data)
}

export const post_after_sale_address_update = (data) => {
  return http('post', `/manage/after-sale/address-update`, data)
}

export const post_after_sale_address_confirm = (data) => {
  return http('post', `/shop-boss/after_sale/confirm`, data)
}

/**
 * 售后原因
 * https://www.apifox.cn/link/project/2014698/apis/api-63125218
 */
export const refundType = (data: any) => {
  return http('get', `/manage/after-sale/refund-type`, data)
}

// 获取店铺下拉
export const getShopList = (data) => {
  return http('get', `/manage/shop/list`, data)
}
// 获取小程序
export const getWechatConfAllList = (data) => {
  return http('get', `/manage/wechat-conf/all-list`, data)
}
// 获取售后详情
export const get_after_sale_info = (data) => {
  return http('get', `/manage/after-sale/info`, data)
}
/**
 * 售后投诉数量统计
 *
 */
export const complaint_num = (data) => {
  return http('get', `/manage/after_sale/complaint_num`, data)
}
//出现指引后调用
export const helpAddApi = (data: any) => {
  return http('post', `/public/user-help/add`, data)
}

/**
 * 线下退款
 *
 */
export const blow_refund = (data: any) => {
  return http('post', `/public/after-sale/blow-refund`, data)
}
