<template>
  <div>
    <DesTablePage :pagination="state.paginationConfig" @changePages="changePages" class="common_card_wrapper">
      <template #title>
        <div>售后列表</div>
      </template>
      <template #extra>
        <a-button
          v-auth="['shopSaleNewSale']"
          type="primary"
          @click="router.push({ path: '/shop/order/add_sale_form' })"
        >
          新建售后单
        </a-button>
        <a-button v-auth="['shopSaleExport']" @click="downloadSales" type="primary"> 导出 </a-button>
      </template>
      <template #search>
        <SearchBaseLayout
          ref="searchFormDataRef"
          :data="state.searchConfig.data"
          @changeValue="changeValue"
          :actions="state.searchConfig.options"
        />
      </template>
      <template #action>
        <div class="flex-y-center justify-between">
          <a-radio-group
            :value="state.actionConfig.value"
            @change="changeBtnType({ value: $event.target.value })"
            :class="['mt16px', state.actionConfig.value === 'time_out' && 'radioGroup']"
          >
            <a-radio-button
              :class="item.value === 'time_out' && 'warn'"
              v-for="item in state.actionConfig.list"
              :key="item.value"
              :value="item.value"
            >
              <a-space>
                <span> {{ item.label }} ({{ item.total }})</span>
                <a-tooltip v-if="item.value === 'time_out'">
                  <template #title>{{ '用户申请售后在规定时间内未进行处理，系统将自动处理售后。' }}</template>
                  <QuestionCircleOutlined class="m-l-4px font-size-14px" />
                </a-tooltip>
              </a-space>
            </a-radio-button>
          </a-radio-group>
          <div class="flex-y-center">
            <a-checkbox
              :class="['mt16px mr-10px', state.actionConfig.value === 'time_out' && 'radioGroup']"
              v-model:checked="state.refund_money_type"
              @change="changeOffline_refund"
            >
              <span class="w-60px inline-block">线下退款</span>
            </a-checkbox>
            <a-checkbox
              :class="['mt16px', state.actionConfig.value === 'time_out' && 'radioGroup']"
              v-model:checked="state.review_status"
              @change="changeReview_status"
            >
              <span class="w-90px inline-block">平台介入</span>
            </a-checkbox>
          </div>
        </div>

        <!-- <ButtonRadioGroup :data="state.actionConfig" @changeValue="changeBtnType" /> -->
      </template>
      <template #tableWarp>
        <DescTableLayout :data="state.tableConfig" class="other_w">
          <template #action>
            <div class="action-warp flex flex-justify-between">
              <!--            <a-space align="center">-->
              <!--              <a-checkbox-->
              <!--                :indeterminate="state.indeterminate"-->
              <!--                :checked="state.allSelectTable.length && state.selectTable.length === state.allSelectTable.length"-->
              <!--                @change="onCheckAllChange"-->
              <!--              >-->
              <!--                当页全选-->
              <!--              </a-checkbox>-->
              <!--              <span>批量处理</span>-->
              <!--              <a-button v-auth="['shopSalePiLianYes']" @click="handleActions('refund')">批量同意仅退款</a-button>-->
              <!--              <a-button v-auth="['shopSalePiLianTuihuoTuiK']" @click="handleActions('refundReturn')"-->
              <!--                >批量同意退货退款</a-button-->
              <!--              >-->
              <!--            </a-space>-->
              <!--            <a-space align="center">-->
              <!--              <a-button type="link" @click="handleSort">-->
              <!--                {{ state.initParams.apply_time_sort === 'desc' ? '最近申请排序' : '临近逾期排序' }} <DownOutlined-->
              <!--              /></a-button>-->
              <!--            </a-space>-->
            </div>
          </template>
          <template #desc="{ data }">
            <div class="desc-table-layout-list-item-desc flex flex-justify-between">
              <a-space align="center">
                <!--              <span-->
                <!--                ><a-checkbox-->
                <!--                  :checked="state.selectTable.includes(data.id)"-->
                <!--                  @change="(checkedValue) => tabelItemSelect(checkedValue, data)"-->
                <!--                >-->
                <!--                  {{ data.app_name }}-->
                <!--                </a-checkbox></span-->
                <!--              >-->
                <span>售后编号: {{ data.refund_sn }}</span>
                <span>订单编号: {{ data.order_sn }}</span>
                <div class="item flex flex_align_center">
                  <span class="mini_program_logo" v-if="data.order_type === 4">
                    <img src="@/assets/images/order/werxin_shop.png" alt="微信小店" />
                  </span>
                  <span v-if="data.order_type == 4" class="mr-4px">{{ data.wechat_shop_name || '--' }}</span>

                  <span class="mini_program_logo" v-if="data.order_type !== 4">
                    <img
                      v-if="data.app_name.indexOf('(H5)') > -1"
                      src="@/assets/images/order/icon_h5.png"
                      alt="小程序来源"
                    />
                    <img v-else src="@/assets/images/order/mini_program.png" alt="小程序来源" />
                  </span>
                  <span v-if="data.order_type !== 4"> {{ data.app_name || '--' }}</span>
                </div>
                <span class="flex flex-items-center">
                  <span>来源: </span>

                  <a-tooltip placement="topLeft">
                    <template #title>{{ channelType(data.channel_type) }}</template>
                    <img
                      v-if="[1, 4, 5, 6, 7, 8].includes(data.channel_type)"
                      class="icon m-l-4px"
                      :src="iconType(data.channel_type)"
                      alt=""
                    />
                  </a-tooltip>
                  <span>{{ ordersource(data.order_source) }}</span>
                  <span v-if="![1, 4].includes(data.order_source)"
                    >（计划ID：{{ data.ad_group_id == 0 ? '--' : data.ad_group_id }}
                    <span style="width: 5px; display: inline-block"></span> 创意ID：{{ data.creative_id || '--' }}
                    <!-- <a-tooltip>
                        <template #title
                          >广告账户授权异常，为避免报表数据错误，请点击
                          <span class="cursor-pointer c-#97DEFF" @click="getAuthUrl">去授权</span></template
                        >
                        <ExclamationCircleOutlined
                          v-if="data.is_authorized == 1 && data.channel_type == 1"
                          class="c-#e63030 ml-10px mr-5px"
                        /> </a-tooltip> -->
                    ）</span
                  >
                </span>
              </a-space>
              <div>
                <span>负责人: {{ data.admin_name || '--' }}</span>
                <span class="ml-10px">支付时间: {{ data.pay_time || '--' }}</span>
              </div>
              <!--            <div>-->
              <!--              <div class="order_status">-->
              <!--                <span-->
              <!--                  :style="{ color: [6, 7, 13].includes(data.after_sale_status) ? '' : '#E77316' }"-->
              <!--                  style="font-weight: 500"-->
              <!--                >-->
              <!--                  {{ salesStatusText(data.after_sale_status) }}-->
              <!--                </span>-->
              <!--              </div>-->
              <!--            </div>-->
            </div>
          </template>

          <template #bodyCell="{ scope, data, pindex }">
            <template v-if="scope.column.key === 'name'">
              <div class="flex w250px">
                <div class="cellos-item_img"><img :src="scope.record.image" alt="" /></div>
                <div class="flex" style="flex: 1; overflow: hidden">
                  <div class="flex-1 cellos-item-prod-warp">
                    <div class="cellos-item_title" @click="toPath(data)">
                      {{ scope.record.product_name }}
                    </div>
                    <div class="cellos-item_style">
                      <a-tooltip placement="topLeft">
                        <template #title>{{ scope.record.sku_name || '--' }}</template>
                        <span>{{ scope.record.sku_name || '--' }}</span>
                      </a-tooltip>
                    </div>
                    <div class="cellos-item_id">
                      <span>商品ID：</span> <span>{{ scope.record.product_code }}</span>
                    </div>
                    <span class="item-lable" v-if="scope.record.active_type_name && scope.record?.activity_type != 4">{{
                      scope.record.active_type_name
                    }}</span>
                    <span
                      class="border p-4px pt0 pb0 border-rd-2px font-size-12px border-color-#C4EEE2 color-#19A378 bg-#E8F6F2 mr8px"
                      v-show="[4].includes(scope.record?.activity_type)"
                    >
                      送礼物
                    </span>

                    <!-- <span
                    v-if="scope.record.order_type == 2"
                    class="border p-3px pt0 pb0 border-rd-2px border-color-#E6D9FF color-#7C49DC bg-#F8F5FF font-size-12px line-height-14px inline-block"
                  >
                    {{ '全球购' }}
                  </span> -->
                  </div>
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'price'">
              <div class="w120px">
                <div>退款：¥ {{ data.refund_money?.toFixed(2) || '0.00' }}</div>
                <div>实收：¥ {{ data.total_money?.toFixed(2) || '0.00' }}</div>
                <div v-if="data.transfer_money > 0">打款：¥ {{ data.transfer_money?.toFixed(2) || '0.00' }}</div>
              </div>
            </template>
            <template v-if="scope.column.key === 'shop_name'">
              <div class="w150px">{{ data.shop_name }}</div>
            </template>
            <template v-if="scope.column.key === 'address'">
              <div class="w195px">
                <div class="wx_info" style="display: flex; align-items: center">
                  <img
                    src="@/assets/images/order/wx_icon.png"
                    style="width: 14px; height: 14px; font-size: 14px; margin-right: 5px"
                  />
                  <span> {{ data.user_phone || '-' }} </span>
                </div>
                <div class="flex flex-items-center">
                  <span class="text_overflow max-w-80px">
                    {{ data.receive_name }}
                  </span>
                  <span class="pl-1">
                    {{ data.receive_phone || '--' }}
                    <span v-auth="['salesLookPhone']">
                      <DataEncipher
                        v-model:data="state.tableConfig.list[pindex]"
                        showKey="showPhone"
                        goalkey="receive_phone"
                        :type="3"
                        argKey="id"
                      />
                    </span>
                  </span>
                </div>
                <div>
                  <a-tooltip>
                    <template #title>{{ data.receive_address || '--' }}</template>
                    <p class="address_info">收货地址：{{ data.receive_address || '--' }}</p>
                  </a-tooltip>
                </div>
              </div>
            </template>

            <template v-if="scope.column.key === 'info'">
              <a-space class="w150px" direction="vertical">
                <div class=" " v-if="!data.deliver_time">未发货</div>
                <div class="colorE6" v-else @click="handleActions('deliverGoods', data)">
                  {{ data.user_name + '已发货>' }}
                </div>
                <div v-if="![1, 4].includes(data.after_sale_type)">
                  <div class=" " v-if="!data.logistics_number">未退货</div>
                  <div class="colorE6 line1 w150px" v-else @click="handleActions('returnGoods', data)">
                    {{ '已退货>' }}
                    <span
                      class="refund"
                      v-if="[3, 211, 301, 302, 304, 311, 406].includes(data.refund_logistics_status)"
                      >{{ '退货已签收' }}</span
                    >
                  </div>
                  <!-- 跨境订单 -->
                  <!-- <div class="colorE6">{{ data.bonded_manager_name }}</div> -->
                </div>
              </a-space>
            </template>
            <template v-if="scope.column.key === 'wuliu_info'">
              <div class="w180px">
                <a-row>
                  <a-col flex="60px">物流公司:</a-col>
                  <a-col flex="140px">{{ data.logistics_company }}</a-col>
                </a-row>
                <a-row>
                  <a-col flex="60px">物流单号:</a-col>
                  <a-col flex="140px">{{ data.logistics_number }}</a-col>
                </a-row>
              </div>
            </template>
            <template v-if="scope.column.key === 'sale_info'">
              <div>
                <div class="w160px">{{ data.msg || '--' }}</div>
                <a-tooltip placement="top" :title="data.reason">
                  <div class="w160px reason">{{ data.reason || '--' }}</div>
                </a-tooltip>

                <div v-if="data.images" class="flex reasonimg">
                  <img
                    class="image mr2"
                    v-for="(item, index) in data.images.split(',')"
                    :key="index"
                    :src="item"
                    alt=""
                  />
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'sale_status'">
              <a-space direction="vertical">
                <span class="sale_type" :id="pindex == 0 ? 'afterSale' : ''" :ref="pindex == 0 ? 'afterSale' : ''">
                  {{ salesStatus(data.after_sale_type) }}
                </span>
                <div class="flex-y-center">
                  <span style="font-size: 14px; font-family: PingFang SC; font-weight: 500; color: #e77316">
                    {{ data.after_sale_status_name }}
                  </span>
                  <span v-if="data.review_status == 1 || data.review_status == 2" class="platform-intervene ml-8px">{{
                    data.review_status == 1 ? '平台介入中' : '平台介入完成'
                  }}</span>
                  <a-tooltip>
                    <template #title>{{ data.refuse_fail || '--' }}</template>
                    <QuestionCircleFilled v-if="data.after_sale_status == 12" class="m-l-8px" />
                  </a-tooltip>
                </div>
                <span
                  class="ml-8px"
                  style="font-size: 14px; font-family: PingFang SC; font-weight: 400; color: #999999"
                  v-if="data.platform_handle === 1"
                  >(平台操作退款)</span
                >

                <span
                  class="cellos-item_id w140px"
                  style="font-weight: normal; display: inline"
                  v-if="data.after_status_remark"
                  >{{ data.after_status_remark }}</span
                >
              </a-space>
              <div class="cellos-item_id" v-if="data.after_sale_status == 10">卖家超时未处理，系统自动退款</div>
              <div
                class="number-id c-#e63030"
                v-if="[1, 3].includes(data.after_sale_status) && openCountDown(data.next_operate_time)"
              >
                {{ openCountDown(data.next_operate_time) }}
              </div>
              <div>{{ customsState(data.customs_status) }}</div>

              <div class="offine-refund flex" v-if="data.refund_money_type === 2">
                <div class="offine-refund-box">线下退款</div>
                <a-button type="link" class="p-0! h-auto" @click="handleNewActions('lookOffineRefund', data)"
                  >查看凭证</a-button
                >
              </div>
            </template>
            <template v-if="scope.column.key === 'apply_time'">
              <div>
                <div>{{ data.apply_time || '--' }}</div>
                <div>{{ data.success_at || '--' }}</div>
              </div>
            </template>
            <template v-if="scope.column.key === 'action'">
              <a-space direction="vertical" align="start">
                <!--              <a-button-->
                <!--                v-auth="['shopSaleEditWuliu']"-->
                <!--                type="link"-->
                <!--                size="small"-->
                <!--                v-if="[2, 4].includes(data.after_sale_status)"-->
                <!--                link-->
                <!--                @click="handleActions('sendGoods', data)"-->
                <!--              >-->
                <!--                填写退货物流-->
                <!--              </a-button>-->
                <!--              <a-button-->
                <!--                v-auth="['shopSaleEditWuliu']"-->
                <!--                v-if="[3].includes(data.after_sale_status)"-->
                <!--                type="link"-->
                <!--                size="small"-->
                <!--                @click="handleActions('editSendGoods', data)"-->
                <!--              >-->
                <!--                修改退货物流-->
                <!--              </a-button>-->
                <!--              <a-button-->
                <!--                v-auth="['shopSaleYelHuo']"-->
                <!--                v-if="[3].includes(data.after_sale_status)"-->
                <!--                type="link"-->
                <!--                size="small"-->
                <!--                @click="handleActions('affirmShippingAddress', data)"-->
                <!--              >-->
                <!--                确认收货-->
                <!--              </a-button>-->
                <!-- v-auth="['shopSaleLookDetail']" -->

                <a-button v-auth="['shopSaleLookDetail']" type="link" size="small" class="pa-0!" @click="toPath(data)"
                  >查看详情</a-button
                >
                <template v-if="data.after_sale_complaint">
                  <a-button
                    v-auth="['shopRelatedComplaints']"
                    type="link"
                    size="small"
                    class="pa-0!"
                    @click="handleActions('relatedComplaints', data)"
                    >关联投诉</a-button
                  >
                </template>

                <template v-if="data.after_sale_status === 12">
                  <a-button
                    class="p-0!"
                    :class="[data?.transfering && 'disabled-btn']"
                    type="link"
                    size="small"
                    @click="handleNewActions('refundAgain', data)"
                  >
                    再次退款
                  </a-button>
                </template>
                <span
                  v-auth="['shopSaleRemark']"
                  style="color: #f2a626"
                  class="cursor-pointer"
                  :class="[data?.transfering && 'disabled-btn']"
                  @click="handleActions('remark', data)"
                  >备注({{ data.shop_remark_count }})</span
                >
                <!-- <a-button type="link" size="small" @click="crossBorderdRefundHandle(data)"> 退款 </a-button> -->
                <!--              <a-button-->
                <!--                v-auth="['shopSaleYes']"-->
                <!--                type="link"-->
                <!--                size="small"-->
                <!--                v-if="[1].includes(data.after_sale_status)"-->
                <!--                @click="handleActions('agree', data)"-->
                <!--              >-->
                <!--                同意-->
                <!--              </a-button>-->
                <!--              <a-button-->
                <!--                v-auth="['shopSaleNo']"-->
                <!--                type="link"-->
                <!--                size="small"-->
                <!--                v-if="[1].includes(data.after_sale_status)"-->
                <!--                @click="handleActions('turnDown', data)"-->
                <!--              >-->
                <!--                拒绝-->
                <!--              </a-button>-->
              </a-space>
            </template>
          </template>
          <template #footer="{ scope, data }">
            <div class="table_row_bottom" v-if="data.shop_remark || data.user_remark">
              <div class="label">{{ data.shop_remark ? getPlatformInfo(data.remark_type) : '买家备注：' }}</div>
              <div class="val text_overflow">
                <a-tooltip placement="topLeft">
                  <template #title>{{ data.shop_remark ? data.shop_remark : data.user_remark }}</template>
                  {{ data.shop_remark ? data.shop_remark : data.user_remark }}
                </a-tooltip>
              </div>
              <div class="flex" v-if="data.shop_remark_url">
                <div
                  v-for="(i, index) in JSON.parse(data.shop_remark_url)"
                  :key="index"
                  class="overflow-hidden border-rd-4px ml-8px"
                  style="width: 30px; height: 30px"
                >
                  <a-image
                    v-if="i.type == 'img' || i.type == 'image'"
                    width="30px"
                    height="30px"
                    :src="i.url"
                  ></a-image>
                  <FilePreview
                    v-if="i.type == 'video'"
                    :src="i.url"
                    :cover="i.cover_image"
                    style="width: 30px; height: 30px"
                  ></FilePreview>
                </div>
              </div>
            </div>
          </template>
        </DescTableLayout>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.modalConfig.isVisible"
      :title="state.modalConfig.title"
      centered
      :closable="state.modalConfig.warpKey === 'relatedComplaints' ? true : false"
      :footer="state.modalConfig.warpKey === 'relatedComplaints' ? null : undefined"
      :confirm-loading="state.modalConfig.confirmLoading"
      @cancel="modalCancel"
      @ok="modalOk"
    >
      <div v-if="['refund'].includes(state.modalConfig.warpKey)">确认同意买家退款后，系统将自动发起退款处理</div>
      <div v-if="['refundReturn'].includes(state.modalConfig.warpKey)">确认同意买家退款后，系统将自动发起退款处理</div>
      <div v-if="['agree'].includes(state.modalConfig.warpKey)">确认进行此操作吗?</div>
      <div v-if="['affirmShippingAddress'].includes(state.modalConfig.warpKey)">
        <div>请确认是否收到货物</div>
        <div>确认收货后，系统将自动发起退款处理</div>
      </div>
      <TurnDown
        ref="formRef"
        v-if="['turnDown'].includes(state.modalConfig.warpKey)"
        v-model:data="state.formTurnDownData"
      />
      <MaterialFlow
        v-if="['deliverGoods', 'returnGoods'].includes(state.modalConfig.warpKey)"
        :data="state.modalConfig"
      />
      <SendGoods
        v-if="['sendGoods', 'editSendGoods'].includes(state.modalConfig.warpKey)"
        :item="state.modalConfig"
        ref="formRef"
        v-model:data="state.logisticsFormData"
        @changeId="
          (data) => {
            state.logisticsFormData.express_id = data.id
          }
        "
      />
      <Remark
        v-if="['remark'].includes(state.modalConfig.warpKey)"
        :item="state.modalConfig.data"
        v-model:data="state.modalConfig.remarkData"
        ref="formRef"
      />
      <!-- 关联投诉 -->
      <RelatedComplaints
        v-if="['relatedComplaints'].includes(state.modalConfig.warpKey)"
        :data="state.modalConfig.data"
        @event="
          () => {
            state.modalConfig.isVisible = false
          }
        "
      ></RelatedComplaints>
    </a-modal>
    <a-tour
      style="width: 314px !important"
      scrollIntoViewOptions="false"
      v-if="state.tableConfig.list.length > 0"
      v-model:current="current"
      :open="open"
      :steps="steps"
      @close="handleOpen(false)"
    />
    <a-modal
      v-model:open="state.newModalConfig.isVisible"
      :title="state.newModalConfig.title"
      centered
      :width="state.newModalConfig.width"
      destroyOnClose
      :maskClosable="false"
      :footer="null"
    >
      <RefundAgainModal
        v-if="['refundAgain', 'lookOffineRefund'].includes(state.newModalConfig.type)"
        :data="state.newModalConfig.data"
        :isPreview="state.newModalConfig.type === 'refundAgain' ? false : true"
        @onEvent="onNewEvent"
      />
    </a-modal>
  </div>
</template>
<script setup lang="tsx">
  defineOptions({ name: 'Sales' })
  import MaterialFlow from './components/MaterialFlow.vue'
  import SendGoods from './components/SendGoods.vue'
  import TurnDown from './components/TurnDown.vue'
  import Remark from './components/Remark.vue'
  import RelatedComplaints from './components/RelatedComplaints.vue'

  import RefundAgainModal from './components/refundAgainModal.vue'
  import { h, onMounted, reactive, ref, onActivated, nextTick } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { cloneDeep } from 'lodash-es'
  import { Button, message, notification, Modal } from 'ant-design-vue'
  import { QuestionCircleFilled, EyeOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
  import {
    get_after_sale_list,
    get_after_sale_count,
    post_after_sale_batch_return_apply,
    post_after_sale_edit,
    get_after_sale_status,
    post_exportdata_create,
    saleStatus,
    post_after_sale_address_update,
    post_after_sale_address_confirm,
    refundType,
    helpAddApi
  } from './index.api'
  import { post_order_record_add } from '@/views/shop/order/list/index.api.ts'
  import {
    SEARCH_CONFIG_DATA,
    BUTTON_RADIO_GROUP,
    salesStatus,
    columns,
    modalConfigTitle,
    formTurnDownData,
    logisticsFormData,
    customsState
  } from './data'
  import { getShopList, getWechatConfAllList } from '@/views/shop/order/list/index.api'
  import { returnMoney } from '../sales_details/index.api'
  import { useRouterBack } from '@/hooks'
  import { requireImg, checkMobilePermission, convertToImg, localStg } from '@/utils'
  import type { TourProps } from 'ant-design-vue'
  const { routeParams, resetRouteParams } = useRouterBack()
  import moment from 'moment'
  const router = useRouter()
  const route = useRoute()
  const formRef = ref(null)
  const open = ref<boolean>(false)
  const current = ref(0)
  const state = reactive({
    review_status: false,
    refund_money_type: false,
    logisticsFormData: JSON.parse(JSON.stringify(logisticsFormData)),
    initParams: {
      page: 1,
      page_size: 10,
      review_status: 0,
      refund_money_type: 0
    },
    form: {},
    searchConfig: SEARCH_CONFIG_DATA,
    actionConfig: cloneDeep(BUTTON_RADIO_GROUP),
    tableConfig: {
      isLoading: false,
      size: 'small',

      pagination: false,
      columns: columns,
      scroll: { scrollToFirstRowOnChange: true, x: 'max-content' },
      list: []
    },
    paginationConfig: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    selectTable: [],
    allSelectTable: [],
    indeterminate: false,
    formTurnDownData: JSON.parse(JSON.stringify(formTurnDownData)),
    modalConfig: {
      isVisible: false,
      title: '',
      warpKey: '',
      data: '' as any,
      confirmLoading: false,
      remarkData: {
        remark: '',
        url: []
      }
    },
    newModalConfig: {
      isVisible: false,
      title: '',
      type: '',
      data: '' as any
    } as any
  })
  const steps: TourProps['steps'] = [
    {
      title: '按钮位置优化',
      description: '售后列表中的【售后类型】搬家了',
      placement: 'top',
      target: () => document.getElementById('afterSale')
    }
  ]

  const handleOpen = async (val: boolean) => {
    open.value = val
    try {
      await helpAddApi({ ui_change: true })
      let obj = localStg.get('userInfo')
      if (obj) {
        obj.ui_change_show = false
      }
      localStg.set('userInfo', obj)
      initPageData(state.initParams)
    } catch (error) {
      console.log(error)
    }
  }
  const getPlatformInfo = (type) => {
    const obj = {
      1: '总后台备注：',
      2: '商家后台备注：',
      3: '平台客服备注：',
      4: '商家客服备注：',
      5: '买家备注：'
    }
    return obj[type]
  }
  const changeReview_status = (event) => {
    const val = event.target.checked ? 1 : 0
    state.initParams.review_status = val
    initPageData(state.initParams)
  }
  const changeOffline_refund = (event) => {
    const val = event.target.checked ? 2 : 0
    state.initParams.refund_money_type = val
    initPageData(state.initParams)
  }
  // 售后状态
  const salesStatusText = (type) => {
    return {
      1: '待处理',
      2: '待买家退货',
      3: '待商家收货',
      4: '换货待商家发货',
      5: '待买家收货',
      6: '退款完成',
      7: '售后驳回',
      8: '退款中',
      9: '极速退款成功',
      10: '售后完成',
      11: '待买家处理',
      12: '退款失败',
      13: '售后关闭'
    }[type]
  }
  const channelType = (type: number) => {
    let status: Record<number, string> = {
      0: '自然流量',
      1: '广点通',
      2: '视频号广告',
      4: '磁力引擎',
      6: '巨量引擎',
      8: '超级汇川'
    }
    return status[type]
  }
  // 订单来源
  const ordersource = (type: string | number) => {
    let status = {
      1: '自然流量',
      2: '广告平台',
      3: '回流流量',
      4: '外部流量'
    }
    return status[type]
  }
  const iconType = (type: number) => {
    let status: Record<number, string> = {
      4: requireImg('order/o1.png'),
      1: requireImg('order/o8.png'),
      5: requireImg('order/o5.png'),
      6: requireImg('order/o6.png'),
      8: requireImg('order/o9.png')
    }
    return status[type]
  }
  const orderStatus = reactive([
    { id: 'all', name: '全部', show: true, param: {}, num: 0 },
    {
      id: 'only_return_money',
      name: '仅退款待处理',
      show: true,
      num: 0,
      param: { after_sale_status: 1, after_sale_type: 1 }
    },
    {
      id: 'return_goods_money',
      name: '退货待处理',
      show: true,
      num: 0,
      param: { after_sale_status: 1, after_sale_type: 2 }
    },
    { id: 'wait_user_return_goods', name: '待买家退货', show: true, num: 0, param: { after_sale_status: 2 } },
    { id: 'wait_shop_deliver_goods', name: '待商家收货', show: true, num: 0, param: { after_sale_status: 3 } },
    // { id: "after_complete", name: '售后完成', show: false, num: 0, param: { after_sale_status: 10 } },
    { id: 'after_close', name: '售后关闭', num: 0, show: true, param: { after_sale_status: 7 } },
    { id: 'return_money_complete', name: '退款完成', show: true, num: 0, param: { after_sale_status: 6 } }
  ])
  const toPath = (data: any) => {
    let query = {
      id: data.id
    }
    if (data.after_sale_complaint) {
      query.after_sale_complaint = data.after_sale_complaint
      query.order_sn = data.order_sn
    }
    router.push({ path: '/shop/order/sale_details', query })
  }

  // 搜索
  const changeValue = (data) => {
    state.form = data.formData

    // let obj = state?.actionConfig?.list.filter((item) => item.value === state.actionConfig.value)
    // console.log(2, obj)

    // state.initParams = { ...state.initParams, page: 1, page_size: 10, ...data.formData }
    let params = {
      ...state.initParams,
      page: 1,
      page_size: 10,
      ...data.formData,
      after_sale_status: data.formData.after_sale_status
        ? data.formData.after_sale_status
        : state.initParams.after_sale_status,
      after_sale_type: data.formData.after_sale_type ? data.formData.after_sale_type : state.initParams.after_sale_type
    }

    // params.after_sale_type = state.form.after_sale_type ? state.form.after_sale_type : null
    // params.after_sale_status = state.form.after_sale_status ? state.form.after_sale_status : null

    state.initParams = params
    // if (obj.param?.after_sale_status) {
    //   state.initParams.after_sale_status = obj.param.after_sale_status
    // } else {
    //   delete state.initParams.after_sale_status
    // }
    // if (obj.param?.after_sale_type) {
    //   state.initParams.after_sale_type = obj.param.after_sale_type
    // } else {
    //   delete state.initParams.after_sale_type
    // }
    // 售后状态和Tab切换联动

    if (data.formData && data.formData.created_at && data.formData.created_at[0] && data.formData.created_at[1]) {
      state.initParams.created_at = data.formData.created_at.join('_')
    } else {
      state.initParams.created_at = undefined
    }
    state.initParams.pay_time = data.formData.pay_time ? data.formData.pay_time.join('_') : undefined
    state.initParams.finish_time = data.formData.finish_time ? data.formData.finish_time.join('_') : undefined
    if (data.formData.after_sale_status) {
      const obj =
        orderStatus.find(
          (v) => v.param.after_sale_status == data.formData.after_sale_status && !v.param.after_sale_type
        ) || {}
      changeBtnType({ value: obj.id || 'all' })
    }

    changeBtnType({ value: state.actionConfig.value })
  }
  const crossBorderdRefundHandle = (val) => {
    try {
      Modal.confirm({
        content: '当前处理的售后单还未完成退货清关，若清关失败则可能导致钱货两空，确定要退款么？',
        icon: null,
        centered: true,
        okText: '确定退款',
        onOk: async () => {
          // await couponRelease({ id: row.id })
          message.success('操作成功')
          getPageData()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  // 按钮操作
  const changeBtnType = (data) => {
    state.actionConfig.value = data.value

    let arr = state.actionConfig.list.filter((item) => item.value === data.value)

    // if (data.value !== 'all') {
    state.initParams = {
      ...{
        page: 1,
        page_size: 10,
        review_status: state.initParams.review_status,
        refund_money_type: state.initParams.refund_money_type
      },
      ...state.form,
      ...arr[0].param,
      created_at:
        state.form.created_at &&
        state.form.created_at[0] &&
        state.form.created_at[1] &&
        state.form.created_at.join('_'),
      pay_time: state.form.pay_time ? state.form.pay_time.join('_') : undefined,
      finish_time: state.form.finish_time ? state.form.finish_time.join('_') : undefined,
      after_sale_type: state.form.after_sale_type
        ? state.form.after_sale_type
        : arr[0].param.after_sale_type
          ? arr[0].param.after_sale_type
          : null,
      after_sale_status: state.form.after_sale_status
        ? state.form.after_sale_status
        : arr[0].param.after_sale_status
          ? arr[0].param.after_sale_status
          : null
    }
    console.log(state.initParams)
    // }
    // state.searchConfig.data[4].value = arr[0].param.after_sale_status
    console.log('state.initParams', state.initParams)
    const sortArr = [
      'only_return_money',
      'return_goods_money',
      'wait_user_return_goods',
      'wait_shop_deliver_goods',
      'time_out'
    ]
    if (state.actionConfig.value == 'wait_shop_deliver_goods' && state.form.next_time_sort) {
      state.initParams.next_time_sort = state.form.next_time_sort
    } else if (sortArr.includes(state.actionConfig.value)) {
      state.initParams.next_time_sort = 'asc'
    } else {
      state.initParams.next_time_sort = undefined
    }

    initPageData(state.initParams)
  }
  onMounted(() => {
    getPageData()
  })
  onActivated(() => {
    if (routeParams.params?.page) {
      if (routeParams.params?.page === 'add') {
        state.initParams.page = 1
        state.initParams.page_size = 10
      }
      getPageData()
      resetRouteParams()
    }
  })
  const searchFormDataRef = ref()
  const getPageData = () => {
    if (route.query?.after_sale_status) {
      state.initParams.after_sale_status = route.query?.after_sale_status
      state.actionConfig.value = 'after_close'
    }
    if (route.query?.refund_sn) {
      // state.searchConfig.data[0].value = route.query?.refund_sn
      state.initParams.refund_sn = route.query?.refund_sn
      nextTick(() => {
        searchFormDataRef.value.formData.refund_sn = route.query?.refund_sn
      })
      // state.content.refund_sn = route.query?.refund_sn
    }
    initPageData(state.initParams)
    initStatus()
    initGetWechatConfAllList()
    initGetShopList()
  }
  const initGetWechatConfAllList = async () => {
    const result = await getWechatConfAllList({ page_size: 1000 })
    if (result.code === 0) {
      state.searchConfig.data[10].props.options = (result.data.list || []).map((v) => {
        return {
          value: v.app_id,
          label: v.app_name
        }
      })
    }
  }
  const initGetShopList = async () => {
    const result = await getShopList({ page_size: 9999 })
    if (result.code === 0) {
      if (result?.data?.list?.length) {
        state.searchConfig.data[3].props.options = result.data.list.map((item) => {
          return {
            value: item.shop_info?.id || '',
            label: item.shop_info?.name || ''
          }
        })
      }
    }
  }
  const initStatus = async () => {
    const result = await get_after_sale_status()
    if (result.code === 0) {
      let arr = []
      for (const resultKey in result.data.list) {
        let item = {
          value: undefined,
          label: undefined
        }
        item.value = +resultKey
        item.label = result.data.list[resultKey]
        arr.push(item)
      }
      state.searchConfig.data[4].props.options = arr
    }
  }
  // 初始化全选数据
  const initSelectAll = (data, key) => {
    const allArr = []
    data && data.forEach((item) => allArr.push(item[key]))
    return allArr
  }

  const init_sale_count = async () => {
    const copyInitParams = JSON.parse(JSON.stringify(state.initParams))
    ;(copyInitParams.after_sale_type = state.form.after_sale_type ? state.form.after_sale_type : null),
      (copyInitParams.after_sale_status = state.form.after_sale_status ? state.form.after_sale_status : null)
    // delete copyInitParams.after_sale_type
    // delete copyInitParams.after_sale_status
    delete copyInitParams.next_operate_time
    const resultCount = await get_after_sale_count(copyInitParams)
    if (resultCount.data) {
      state.actionConfig.list.forEach((item) => {
        item.total = resultCount.data[item.value] || 0
      })
    }
  }
  const modifyButtonText = () => {
    // 获取 "结束导览" 按钮
    const nextButton = document.querySelector('.ant-tour-next-btn span')
    if (nextButton) {
      nextButton.textContent = '知道了'
    }
  }
  const initPageData = async (data) => {
    try {
      state.tableConfig.isLoading = true
      const result = await get_after_sale_list(data)
      if (result.code === 0) {
        const resultList = result.data?.list || []
        state.tableConfig.list = resultList.map((v) => {
          return {
            ...v,
            showPhone: 0
          }
        })
        state.allSelectTable = initSelectAll(state.tableConfig.list, 'id')
        state.paginationConfig.total = result.data?.total || 0
        state.paginationConfig.current = result.data?.page || 1
        state.paginationConfig.pageSize = result.data?.size || 10
        init_sale_count()
        if (localStg.get('userInfo')?.ui_change_show) {
          setTimeout(() => {
            open.value = true
          }, 500)
          setTimeout(() => {
            modifyButtonText()
          }, 600)
        }
      }
    } catch (error) {
    } finally {
      state.tableConfig.isLoading = false
    }
  }
  const handleSort = () => {
    if (state.initParams.apply_time_sort === 'desc') {
      state.initParams.apply_time_sort = 'asc'
    } else {
      state.initParams.apply_time_sort = 'desc'
    }
    state.initParams.page = 1
    initPageData(state.initParams)
  }
  // 分页
  const changePages = (data) => {
    state.initParams.page = data.page
    state.initParams.page_size = data.pageSize
    initPageData(state.initParams)
  }
  // 选择每一条数据
  const tabelItemSelect = (checkedValue, data) => {
    if (state.selectTable.includes(data.id)) {
      state.selectTable = state.selectTable.filter((item) => item !== data.id)
    } else {
      state.selectTable.push(data.id)
    }
    state.indeterminate = !!state.selectTable.length && state.selectTable.length < state.allSelectTable.length
  }
  // 全选
  const onCheckAllChange = (e: any) => {
    Object.assign(state, {
      selectTable: e.target.checked ? state.allSelectTable : [],
      indeterminate: false
    })
  }
  // 集中处理 操作事件
  const handleActions = (type, data) => {
    if (!state.selectTable.length && !data) {
      message.warning('请选择相关售后订单进行批量操作')
      return
    }
    if (['remark'].includes(type) && data?.transfering) {
      message.warning('该笔售后单存在打款中记录，不可操作售后单')
      return
    }
    if (['editSendGoods'].includes(type)) {
      state.logisticsFormData.logistics_company = data.logistics_company
      state.logisticsFormData.logistics_number = data.logistics_number
    }
    state.modalConfig.isVisible = true
    state.modalConfig.title = modalConfigTitle[type]
    state.modalConfig.warpKey = type
    state.modalConfig.data = data
  }
  const modalCancel = () => {
    state.modalConfig.isVisible = false
    setTimeout(() => {
      if (['turnDown'].includes(state.modalConfig.warpKey)) {
        state.formTurnDownData = JSON.parse(JSON.stringify(formTurnDownData))
      }
      if (['sendGoods', 'editSendGoods'].includes(state.modalConfig.warpKey)) {
        state.logisticsFormData = JSON.parse(JSON.stringify(logisticsFormData))
      }
      state.modalConfig.title = ''
      state.modalConfig.warpKey = ''
      state.modalConfig.data = ''
      state.modalConfig.confirmLoading = false
    }, 300)
  }
  const modalOk = async () => {
    try {
      state.modalConfig.confirmLoading = true
      let result = null
      if (['refund', 'refundReturn'].includes(state.modalConfig.warpKey)) {
        result = await post_after_sale_batch_return_apply({ ids: state.selectTable.join() })
      }
      if (['agree'].includes(state.modalConfig.warpKey)) {
        result = await post_after_sale_edit({ after_sale_status: 1, refund_sn: state.modalConfig.data.refund_sn })
      }
      if (['turnDown'].includes(state.modalConfig.warpKey)) {
        const values = await formRef.value.validateFields()
        if (values) {
          result = await post_after_sale_edit({
            after_sale_status: 2,
            refund_sn: state.modalConfig.data.refund_sn,
            refuse_reason: state.formTurnDownData.refuse_reason
          })
        }
      }
      if (['sendGoods', 'editSendGoods'].includes(state.modalConfig.warpKey)) {
        const values = await formRef.value.validateFields()
        if (values) {
          const list = formRef.value.getlist()
          list.forEach((item) => {
            if (item.id === values.express_id) {
              values.logistics_company = item.code
            }
          })

          result = await post_after_sale_address_update({
            id: state.modalConfig.data.id,
            ...values
          })
        }
      }
      if (['affirmShippingAddress'].includes(state.modalConfig.warpKey)) {
        result = await post_after_sale_address_confirm({
          id: state.modalConfig.data.id
        })
      }

      if (['remark'].includes(state.modalConfig.warpKey)) {
        const values = await formRef.value.validateFields()
        if (values) {
          let list =
            values.url?.map((v) => {
              if (v?.fileInfo?.type == 'video') {
                return {
                  url: v.url,
                  type: v?.fileInfo?.type,
                  cover_image: convertToImg(v.url)
                }
              } else return { url: v.url, type: 'image' }
            }) || []
          const res = await post_order_record_add({
            order_id: state.modalConfig.data.order_id,
            ...values,
            url: JSON.stringify(list)
          })
          if (res.code === 0) {
            state.modalConfig.confirmLoading = false
            initPageData(state.initParams)
            formRef.value.getRemarkList()
            state.modalConfig.remarkData.remark = null
            state.modalConfig.remarkData.url = []
          }
        }
      }

      if (result?.code === 0) {
        message.success('操作成功')
        state.selectTable = []
        state.indeterminate = false
        initPageData(state.initParams)
      }
      if (state.modalConfig.warpKey !== 'remark') modalCancel()
    } catch (error: any) {
      if (state.modalConfig.warpKey !== 'remark') modalCancel()
      state.modalConfig.confirmLoading = false
    }
  }
  const downloadSales = async () => {
    // 没有对应权限且点击了取消
    if ((await checkMobilePermission('salesLookPhone')) == 1) return
    let params = {
      ...state.initParams
    }
    if (!state.initParams.after_sale_type) {
      delete params.after_sale_type
    }
    if (!state.initParams.after_sale_status) {
      delete params.after_sale_status
    }

    if (state.initParams.admin_ids && !state.initParams.admin_ids.length) {
      params.admin_ids = undefined
    }
    const result = await post_exportdata_create({
      params: JSON.stringify(params),
      type: 'after_order'
    })
    if (result.code === 0) {
      notification.success({
        key: 'loaditem',
        message: '导出成功',
        description: () => {
          return h(
            Button,
            {
              type: 'link',
              size: 'small',
              onClick: () => go_page('load', null)
            },
            { default: () => '请前往系统->下载中心查看' }
          )
        }
      })
    }
  }
  const go_page = (type, data) => {
    if (['load'].includes(type)) {
      notification.close('loaditem')
      const href = router.push({
        path: '/system/setUp/download'
      })
      // window.open(href.href, '_blank')
    }
  }

  // 获取售后状态
  const getSaleStatus = async () => {
    try {
      let res = await saleStatus()
      state.saleData = res.data.list
      // searchList.forEach((v) => {
      //   if (v.key == 'after_sale_status') {
      //     v.options = Object.keys(res.data.list).map((v) => {
      //       return {
      //         value: v,
      //         label: res.data.list[v],
      //       }
      //     })
      //   }
      // })
    } catch (error) {}
  }

  // 获取售后原因
  const getRefundType = async () => {
    try {
      let res = await refundType()
      let arr: any[] = []
      let _data = cloneDeep(res.data)
      for (const key in _data) {
        if (_data[key]) arr.push(..._data[key])
      }
      state.searchConfig.data.forEach((v) => {
        if (v.field == 'msg_id') {
          v.props.options = arr.map((v) => {
            return {
              value: v.key,
              label: v.value
            }
          })
        }
      })
    } catch (error) {
      console.log(error, 'arrarrarr1')
    }
  }
  // const toDetail = (row) => {
  //   router.push({ path: '/shop/order/sale_details', query: { id: row.id } })
  // }
  getRefundType()
  getSaleStatus()

  // 开启倒计时
  function openCountDown(time) {
    if (!time) {
      return null
    }

    const currentTime = moment()
    const targetDate = moment.unix(time)

    if (currentTime.isBefore(targetDate)) {
      const duration = moment.duration(targetDate.diff(currentTime))

      const days = Math.floor(duration.asDays()).toString().padStart(2, '0')
      const hours = duration.hours().toString().padStart(2, '0')
      const minutes = duration.minutes().toString().padStart(2, '0')
      const seconds = duration.seconds().toString().padStart(2, '0')

      return `${days}天${hours}小时${minutes}分${seconds}秒`
    } else {
      return '00天00小时00分00秒'
    }
  }

  const handleNewActions = (type: string, record: any) => {
    try {
      if (type === 'refundAgain' || type === 'lookOffineRefund') {
        if (record?.transfering && type === 'refundAgain') {
          message.warning('该笔售后单存在打款中记录，不可操作售后单')
          return
        }
        state.newModalConfig.type = type
        state.newModalConfig.width = 620
        state.newModalConfig.isVisible = true
        state.newModalConfig.title = type === 'refundAgain' ? '再次退款' : '退款详情'
        state.newModalConfig.data = record
      }
    } catch (err) {
      console.log(e)
    }
  }
  const onNewEvent = ({ cmd, data }: any) => {
    state.newModalConfig.isVisible = false
    if (cmd === 'submit') {
      getPageData()
    }
  }
</script>
<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn.scss';
  .sale_type {
    background: #f6f8ff;
    border-radius: 3px;
    border: 1px solid #e1e6ff;
    font-weight: 400;
    font-size: 14px;
    color: #647dff;
    padding: 2px 8px;
    display: inline-block;
  }
  :deep(.other_w .desc-table-layout-list) {
    min-width: 1000px;
  }
  :deep(.other_w .desc-table-layout-list .desc-table-layout-list-item:last-child) {
    margin-bottom: 0;
  }
  .icon {
    width: 15px;
    height: 15px;
    margin-right: 5px;
  }
  img {
    display: block;
    width: 100%;
    height: 100%;
  }
  .card-warp-dex {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .action-warp {
    padding: 8px 16px;
  }
  .desc-table-layout-list-item-desc {
    background-color: rgba(36, 47, 87, 0.05);
    padding: 8px 16px;
  }
  .user-address {
    @include text_overflow(2);
  }
  .cellos-item_title {
    cursor: pointer;
    @include set_font_config(--font-size-large, --primary-color);
    @include text_overflow(2);
    font-size: 14px;
  }
  .cellos-item-prod-warp {
    box-sizing: border-box;
    padding: 0 8px;
    overflow: hidden;
  }
  .cellos-item_img {
    @include set_node_whb(70px, 70px);
  }
  .cellos-item_style {
    @include set_font_config(--font-size-tiny, --text-color-gray);
    @include text_overflow(1);
    padding: 4px 0px;
    box-sizing: border-box;
  }
  .cellos-item_id {
    @include set_font_config(--font-size-tiny, --text-color-gray);
    @include text_overflow(1);
  }
  .cellos-item_mask {
    @include set_font_config(--font-size-tiny, --primary-color);
  }
  .after_status {
    width: 120px;
    .colorE6 {
      color: #e63030;
      cursor: pointer;
    }
  }
  .item {
    margin-right: 12px;
    .mini_program_logo {
      img {
        width: 12px;
        height: 12px;
        display: block;
        margin-right: 5px;
        margin-bottom: 1px;
      }
    }
  }
  .colorE6 {
    color: #e63030;
    cursor: pointer;
  }
  .address_info {
    word-wrap: break-word;
    word-break: break-all;
    @include text_overflow(1);
  }
  .warn {
    color: #e63030 !important;
  }
  .radioGroup {
    .warn {
      border: 1px solid #e63030;
      color: #e63030 !important;
    }
    .warn::before {
      background-color: #e63030;
    }
  }

  .table_row_bottom {
    // height: 52px;
    // line-height: 52px;
    // background: #f3f6fc;
    // padding-left: 20px;
    font-size: 14px;
    color: #404040;
    display: flex;
    // border: 1px solid #eef0f3;
    border-top: none;
    .label {
      color: #404040;
      width: fit-content;
    }
    .val {
      // flex: 1;
      max-width: 60%;
    }
  }
  .item-lable {
    border: 1px solid #fe4d4f;
    color: #fe4d4f;
    border-radius: 4px;
    padding: 2px;
  }
  .refund {
    width: 66px;
    height: 18px;
    font-size: 12px;
    color: #60a13b;
    background: #e5f0df;
    border-radius: 4px;
    margin-left: 6px;
    padding: 4px;
  }
  .reason {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    text-align: justify;
    overflow: hidden;
  }
  .reasonimg {
    width: 100%;
    flex-wrap: wrap;
    .image {
      width: 70px;
      height: 70px;
      display: block;
      margin-bottom: 6px;
    }
  }
  // ::v-deep .desc-table-layout-list {
  //   min-width: 600px;
  // }
  ::v-deep .desc-table-layout-list-item {
    width: 100%;
  }
  .disabled-btn {
    cursor: not-allowed;
    color: #00000040 !important;
    &:hover {
      color: #00000040 !important;
    }
  }
  .disabled-bg-btn {
    cursor: not-allowed;
    background-color: #00000040 !important;
    &:hover {
      background-color: #00000040 !important;
    }
  }
  .platform-intervene {
    background: #ffeeee;
    border-radius: 2px;
    border: 1px solid #ffbbbc;
    padding: 2px 4px;
    box-sizing: border-box;
    font-size: 12px;
    color: #ff4d4f;
    line-height: 12px;
    display: inline-block;
  }
  .platform-intervene {
    background: #ffeeee;
    border-radius: 2px;
    border: 1px solid #ffbbbc;
    padding: 2px 4px;
    font-size: 12px;
    color: #ff4d4f;
    line-height: 12px;
    display: inline-block;
    box-sizing: border-box;
  }
  .offine-refund-box {
    padding: 2px;
    border-radius: 2px;
    background-color: #e8e8e9;
    color: #636e95;
    // border: 1px solid #e8e8e9;
    margin-right: 4px;
  }
</style>
<style>
  .ant-tour {
    width: 314px;
  }
  .ant-tour .ant-tour-inner .ant-tour-header .ant-tour-title {
    font-size: 16px;
  }
  .ant-tour-inner .ant-tour-description {
    font-weight: 400;
    font-size: 14px;
    color: #575757;
    padding-left: 24px !important;
    margin-bottom: 10px;
  }
  .ant-tour .ant-tour-inner .ant-tour-header {
    padding: 20px 18px 16px 24px;
  }
</style>
