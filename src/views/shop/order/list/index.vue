<template>
  <div>
    <DesTablePage :pagination="state.paginationConfig" @changePages="changePages" class="common_card_wrapper">
      <template #title>
        <div>订单列表</div>
      </template>
      <template #extra>
        <a-button v-auth="['shopOrderExport']" type="primary" @click="downloadOrder">导出</a-button>
      </template>
      <template #search>
        <div class="parcel mt-16px">
          <div class="desc flex flex-items-baseline flex-justify-between mb8px">
            <div class="flex flex-items-baseline mb-11px">
              <div class="parcel-title-2">包裹异常监控</div>
              <div class="updataTime">数据更新时间：{{ state.logistic_date || '--' }}</div>
            </div>
            <div class="flex tips">
              <img class="tip" src="@/assets/images/goods/tips.png" alt="" />
              <span class="c-#FE9000 font-size-12px ml-8px font-normal line-height-12px"
                >注：包裹异常监控功能内测中</span
              >
            </div>
          </div>
          <div class="mb-9px">
            <HparcelList :list="state.logistics" show-tooltip source="orderList" @event="onParcelEvent" />
          </div>
        </div>
        <SearchBaseLayout
          ref="searchFormDataRef"
          :data="state.searchConfig.data"
          @changeValue="changeValue"
          :actions="state.searchConfig.options"
          use-config
        />
      </template>
      <template #action>
        <div class="flex-y-center flex-justify-between">
          <ButtonRadioGroup :data="state.actionConfig" @changeValue="changeBtnType" />
          <div class="checkbox mt-16px">
            <a-checkbox
              v-if="[0, 3].includes(state.initParams.status) && ![6].includes(state.initParams.after_status)"
              v-model:checked="state.signed_for"
              @change="changeSignedFor"
            >
              <span class="w-50px inline-block">已签收</span>
            </a-checkbox>
            <a-checkbox
              v-if="[0, 2].includes(state.initParams.status) && ![6].includes(state.initParams.after_status)"
              v-model:checked="state.urge_send"
              @change="changeUrgeSend"
            >
              <span class="w-60px inline-block">已催发货</span>
            </a-checkbox>
          </div>
        </div>
      </template>
      <template #tableWarp>
        <DescTableLayout :data="state.tableConfig" class="other_w">
          <template #action>
            <div class="action-warp flex flex-justify-between"></div>
          </template>
          <template #desc="{ data }">
            <div class="desc-table-layout-list-item-desc flex flex-justify-between">
              <div class="flex flex-items-center">
                <span class="mini_program_logo" v-if="data.order_type !== 4">
                  <img
                    v-if="data.app_name.indexOf('(H5)') > -1"
                    :src="requireImg('order/icon_h5.png')"
                    alt="小程序来源"
                  />
                  <img v-else :src="requireImg('order/mini_program.png')" alt="小程序来源" />
                </span>
                <span class="mr5px" v-if="data.order_type !== 4">{{ data.app_name || '--' }}</span>

                <span class="mini_program_logo" v-if="data.order_type === 4">
                  <img :src="requireImg('order/werxin_shop.png')" alt="微信小店" />
                </span>
                <span v-if="data.order_type === 4" class="mr5px">{{ data.wechat_shop_name || '--' }}</span>
                <span class="flex flex-items-center m-r-5px" v-if="data.order_type != 4">
                  <SvgIcon icon="dianpu" class="m-r-5px"></SvgIcon>
                  <span>{{ data.shop_name }}</span>
                </span>
                <span class="m-r-5px">订单编号: {{ data.order_num }}</span>
                <span class="m-r-5px">来源:</span>
                <template v-if="true">
                  <span class="flex flex-items-center">
                    <a-tooltip>
                      <template #title>{{ channelType(data.channel_type) }}</template>
                      <img
                        v-if="[1, 4, 5, 6, 8].includes(data.channel_type)"
                        class="icon m-r-5px"
                        :src="iconType(data.channel_type)"
                        alt=""
                      />
                      <!-- <img
                    v-if="data.channel_type == 1"
                    class="icon m-r-5px"
                    :src="requireImg('order/wx_icon2.png')"
                    alt=""
                  /> -->
                    </a-tooltip>
                    <span>{{ data.recall_type == 1 ? '弃单召回' : ordersource(data.order_source) }}</span>
                  </span>
                  <!-- <span v-if="[2, 3].includes(data.order_source)">{{ isCallbackType(data.is_callback) }}</span> -->
                  <span v-if="![1, 4].includes(data.order_source)"
                    >（计划ID：{{ +data.c_id || '--' }}
                    <span style="width: 5px; display: inline-block"></span> 账户ID：{{ data.ad_account_id || '--'
                    }}<a-tooltip>
                      <template #title>广告账户授权异常，会导致报表数据错误 </template>
                      <ExclamationCircleOutlined
                        v-if="data.is_authorized == 1"
                        class="c-#e63030 ml-10px mr-5px"
                      /> </a-tooltip
                    >）</span
                  >
                  <span class="ml-3px flex items-center" v-if="data.wechat_shop_finder_name">
                    推客：{{ data.wechat_shop_finder_name }}
                    <!-- <CopyOutlined class="ml5px c-#1677ff" /> -->
                    <SvgIcon
                      icon="ad-copy-1"
                      class="ml-5px c-#1677ff cursor-pointer"
                      @click="copy(data.wechat_shop_finder_name)"
                    />
                  </span>
                  <a-popover>
                    <template #content>
                      <div class="mini_program_logo flex items-center mb4px">
                        <img v-if="data.page_type === 2" :src="requireImg('order/icon_h5.png')" alt="h5来源" />
                        <img v-else :src="requireImg('order/mini_program.png')" alt="小程序来源" />
                        <span class="mr5px">{{ data.app_name || '--' }}</span>
                      </div>

                      <div class="ml20px c-#666666 font-size-12px">
                        <!-- <SvgIcon icon="dianpu" class="m-r-5px"></SvgIcon> -->
                        店铺：<span>{{ data.shop_name }}</span>
                      </div>
                    </template>
                    <span class="c-#1677ff ml15px cursor-pointer" v-if="data.order_type == 4">更多信息</span>
                  </a-popover>
                </template>
                <template v-else> {{ data.wechat_shop_finder_name || '--' }} </template>
              </div>
              <a-space>
                <span>负责人: {{ data.admin_name || '--' }}</span>
                <span>
                  <span>所属项目:</span>
                  <a-tooltip>
                    <template #title>{{ data.ad_name || '--' }}</template>
                    <span>{{
                      data.ad_name && data.ad_name.length > 6 ? `${data.ad_name.slice(0, 6)}...` : data.ad_name || '--'
                    }}</span>
                  </a-tooltip>
                </span>
                <span v-if="data.pay_time !== 0">支付时间: {{ secToTime(data.pay_time) }}</span>
                <span v-else>创建时间: {{ secToTime(data.created_at) }}</span>
              </a-space>
            </div>
          </template>
          <template #bodyCell="{ scope, data, pindex }">
            <template v-if="scope.column.key === 'goods'">
              <div class="flex">
                <div class="cellos-item_img"><img :src="scope.record.image" alt="" /></div>
                <div class="flex" style="flex: 1; overflow: hidden">
                  <div class="flex-1 cellos-item-prod-warp">
                    <a-tooltip placement="topLeft">
                      <template #title>{{ scope.record.name }}</template>
                      <div class="cellos-item_title">
                        {{ scope.record.name }}
                      </div>
                    </a-tooltip>
                    <div class="cellos-item_style">
                      <a-tooltip placement="topLeft">
                        <template #title>{{ scope.record.sku_name || '--' }}</template>
                        <span>{{ scope.record.sku_name || '--' }}</span>
                      </a-tooltip>
                    </div>
                    <div class="cellos-item_id">
                      <span>落地页ID：</span>
                      <span>{{ data.order_type == 4 ? data.product_code : scope.record.product_code }}</span>
                    </div>
                    <div v-if="data.order_type == 4 && data.wechat_shop_product_id" class="number-id cellos-item_id">
                      <span>小店商品ID：</span> <span>{{ data.wechat_shop_product_id }}</span>
                    </div>
                    <span
                      class="border p-4px pt0 pb0 border-rd-2px border-color-#FFE3D1 color-#FE9653 bg-#FFF7F3 mr8px"
                      v-show="scope.record?.active_type"
                    >
                      {{ scope.record.active_type === 1 ? '顺手买一件' : '周边推荐' }}
                    </span>
                    <span
                      class="border p-4px pt0 pb0 border-rd-2px font-size-12px border-color-#C4EEE2 color-#19A378 bg-#E8F6F2 mr8px"
                      v-show="[4].includes(data.activity_type)"
                    >
                      送礼物
                    </span>

                    <!-- <span
                    v-if="data.order_type == 2"
                    class="border p-3px pt0 pb0 border-rd-2px border-color-#E6D9FF color-#7C49DC bg-#F8F5FF font-size-12px line-height-14px inline-block"
                  >
                    全球购
                  </span> -->
                  </div>
                  <div class="">
                    <div class="text-right pb-1">¥ {{ scope.record.price }}</div>
                    <div class="text-right">{{ scope.record.num }}件</div>
                  </div>
                </div>
              </div>
            </template>
            <template v-if="scope.column.key === 'order'">
              <div class="flex flex-col">
                <span
                  class="font-medium"
                  :style="{
                    color: orderType(data.status).color
                  }"
                  >{{ orderType(data.status).text }}</span
                >

                <span v-if="[1, 2, 3, 8, 15, 16].includes(data.after_status)" class="c-red font-medium">售后中</span>
                <span v-else-if="[6, 9, 10].includes(data.after_status)" class="c-#404040 font-medium">
                  <span>售后完成</span>
                  <a-popover v-if="data.after_status_remark">
                    <template #content>
                      <span>{{ data.after_status_remark }}</span>
                    </template>
                    <InfoCircleOutlined style="margin-left: 6px" />
                  </a-popover>
                </span>
                <span v-else-if="[7].includes(data.after_status)" class="c-#404040 font-medium">售后关闭</span>
                <span v-else-if="[12].includes(data.after_status)" class="c-#404040 font-medium">退款失败</span>
                <span class="color-opacity-6" v-else>无售后</span>
                <span style="color: #f2a626" v-if="data.time_out_str && state.actionConfig.value != 9">{{
                  data.time_out_str
                }}</span>
                <div>
                  <span class="item-urge" v-if="[1].includes(data.urge_send)">{{ urgeStatus[data.urge_send] }}</span>
                  <div class="date-urge" v-if="data.urge_time">
                    {{ formatDate(data.urge_time * 1000) }}
                  </div>
                </div>
                <div class="c-#ff4052" v-if="data.time_out_str && state.actionConfig.value == 9">
                  <InfoCircleOutlined class="mr-6px" />
                  <span>发货超时,即将退款</span>
                </div>
              </div>
            </template>

            <template v-if="scope.column.key === 'prices'">
              <div>
                <div>¥ {{ data.money?.toFixed(2) }}</div>
                <div class="flex">
                  <span style="color: #05b855">微信支付</span>
                  <span class="ml8px c-#f2a626"> {{ data.mch_id ? `（${data.mch_id}）` : '--' }}</span>
                </div>
                <a-tooltip
                  color="#fff"
                  trigger="click"
                  :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
                >
                  <template #title>
                    <div class="p-8px">
                      <div class="c-#494949">运费：¥ {{ data.freight_money?.toFixed(2) || '0.00' }}</div>
                      <div class="c-#494949" v-if="scope.record.active_type !== 2">
                        优惠：¥ {{ data.discount?.toFixed(2) || '0.00' }} {{ couponSource(data.coupon_source) }}
                      </div>
                      <div v-else class="c-#494949">优惠减：¥0.00</div>
                      <div class="c-#494949" v-if="data.rebate_money > 0">
                        返现：¥ {{ data.rebate_money?.toFixed(2) || '0.00' }}
                      </div>
                      <div class="c-#494949" v-if="data.recall_type == 1">
                        召回立减：¥{{ data.recall_money.toFixed(2) || '0.00' }}
                      </div>
                      <div class="c-#494949" v-if="data.recall_type == 2">
                        降价：¥{{ data.recall_money.toFixed(2) || '0.00' }}
                      </div>
                      <div class="c-#494949" v-if="data.transfer_money > 0">
                        打款：¥ {{ data.transfer_money?.toFixed(2) || '0.00' }}
                      </div>
                    </div>
                  </template>
                  <a-button type="link" class="p-0! h-auto!">查看明细</a-button>
                </a-tooltip>
              </div>
            </template>
            <template v-if="scope.column.key === 'user'">
              <div>
                <div class="wx_info" v-if="data.phone" style="display: flex; align-items: center">
                  <img
                    src="@/assets/images/order/wx_icon.png"
                    style="width: 14px; height: 14px; font-size: 14px; margin-right: 5px"
                  />
                  {{ data.phone }}
                </div>
                <div>unionid：{{ data.unionid || '--' }}</div>
                <div class="flex flex-items-center">
                  <a-tooltip>
                    <template #title>{{ data.consignee }}</template>
                    <span class="text_overflow max-w-80px"> {{ data.consignee }} </span>
                  </a-tooltip>
                  <span class="pl-1">
                    {{ data.receive_phone || '--' }}
                    <span v-auth="['orderLookPhone']">
                      <DataEncipher
                        v-model:data="state.tableConfig.list[pindex]"
                        showKey="showPhone"
                        goalkey="receive_phone"
                        :type="1"
                        argKey="order_id"
                      />
                    </span>
                  </span>
                </div>
                <a-tooltip>
                  <template #title>{{ data.address }}{{ data.address_detail }}</template>
                  <div class="user-address">{{ data.address }}{{ data.address_detail }}</div>
                </a-tooltip>
                <!-- <div>
                <span
                  class="cu_po"
                  v-if="data.address && [1, 2].includes(data.status)"
                  @click="handleActions('area', data)"
                  >修改</span
                >
              </div> -->
              </div>
            </template>
            <template v-if="scope.column.key === 'pay'">
              <div v-if="data?.express_number">
                <div class="flex flex-items-center">
                  <span>{{ data.express_name }}</span>
                  <span class="parcel_tag ml-6px" v-if="data.logistics_status_name">{{
                    data.logistics_status_name || '--'
                  }}</span>
                </div>
                <div>{{ data.express_number }}</div>
                <div>{{ data.deliver_time != 0 ? formatDate(data.deliver_time * 1000) : '--' }}</div>
                <span class="cu_po" @click="handleActions('lookWl', data)"> 查看物流 </span>
                <!-- 3已签收 301正常签收 302派件异常后最终签收 304待收签收 311快递柜或驿站签收 -->
                <span class="c-#1981ff ml-2" v-if="[3, 301, 302, 304, 311].includes(data.logistics_status)">
                  已签收
                </span>
              </div>
              <div v-else>--</div>
              <!-- <template v-if="data.order_type === 2">
              <div v-if="data.customs_status !== 'CUSTOMS_AUDIT_IM_SUCCESS_CALLBACK'">
                支付清关:
                <span :style="{ color: payCustomsState(data.pay_customs_status)?.color }">{{
                  data.pay_customs_status_name || '-'
                }}</span>
                <a-tooltip placement="topLeft">
                  <template #title>{{
                    data?.order_customs?.pay_customs_failed_reason || data?.order_customs?.cert_check_result_name
                  }}</template>
                  <ExclamationCircleOutlined
                    class="c-#FE4042 ml4px"
                    v-if="data?.order_customs?.pay_customs_failed_reason || data?.order_customs?.cert_check_result_name"
                  />
                </a-tooltip>
              </div>
              <div>
                海关清关:
                <span :style="{ color: customsState(data.customs_status)?.color }">{{
                  customsState(data.customs_status)?.name
                }}</span>
                <a-tooltip placement="topLeft">
                  <template #title>{{ data?.order_customs?.customs_failed_reason || '-' }}</template>
                  <ExclamationCircleOutlined
                    class="c-#FE4042 ml4px"
                    v-if="data?.order_customs?.customs_failed_reason"
                  />
                </a-tooltip>
              </div>
            </template> -->
            </template>
            <template v-if="scope.column.key === 'action'">
              <div class="flex flex-col">
                <span
                  v-auth="['shopOrderLook']"
                  class="cu_po"
                  @click="
                    router.push({
                      path: '/shop/order/order_details',
                      query: data?.sub_order_num
                        ? {
                            id: data?.order_id,
                            active_type: data?.product_list?.[0]?.active_type,
                            sub_order_num: data?.sub_order_num
                          }
                        : {
                            id: data?.order_id,
                            active_type: data?.product_list?.[0]?.active_type
                          }
                    })
                  "
                  >查看详情</span
                >
                <template v-if="data.order_type !== 4">
                  <span
                    v-auth="['shopOrderListRoker']"
                    style="color: #f2a626"
                    class="cu_po"
                    @click="handleActions('remark', data)"
                    >备注({{ data.shop_remark_count }})</span
                  >
                  <span
                    v-auth="['shopOrderListNoOrder']"
                    class="cu_po"
                    v-show="state.actionConfig.value == 9 || data.status == 7"
                    @click="handleCancelAction('cancelOrder', data)"
                    >取消订单</span
                  >
                </template>

                <!-- 跨境订单  并且不是(已取消/待付款)  并且 不是海关清关成功或失败 并且不是售后完成 才能展示 -->
                <!-- <span
                class="cu_po"
                @click="submitRowBtn(1, data)"
                v-if="
                  data?.order_type == 2 &&
                  ![-1, 1].includes(data?.status) &&
                  !['CUSTOMS_AUDIT_IM_SUCCESS_CALLBACK', 'CUSTOMS_AUDIT_IM_FAILURE_CALLBACK'].includes(
                    data?.customs_status
                  ) &&
                  ![6, 9, 10].includes(data?.after_status)
                "
                >提交支付申报</span
              > -->
                <!-- <span class="cu_po" @click="submitRowBtn(2, data)">提交海关清关</span> -->
              </div>
            </template>
          </template>
          <template #footer="{ scope, data }">
            <div class="table_row_bottom" v-if="data.shop_remark || data.user_remark">
              <div class="label">{{ data.shop_remark ? getPlatformInfo(data.remark_type) : '买家备注：' }}</div>
              <div class="val text_overflow">
                <a-tooltip placement="topLeft">
                  <template #title>{{ data.shop_remark ? data.shop_remark : data.user_remark }}</template>
                  {{ data.shop_remark ? data.shop_remark : data.user_remark }}
                </a-tooltip>
              </div>
              <div class="flex" v-if="data.shop_remark_url">
                <div
                  v-for="(i, index) in JSON.parse(data.shop_remark_url)"
                  :key="index"
                  class="overflow-hidden border-rd-4px ml-8px"
                  style="width: 30px; height: 30px"
                >
                  <a-image
                    v-if="i.type == 'img' || i.type == 'image'"
                    width="30px"
                    height="30px"
                    :src="i.url"
                  ></a-image>
                  <FilePreview
                    v-if="i.type == 'video'"
                    :src="i.url"
                    :cover="i.cover_image"
                    style="width: 30px; height: 30px"
                  ></FilePreview>
                </div>
              </div>
            </div>
          </template>
        </DescTableLayout>
      </template>
    </DesTablePage>
    <a-modal
      :width="['lookWl'].includes(state.modalConfigData.type) ? 600 : 520"
      centered
      @cancel="modalCancel"
      @ok="modalOk"
      v-model:open="state.modalConfigData.open"
      :title="state.modalConfigData.title"
    >
      <Remark
        v-if="['remark'].includes(state.modalConfigData.type)"
        v-model:data="state.remarkData"
        ref="formRef"
        :item="state.modalConfigData.data"
      />
      <CancelOrder
        v-if="['cancelOrder'].includes(state.modalConfigData.type)"
        v-model:data="state.cancelOrderData"
        ref="formRef"
      />
      <DeliverGoods
        v-if="['deliverGoods'].includes(state.modalConfigData.type)"
        v-model:data="state.deliverGoodsData"
        ref="formRef"
        :list="state.expressData"
      />
      <LookWl v-if="['lookWl'].includes(state.modalConfigData.type)" :data="state.modalConfigData.data" />
      <EditArea v-if="['area'].includes(state.modalConfigData.type)" ref="formRef" v-model:data="state.areaFormData" />
    </a-modal>
  </div>
</template>
<script setup lang="tsx">
  defineOptions({ name: 'OrderList' })
  import moment from 'moment'
  import { SvgIcon } from '@/components'
  import DeliverGoods from './components/DeliverGoods.vue'
  import CancelOrder from './components/CancelOrder.vue'
  import Remark from './components/Remark.vue'
  import LookWl from './components/LookWl.vue'
  import EditArea from './components/EditArea.vue'
  import HparcelList from './components/ParcelList.vue'
  import ParcelList from '@/views/shop/review/data/components/ParcelList.vue'
  import datas from './data'
  import { onMounted, reactive, ref, h, onActivated } from 'vue'
  import { DownOutlined, EyeOutlined, InfoCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import {
    getShopList,
    getWechatConfAllList,
    get_order_list_count,
    get_order_list,
    addressUpdate,
    post_order_record_add,
    post_exportdata_create,
    listLogisticCount,
    replay_pay_customs,
    getShopCascade
  } from './index.api'
  import { wechatDarenList } from '@/views/shop/goods/talent/index.api'
  const {
    ordersource,
    SEARCH_CONFIG_DATA,
    ACTIONS_DATA,
    orderType,
    modalConfigData,
    staticTitle,
    cancelOrderData,
    deliverGoodsData,
    remarkData,
    couponSource,
    initQuery,
    isCallbackType,
    areaFormData,
    channelType,
    iconType,
    customsState,
    payCustomsState
  } = datas()
  import { secToTime, formatDate, requireImg, copy, checkMobilePermission, convertToImg } from '@/utils'
  import { useRoute, useRouter } from 'vue-router'
  import { message, notification, Button, Modal } from 'ant-design-vue'
  import { debounce } from 'lodash-es'
  import { post_order_cancel } from '@/views/shop/order/details/index.api'
  const route = useRoute()
  const formRef = ref(null)
  const router = useRouter()
  const searchFormDataRef = ref()
  const state = reactive({
    initParams: {
      page: 1,
      page_size: 10,
      urge_send: 0,
      status: route.query?.status || 0,
      after_status: 0,
      un_split_status: 0,
      channel_type: 0,
      ad_id: '',
      ad_account_id: 0,
      coupon_type: 0,
      order_source: route.query?.order_source || 0,
      created_at: `${moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')}_${moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')}`,
      shop_ids: '',
      company_ids: ''
    },
    logistics_code: '',
    signed_for: false,
    urge_send: false,
    searchConfig: SEARCH_CONFIG_DATA,
    modalConfigData,
    remarkData: JSON.parse(JSON.stringify(remarkData)),
    cancelOrderData: JSON.parse(JSON.stringify(cancelOrderData)),
    deliverGoodsData: JSON.parse(JSON.stringify(deliverGoodsData)),
    areaFormData: JSON.parse(JSON.stringify(areaFormData)),
    actionConfig: {
      value: 0,
      list: ACTIONS_DATA
    },
    tableConfig: {
      isLoading: false,
      size: 'small',
      dataSourceKey: 'product_list',
      pagination: false,
      scroll: { scrollToFirstRowOnChange: true, x: 'max-content' },
      columns: [
        {
          title: '商品信息',
          dataIndex: 'goods',
          key: 'goods',
          width: 180
        },
        {
          title: '订单/售后状态',
          key: 'order',
          dataIndex: 'order',
          width: 150
        },
        {
          title: '实付金额(元)',
          key: 'prices',
          dataIndex: 'prices',
          width: 200
        },
        {
          title: '买家/收货人',
          key: 'user',
          dataIndex: 'user',
          width: 180
        },
        {
          title: '配送方式/发货时间',
          key: 'pay',
          dataIndex: 'pay',
          width: 180
        },
        {
          title: '操作',
          dataIndex: 'action',
          key: 'action',
          width: 120
        }
      ],
      list: []
    },
    paginationConfig: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    logistics: [
      // {
      //   logistics_status: 501,
      //   label: '支付单清关失败',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '微信支付单清关失败的订单数'
      // },
      // {
      //   logistics_status: 502,
      //   label: '订购人身份异常',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '用户实名认证信息填写错误的订单数'
      // },
      // {
      //   logistics_status: 503,
      //   label: '海关待清关',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '还未提交海关清关的订单数'
      // },
      // {
      //   logistics_status: 504,
      //   label: '海关清关中',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '海关清关中的订单数'
      // },
      // {
      //   logistics_status: 505,
      //   label: '海关拒单',
      //   unit_text: '(单)',
      //   num: 0,
      //   desc: '海关清关审核失败的订单数'
      // },
      { logistics_status: 0, label: '异常总单数', unit_text: '(个)', num: 0, desc: '包含所有异常包裹', children: [] },
      {
        logistics_status: 1401,
        label: '揽收异常',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '系统打单或下单后，超过了预设监控时段还未被取走的包裹'
      },
      {
        logistics_status: 1402,
        label: '签收异常',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '派件员长期持有包裹或联系不上客户未派送。可能存在丢件或收派网点积压，需要及时跟踪和处理'
      },
      {
        logistics_status: 1403,
        label: '物流停滞',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '包裹运输过程中，长时间没有产生下一步物流轨迹。可能存在丢件或派件网点积压，需要及时干预'
      },
      {
        logistics_status: 1405,
        label: '派件异常',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '快递员派件过程中，因无法联系、超区件等问题导致派件异常'
      },

      {
        logistics_status: 1404,
        label: '拒收/拦截',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '收件人拒绝签收或包裹运输过程中，因拦截而退回的包裹'
      },
      {
        logistics_status: 1406,
        label: '问题件',
        unit_text: '(个)',
        num: 0,
        children: [],
        desc: '其他破损、退货未签收等问题件'
      }
    ],
    logistic_date: ''
  })

  const getPlatformInfo = (type) => {
    const obj = {
      1: '总后台备注：',
      2: '商家后台备注：',
      3: '平台客服备注：',
      4: '商家客服备注：',
      5: '买家备注：'
    }
    return obj[type]
  }
  // 获取包裹异常监控
  const getListLogisticCount = debounce(async () => {
    try {
      const { data } = await listLogisticCount()
      state.logistics.forEach((item) => {
        data.cnt.forEach((items) => {
          if (item.logistics_status == items.code) {
            item.num = items.count
            item.children = items.children
          }
        })
      })
      state.logistic_date = data.last_callback_date || formatDate(new Date().getTime())
    } catch (error) {
      console.error(error)
    }
  }, 500)

  const parcel_tag_filter = (type: any) => {
    if (!type) return false
    const result = state.logistics.find((v: any) => v.logistics_status === type)
    if (result) {
      return result.label
    } else {
      return '-'
    }
  }

  // 取消订单
  const handleCancelAction = async (type: any, data: any) => {
    try {
      if ([1, 2, 3, 8].includes(data.after_status)) return await cancelOrderConfirm('该订单已存在售后!')
      if (data.status === 3) await cancelOrderConfirm('当前订单已发货。取消后订单金额将原路退回。是否要取消？')
      handleActions(type, data)
    } catch (error) {
      console.error(error)
    }
  }

  const urgeStatus = reactive({
    1: '已催发货',
    0: '未催发货'
  })

  // 卡片点击事件
  const onParcelEvent = (data: any, it: any) => {
    if (data.checked) {
      state.initParams.logistics_status = data.logistics_status || 1
      searchFormDataRef.value.formData.created_at = []
      delete state.initParams.created_at
    } else {
      searchFormDataRef.value.formData.created_at = [
        moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
      ]
      state.initParams.created_at = searchFormDataRef.value.formData.created_at.join('_')
      delete state.initParams.logistics_status
    }
    if (it?.code) {
      state.initParams.logistics_status = it.code
    }
    initPageData(state.initParams)
  }

  // 搜索
  const changeValue = (data: any) => {
    console.log(data, '~~~~~~~~~~~~~~~~~~~~')
    let shopids = []
    let companyids = []
    if (data.formData?.shop_id?.length > 0) {
      data.formData?.shop_id.forEach((item) => {
        if (item.length == 1) {
          companyids.push(item[0])
        } else {
          if (companyids.indexOf(item[0]) == -1) {
            companyids.push(item[0])
          }
          if (shopids.indexOf(item[1]) == -1) {
            shopids.push(item[1])
          }
        }
      })
    }

    state.initParams = {
      ...state.initParams,
      ...data.formData,
      shop_ids: shopids.length ? shopids.join(',') : '',
      company_ids: companyids.length ? companyids.join(',') : ''
    }
    delete state.initParams.shop_id
    console.log(state.initParams, 'state.initParams')
    if (data.formData && data.formData.appids?.length) {
      state.initParams.appids = data.formData.appids.join(',')
    } else {
      state.initParams.appids = null
    }
    if (data.formData && data.formData.created_at && data.formData.created_at[0] && data.formData.created_at[1]) {
      state.initParams.created_at = data.formData.created_at.join('_')
    } else {
      state.initParams.created_at = null
    }
    if (data.formData && data.formData.pay_at && data.formData.pay_at[0] && data.formData.pay_at[1]) {
      state.initParams.pay_at = data.formData.pay_at.join('_')
    } else {
      state.initParams.pay_at = null
    }
    if (data.formData && data.formData.deliver_time && data.formData.deliver_time[0] && data.formData.deliver_time[1]) {
      state.initParams.deliver_time = data.formData.deliver_time.join('_')
    } else {
      state.initParams.deliver_time = null
    }
    if (
      data.formData &&
      data.formData.logistics_fail_time &&
      data.formData.logistics_fail_time[0] &&
      data.formData.logistics_fail_time[1]
    ) {
      state.initParams.logistics_fail_time = data.formData.logistics_fail_time.join('_')
    } else {
      state.initParams.logistics_fail_time = null
    }
    if (data.formData && data.formData.success_at && data.formData.success_at[0] && data.formData.success_at[1]) {
      state.initParams.success_at = data.formData.success_at.join('_')
    } else {
      state.initParams.success_at = null
    }
    if (!data.status) {
      data.formData.created_at = [
        moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
      ]
      searchFormDataRef.value.formData.created_at = [
        moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
      ]
      state.logistics.forEach((v: any) => {
        v.checked = false
      })
      delete state.initParams.logistics_status
    }
    const orderSource = data.formData.order_source
    if (orderSource) {
      const lastSource = orderSource[orderSource.length - 1]
      if (orderSource[0] === 125) {
        state.initParams.wechat_shop_finder_id = lastSource
        state.initParams.sms_source = undefined
        state.initParams.order_source = undefined
      } else {
        state.initParams.order_source = lastSource
        state.initParams.sms_source = undefined
      }
    } else {
      // 如果 order_source 不存在，可以根据需要处理
      state.initParams.sms_source = undefined
      state.initParams.order_source = undefined
      state.initParams.wechat_shop_finder_id = undefined
    }
    if (state.signed_for) {
      state.initParams.logistics_status = 3
    }
    initPageData(state.initParams)
  }
  // 按钮操作
  const changeBtnType = (data) => {
    state.actionConfig.value = data.value
    state.initParams.status = data.value
    state.initParams.page = 1
    state.initParams.after_status = 0
    if ([7, 9].includes(data.value)) {
      state.initParams.un_split_status = 2
      state.initParams.just_time_out = 1
    } else if (data.value == 8) {
      state.initParams.un_split_status = 1
      state.initParams.just_time_out = undefined
    } else if (data.value == 6) {
      state.initParams.status = 0
      state.initParams.un_split_status = 0
      state.initParams.after_status = 6
      state.initParams.just_time_out = undefined
    } else if (data.value == 101) {
      state.initParams.status = 0
      state.initParams.un_split_status = 0
      state.initParams.after_status = 0
      state.initParams.split_state = 'abnormal'
      state.initParams.just_time_out = undefined
    } else {
      state.initParams.un_split_status = 0
      state.initParams.just_time_out = undefined
    }
    initPageData(state.initParams)
  }

  onActivated(() => {
    if (route.query?.logistics_status) {
      //组件激活后把页面位置设置为0,页面跳转到顶部
      document.getElementById('scrollbar').scrollTop = 0
    } else {
      delete state.initParams.logistics_status
    }
    if (route.query?.order_num) {
      state.initParams.order_num = route.query?.order_num
      searchFormDataRef.value.formData.order_num = route.query?.order_num
      state.initParams.created_at = undefined
      searchFormDataRef.value.formData.created_at = []
    }
    if (route.query?.status) {
      state.initParams.status = +route.query?.status
      state.actionConfig.value = route.query?.status
    }
    initGetShopList()
    initGetWechatConfAllList()
    if (route.query?.status) {
      changeBtnType({ value: Number(route.query?.status) })
    } else {
      if (route.query?.logistics_status) {
        state.logistics_code = Number(route.query?.code) || undefined
        let checkedParcel
        state.logistics.forEach((v: any) => {
          if (v.logistics_status === Number(route.query?.logistics_status)) {
            v.checked = true
            checkedParcel = v
          } else {
            v.checked = false
          }
        })
        onParcelEvent(checkedParcel, { code: state.logistics_code })
      } else {
        initPageData(state.initParams)
      }
    }

    getListLogisticCount()
  })
  const initGetWechatConfAllList = async () => {
    const result = await getWechatConfAllList({ page_size: 1000 })
    if (result.code === 0) {
      ;(state.searchConfig.data || []).forEach((item) => {
        if (item.field === 'appids') {
          item.props.options = (result.data.list || []).map((v) => {
            return {
              value: v.app_id,
              label: v.app_name
            }
          })
        }
      })
    }
  }
  const initGetShopList = async () => {
    const result = await getShopCascade({ page_size: 1000 })
    // const result = await getShopList({ page_size: 1000 })
    if (result.code === 0) {
      if (result?.data?.length) {
        ;(state.searchConfig.data || []).forEach((item) => {
          if (item.field === 'shop_id') {
            item.props.options = result.data || []
          }
        })
      }
    }
  }
  //获取达人列表
  const getDarenList = async () => {
    try {
      let params = {
        page: 1,
        size: 9999
      }
      let { data } = await wechatDarenList(params)
      state.searchConfig.data.forEach((item) => {
        if (item.field === 'order_source') {
          item.props.options?.forEach((items) => {
            if (items.value == 125) {
              items.children = (data.list || []).map((v) => {
                return {
                  value: v.finder_id,
                  label: v.nickname
                }
              })
            }
          })
        }
      })
    } catch (error) {}
  }
  getDarenList()
  const initExpress = async () => {
    const result = await get_order_get_express()
    if (result.code === 0) {
      state.expressData = result.data
    }
  }
  const initPageData = async (data) => {
    try {
      state.tableConfig.isLoading = true
      if (route.query?.status) {
        data.created_at = undefined
        state.searchConfig.data.forEach((item) => {
          if (item.field === 'created_at') {
            item.props.value = undefined
          }
        })
      }

      const result = await get_order_list(data)
      const resultCount = await get_order_list_count(data)
      if (result.code === 0 && resultCount.code === 0) {
        state.tableConfig.list = result?.data?.list || []
        state.tableConfig.list = (result?.data?.list || []).map((v) => {
          return {
            ...v,
            showPhone: 0
          }
        })
        state.paginationConfig.total = result.data?.total_num || 0
        state.paginationConfig.pageSize = result.data?.size || 10
        state.paginationConfig.current = result.data?.page || 1
        if (resultCount.data.count) {
          // 0=全部  1,待支付 2,待发货 3待收货  4已完成 -1已取消 7发货超时  8,24小时内发货 5,售后中
          state.actionConfig.list.forEach((item) => {
            if ([7, 9].includes(item.value)) {
              item.total = resultCount.data.count[99] || 0
            } else if (item.value === 8) {
              item.total = resultCount.data.count[98] || 0
            } else if (item.value === 6) {
              item.total = resultCount.data?.count[97] || 0
            } else if (item.value === 17) {
              item.total = resultCount.data?.count[7] || 0
            } else if (item.value === 101) {
              item.total = resultCount.data?.count[101] || 0
            } else {
              item.total = resultCount.data.count[item.value] || 0
            }
          })
        }
        getListLogisticCount()
      }
    } catch (error) {
      console.error(error)
    } finally {
      state.tableConfig.isLoading = false
    }
  }
  // 分页
  const changePages = (data) => {
    state.initParams.page = data.page
    state.initParams.page_size = data.pageSize
    state.paginationConfig.pageSize = data.pageSize
    initPageData(state.initParams)
  }
  //提交按钮
  const submitRowBtn = async (type, row) => {
    switch (type) {
      case 1:
        if (row.pay_custom_status === 'PROCESSING') {
          Modal.confirm({
            title: '提示',
            icon: createVNode(ExclamationCircleOutlined),
            content: '支付单正在海关申报中，确认要重新提交申报么？',
            async onOk() {
              try {
                await replay_pay_customs({ order_num: row.order_num })
                message.success('提交成功')
                initPageData(state.initParams)
              } catch (error) {
                console.error(error)
              }
            }
          })
        } else {
          try {
            await replay_pay_customs({ order_num: row.order_num })
            message.success('提交成功')
            initPageData(state.initParams)
          } catch (error) {
            console.error(error)
          }
        }

        break
      case 2:
        console.log('暂无')

        break

      default:
        break
    }
    console.log(type, row)
  }
  const handleActions = (type, data) => {
    state.modalConfigData.open = true
    state.modalConfigData.type = type
    state.modalConfigData.data = data
    state.modalConfigData.title = staticTitle[type]
  }

  const modalCancel = () => {
    state.modalConfigData.open = false
    setTimeout(() => {
      if (['remark'].includes(state.modalConfigData.type)) {
        state.remarkData = JSON.parse(JSON.stringify(remarkData))
        initPageData(state.initParams)
      }
      if (['cancelOrder'].includes(state.modalConfigData.type)) {
        state.cancelOrderData = JSON.parse(JSON.stringify(cancelOrderData))
      }
      if (['deliverGoods'].includes(state.modalConfigData.type)) {
        state.deliverGoodsData = JSON.parse(JSON.stringify(deliverGoodsData))
      }
      if (['area'].includes(state.modalConfigData.type)) {
        state.areaFormData = JSON.parse(JSON.stringify(areaFormData))
      }
      state.modalConfigData.data = ''
      state.modalConfigData.type = ''
      state.modalConfigData.title = ''
    }, 300)
  }
  const modalOk = async () => {
    if (['lookWl'].includes(state.modalConfigData.type)) {
      modalCancel()
    }
    if (['remark'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      if (values) {
        console.log(state.modalConfigData.data)
        let list =
          values.url?.map((v) => {
            if (v?.fileInfo?.type == 'video') {
              return {
                url: v.url,
                type: v?.fileInfo?.type,
                cover_image: convertToImg(v.url)
              }
            } else return { url: v.url, type: 'image' }
          }) || []

        const result = await post_order_record_add({
          order_id: state.modalConfigData.data.order_id,
          ...values,
          url: JSON.stringify(list)
        })
        if (result.code === 0) {
          initPageData(state.initParams)
          formRef.value.getRemarkList()
        }
        state.remarkData.remark = null
        state.remarkData.url = []
        state.modalConfigData.open = true
      }
    }
    if (['cancelOrder'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      if (values) {
        const result = await post_order_cancel({
          order_id: state.modalConfigData.data.order_id,
          ...values,
          shop_id: state.modalConfigData.data.shop_id
          // money: state.modalConfigData.data.total_money
        })
        if (result.code === 0) {
          message.success(result.msg)
          initPageData(state.initParams)
          modalCancel()
        }
      }
    }
    if (['area'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      if (values) {
        let params = {
          order_id: state.modalConfigData.data.order_id,
          ...values,
          area_id: Number(values.area_ids[2]),
          area_ids: values.area_ids.join(',')
        }
        const result = await addressUpdate(params)
        if (result.code === 0) {
          initPageData(state.initParams)
          modalCancel()
        }
      }
    }
    if (['deliverGoods'].includes(state.modalConfigData.type)) {
      const values = await formRef.value.validateFields()
      if (values) {
        console.log(deliverGoodsData)
        let selectItem = state.expressData.filter((item) => item.id === values.express_id)
        let express = selectItem[0].code || ''
        const result = await post_order_send({
          order_id: state.modalConfigData.data.order_id,
          ...values,
          express
        })
        if (result.code === 0) {
          initPageData(state.initParams)
          modalCancel()
        }
      }
    }
  }

  const go_page = (type, data) => {
    if (['sale'].includes(type)) {
      let query = { order_sn: data.order_num }
      const href = router.resolve({
        path: '/shop/shopOrder/ShopSalesQuery',
        query: query
      })
      window.open(href.href, '_blank')
    }
    if (['load'].includes(type)) {
      notification.close('loaditem')
      router.push({
        path: '/system/setUp/download'
      })
      // window.open(href.href, '_blank')
    }
  }
  const downloadOrder = async () => {
    for (let key in state.initParams) {
      if (state.initParams[key] == null) {
        state.initParams[key] = undefined
      }
    }
    state.initParams.ad_account_id = Number(state.initParams.ad_account_id || 0)
    state.initParams.active_type = Number(state.initParams.active_type || 0)

    if (state.initParams?.admin_ids?.length == 0) {
      delete state.initParams.admin_ids
    }

    const result = await post_exportdata_create({
      params: JSON.stringify(state.initParams),
      type: 'boss_order'
    })
    if (result.code === 0) {
      notification.success({
        key: 'loaditem',
        message: '导出成功',
        description: () => {
          return h(
            Button,
            {
              type: 'link',
              size: 'small',
              onClick: () => go_page('load', null)
            },
            { default: () => '请前往系统->下载中心查看' }
          )
        }
      })
    }
  }
  const changeSignedFor = (event) => {
    const val = event.target.checked ? 3 : ''
    state.initParams.logistics_status = val
    initPageData(state.initParams)
  }
  const changeUrgeSend = (event) => {
    const val = event.target.checked ? 1 : 0
    state.initParams.urge_send = val
    initPageData(state.initParams)
  }
</script>
<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn.scss';
  :deep(.other_w .desc-table-layout-list) {
    min-width: 1000px;
  }
  :deep(.other_w .desc-table-layout-list .desc-table-layout-list-item:last-child) {
    margin-bottom: 0;
  }

  .parcel {
    .parcel-title {
      line-height: 14px;
      font-size: 14px;
      font-weight: 500;
      color: #080f1e;
      line-height: 14px;
    }
    .parcel-title-2 {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #5e6584;
      line-height: 16px;
      text-align: left;
      font-style: normal;
    }
    .updataTime {
      margin-left: 8px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 12px;
      text-align: left;
      font-style: normal;
    }
    .tips {
      align-items: center;
      padding: 6px 8px;
      background: #f9f9fa;
      border-radius: 2px;
      margin-left: 8px;
      .tip {
        width: 14px;
        height: 12px;
      }
    }
  }
  .parcel_tag {
    display: inline-block;
    line-height: 16px;
    background: rgba(211, 13, 13, 0.14);
    border-radius: 2px;
    font-weight: 400;
    font-size: 12px;
    color: #d30d0d;
    padding: 0 4px;
  }
  .mini_program_logo {
    img {
      width: 12px;
      height: 12px;
      display: block;
      margin-right: 5px;
      margin-bottom: 1px;
    }
  }
  .table_row_bottom {
    // height: 52px;
    // line-height: 52px;
    // background: #f3f6fc;
    // padding-left: 20px;
    font-size: 14px;
    color: #404040;
    display: flex;
    // border: 1px solid #eef0f3;
    border-top: none;
    .label {
      color: #404040;
      width: fit-content;
    }
    .val {
      // flex: 1;
      max-width: 60%;
    }
  }
  img {
    display: block;
    width: 100%;
    height: 100%;
  }
  .card-warp-dex {
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .action-warp {
    margin-top: 5px;
    padding: 8px 16px;
  }
  .desc-table-layout-list-item-desc {
    background-color: rgba(36, 47, 87, 0.05);
    padding: 8px 16px;
  }
  .user-address {
    @include text_overflow(2);
  }
  .cellos-item_title {
    // @include set_font_config(--font-size-large, --text-color-base);
    font-size: 14px;
    @include text_overflow(2);
  }
  .cellos-item-prod-warp {
    box-sizing: border-box;
    padding: 0 8px;
    overflow: hidden;
  }
  .cellos-item_img {
    @include set_node_whb(70px, 70px);
  }
  .cellos-item_style {
    @include set_font_config(--font-size-tiny, --text-color-gray);
    @include text_overflow(1);
    padding: 4px 0px;
    box-sizing: border-box;
  }
  .cellos-item_id {
    @include set_font_config(--font-size-tiny, --text-color-gray);
    @include text_overflow(1);
  }
  .cellos-item_mask {
    @include set_font_config(--font-size-tiny, --primary-color);
  }
  .cu_po {
    color: var(--primary-color);
    cursor: pointer;
  }
  .icon {
    width: 12px;
    height: 12px;
  }
  .wx_info {
    color: #60a13b;
  }
  .font_red {
    color: #b70000;
  }

  .item-urge {
    background: #e37c3f;
    border-radius: 3px;
    padding: 2px 4px;
    margin-top: 10px;
    display: inline-block;
    font-size: 12px;
    color: #fff;
  }
  :deep {
    .ant-cascader-dropdown .ant-cascader-menu {
      min-width: 280px;
    }
    .edit_cascader_wrapper .ant-cascader-menu-item {
      max-width: 100%;
    }
  }
  :deep(.common_card_wrapper .ant-card-body) {
    padding-top: 0;
  }
</style>
