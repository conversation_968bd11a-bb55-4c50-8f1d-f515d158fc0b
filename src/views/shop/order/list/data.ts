import { requireImg } from '@/utils'
import moment from 'moment'
import { Cascader } from 'ant-design-vue'
export default function datas() {
  // 订单来源
  const ordersource = (type) => {
    let status = {
      1: '自然流量',
      2: '广告平台',
      3: '回流流量',
      4: '外部流量'
    }
    return status[type]
  }

  //广告平台
  const channelType = (type) => {
    let status: Record<string | number, string> = {
      0: '自然流量',
      1: '广点通',
      2: '视频号广告',
      4: '磁力引擎',
      6: '巨量引擎',
      8: '超级汇川'
    }
    return status[type]
  }

  const iconType = (type) => {
    let status = {
      // 0: '',
      // 1: requireImg('order/wx_icon2.png'),
      // 3: requireImg('order/back1.png'),
      4: requireImg('order/o1.png'),
      1: requireImg('order/o8.png'),
      5: requireImg('order/o5.png'),
      6: requireImg('order/o6.png'),
      8: requireImg('order/o9.png')

    }
    return status[type]
  }

  // 搜索组件数据
  const SEARCH_CONFIG_DATA = {
    data: [
      {
        type: 'input.text',
        field: 'order_num',
        value: undefined,
        props: {
          placeholder: '请输入订单编号'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'product',
        value: undefined,
        props: {
          placeholder: '请输入商品名称/商品ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'consignee',
        value: undefined,
        props: {
          placeholder: '请输入收货人姓名/手机号'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'phone',
        value: undefined,
        props: {
          placeholder: '请输入买家手机号'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'cascader',
        field: 'shop_id',
        useConfig: true,
        props: {
          placeholder: '请选择店铺名称',
          options: [],
          multiple: true,
          showCheckedStrategy: Cascader.SHOW_CHILD,
          fieldNames: {
            label: 'name',
            value: 'id'
          },
        },
        value: [],
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'appids',
        useConfig: true,
        props: {
          placeholder: '请选择查询的小程序',
          options: [],
          showSearch: true,
          mode: 'multiple',
          showArrow: true
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'pay_num',
        value: undefined,
        useConfig: true,
        props: {
          placeholder: '请输入支付流水号'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'express_number',
        value: undefined,
        useConfig: true,
        props: {
          placeholder: '请输入快递单号'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'ad_id',
        value: undefined,
        useConfig: true,
        props: {
          placeholder: '请输入计划ID'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'input.text',
        field: 'ad_account_id',
        useConfig: true,
        props: {
          placeholder: '请输入账户ID'
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'coupon_type',
        value: undefined,
        useConfig: true,
        props: {
          placeholder: '请选择优惠券',
          options: [
            {
              value: 1,
              label: '新客优惠券'
            },
            {
              value: 2,
              label: '回流优惠券'
            },
            {
              value: 3,
              label: '支付失败优惠券'
            },
            {
              value: 4,
              label: '新客优惠券-微信卡券'
            },
            {
              value: 5,
              label: '回流优惠券-微信卡券'
            },
            {
              value: 7,
              label: '商品优惠券'
            },
            {
              value: 8,
              label: '商品优惠券-微信卡券'
            },
            {
              value: 9,
              label: '商品浏览优惠券'
            },
          ]
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'cascader',
        field: 'order_source',
        useConfig: true,
        props: {
          placeholder: '请选择订单来源',
          options: [
            {
              value: 1,
              label: '自然流量'
            },
            {
              value: 2,
              label: '广告流量'
            },
            {
              value: 3,
              label: '回流流量'
            },
            {
              value: 8,
              label: '外部流量'
            },
            {
              value: 7,
              label: '弃单召回'
            },
            {
              value: 125,
              label: '微信小店',
              children: []
            }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'channel_type',
        useConfig: true,
        props: {
          placeholder: '请选择广告平台',
          options: [
            {
              value: 1,
              label: '自然流量'
            },
            {
              value: 2,
              label: '广点通'
            },
            {
              value: 5,
              label: '磁力引擎'
            },
            {
              value: 7,
              label: '巨量引擎'
            },
            {
              value: 9,
              label: '超级汇川'
            }

          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'admin',
        field: 'admin_ids',
        useConfig: true,
        value: [],
        props: {
          placeholder: '请选择负责人',
          multiple: true
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'channel_id',
        field: 'channel_ids',
        useConfig: true,
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },

      {
        type: 'input.text',
        field: 'mch_id',
        value: undefined,
        useConfig: true,
        props: {
          placeholder: '请输入商户号'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'active_type',
        props: {
          placeholder: '请选择活动类型',
          options: [
            {
              value: '1',
              label: '顺手买一件'
            },
            {
              value: '2',
              label: '周边推荐'
            },
            {
              value: '4',
              label: '送礼物'
            }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'select',
        field: 'rebate_status',
        useConfig: true,
        label: '返现状态',
        props: {
          placeholder: '请选择返现状态',
          options: [
            { value: 0, label: '全部' },
            { value: 1, label: '已返现' },
            { value: 2, label: '未返现' }
          ]
        },
        value: undefined,
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      // {
      //   type: 'select',
      //   field: 'pay_custom_status',
      //   useConfig: true,
      //   label: '支付单状态',
      //   props: {
      //     placeholder: '请选择支付单状态',
      //     options: [
      //       { value: 'UNDECLARED', label: '未申报' },
      //       { value: 'SUBMITTED', label: '申报已提交' },
      //       { value: 'PROCESSING', label: '申报中' },
      //       { value: 'SUCCESS', label: '申报成功' },
      //       { value: 'FAIL', label: '申报失败' },
      //       { value: 'EXCEPT', label: '申报异常' }
      //     ]
      //   },
      //   value: undefined,
      //   layout: {
      //     xs: 24,
      //     sm: 12,
      //     md: 8,
      //     lg: 8,
      //     xl: 8,
      //     xxl: 6
      //   }
      // },
      // {
      //   type: 'select',
      //   field: 'customs_status',
      //   useConfig: true,
      //   label: '海关清关状态',
      //   props: {
      //     placeholder: '请选择海关清关状态',
      //     options: [
      //       { value: 'ORDER_PAYED_CUSTOMS_PENDING_CALLBACK', label: '待清关' },
      //       { value: 'CUSTOMS_AUDIT_IM_PROCESSING_CALLBACK', label: '清关中' },
      //       { value: 'CUSTOMS_AUDIT_IM_SUCCESS_CALLBACK', label: '清关成功' },
      //       { value: 'CUSTOMS_AUDIT_IM_FAILURE_CALLBACK', label: '清关失败' }
      //     ]
      //   },
      //   value: undefined,
      //   layout: {
      //     xs: 24,
      //     sm: 12,
      //     md: 8,
      //     lg: 8,
      //     xl: 8,
      //     xxl: 6
      //   }
      // },
      // {
      //   type: 'select',
      //   field: 'order_type',
      //   useConfig: true,
      //   label: '订单类型',
      //   props: {
      //     placeholder: '请选择订单类型',
      //     options: [
      //       { value: 1, label: '普通订单' },
      //       { value: 2, label: '跨境订单' }
      //     ]
      //   },
      //   value: undefined,
      //   layout: {
      //     xs: 24,
      //     sm: 12,
      //     md: 8,
      //     lg: 8,
      //     xl: 8,
      //     xxl: 6
      //   }
      // },
      {
        type: 'datetime',
        field: 'created_at',
        value: [
          moment().subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ],
        props: {
          placeholder: ['下单开始时间', '下单结束时间']
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'datetime',
        field: 'deliver_time',
        value: undefined,
        props: {
          placeholder: ['发货开始时间', '发货结束时间']
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'datetime',
        field: 'logistics_fail_time',
        value: undefined,
        props: {
          placeholder: ['物流异常开始时间', '物流异常结束时间']
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        type: 'datetime',
        field: 'success_at',
        value: undefined,
        props: {
          placeholder: ['退款完成开始时间', '退款完成结束时间']
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      }
      // {
      //   type: 'select',
      //   field: 'order_source',
      //   value: undefined,
      //   props: {
      //     placeholder: '请选择订单来源',
      //     options: [
      //       {
      //         value: 0,
      //         label: '全部'
      //       },
      //       {
      //         value: 1,
      //         label: '自然流量'
      //       },
      //       {
      //         value: 2,
      //         label: '广告平台-未回传'
      //       },
      //       {
      //         value: 3,
      //         label: '广告平台-已回传'
      //       },
      //       {
      //         value: 4,
      //         label: '回传流量-已回传'
      //       },
      //       {
      //         value: 5,
      //         label: '回传流量-未回传'
      //       }
      //     ]
      //   },
      //   layout: {
      //     xs: 24,
      //     sm: 12,
      //     md: 8,
      //     lg: 8,
      //     xl: 8,
      //     xxl: 6
      //   }
      // },
      // {
      //   type: 'date',
      //   field: 'pay_at',
      //   value: undefined,
      //   props: {
      //     placeholder: ['支付开始时间', '支付结束时间']
      //   },
      //   layout: {
      //     xs: 24,
      //     sm: 12,
      //     md: 8,
      //     lg: 8,
      //     xl: 8,
      //     xxl: 6
      //   }
      // },
    ],
    options: {
      foldNum: 11
    }
  }

  // 操纵按钮组
  const ACTIONS_DATA = [
    {
      value: 0,
      label: '全部',
      total: 0
    },
    {
      value: 1,
      label: '待付款',
      total: 0
    },
    {
      value: 2,
      label: '待发货',
      total: 0
    },
    {
      value: 3,
      label: '待收货',
      total: 0
    },
    {
      value: 4,
      label: '已完成',
      total: 0
    },
    {
      value: -1,
      label: '已取消',
      total: 0
    },
    {
      value: 7,
      label: '发货即将超时',
      total: 0
    },
    {
      value: 8,
      label: '24小时内待发货',
      total: 0
    },
    {
      value: 17,
      label: '礼物待领取',
      total: 0
    },
    {
      value: 9,
      label: '发货超时',
      type: 'timeout',
      total: 0
    },
    {
      value: 101,
      label: '分账冻结',
      total: 0
    },
    {
      value: 5,
      label: '售后中',
      total: 0
    },
    {
      value: 6,
      label: '售后完成',
      total: 0
    }
  ]

  const modalConfigData = {
    open: false,
    title: '',
    type: '',
    data: ''
  }
  const staticTitle = {
    remark: '平台备注',
    cancelOrder: '取消订单',
    deliverGoods: '发货',
    lookWl: '物流信息',
    area: '修改收货地址'
  }
  const remarkData = {
    remark: '',
    url: []
  }
  const cancelOrderData = {
    cancel_remark: '',
    cancel_type: undefined
  }
  const cancelTypeOptions = [
    {
      value: 1,
      label: '库存不足'
    },
    {
      value: 2,
      label: '订单地址不全'
    },
    {
      value: 3,
      label: '所在区域库存不足'
    },
    {
      value: 4,
      label: '买家不想买'
    },
    {
      value: 5,
      label: '与买家协商取消'
    },
    {
      value: 6,
      label: '其他'
    }
  ]
  const deliverGoodsData = {
    express_number: '',
    express_id: undefined
  }
  const areaFormData = {
    consignee: undefined,
    receive_phone: undefined,
    area_ids: undefined,
    address_detail: undefined
  }
  // 优惠券类型
  const couponSource = (type) => {
    let status = {
      1: '新客优惠券',
      2: '回流优惠券',
      3: '支付失败优惠券',
      4: '新客优惠券-微信卡券',
      5: '回流优惠券-微信卡券',
      7: '商品优惠券',
      8: '商品优惠券-微信卡券',
      9: '商品浏览优惠券'
    }
    return status[type] || ''
  }

  const isCallbackType = (type) => {
    let status = {
      0: '-未回传',
      1: '-已回传'
    }
    return status[type]
  }

  const orderType = (type) => {
    let status = {
      0: {
        color: '#F28C0C',
        text: '--'
      },
      1: {
        color: '#F28C0C',
        text: '待付款'
      },
      2: {
        color: '#FF4042',
        text: '待发货'
      },
      3: {
        color: '#118BCE',
        text: '待收货'
      },
      4: {
        color: '#404040',
        text: '已完成'
      },
      '-1': {
        color: '#404040',
        text: '已取消'
      },
      6: {
        color: '#B70000',
        text: '售后中'
      },
      7: {
        color: '#FF4042',
        text: '礼物待领取'
      }
    }
    return status[type]
  }

  const logisticsStatusEnums = {
    '401': '发货无信息',
    '403': '超时未更新',
    '402': '超时未签收',
    '412': '快递柜或驿站超时未取',
    '404': '拒收'
  }

  const customsState = (type) => {
    let status = {
      ORDER_PAYED_CUSTOMS_PENDING_CALLBACK: {
        color: '#F2A626',
        name: '待清关'
      },
      CUSTOMS_AUDIT_IM_PROCESSING_CALLBACK: {
        color: '#647DFF',
        name: '清关中'
      },
      CUSTOMS_AUDIT_IM_SUCCESS_CALLBACK: {
        color: '#222222',
        name: '清关成功'
      },
      CUSTOMS_AUDIT_IM_FAILURE_CALLBACK: {
        color: '#FE4042',
        name: '清关失败'
      }
    }

    return status[type]
  }

  const payCustomsState = (type) => {
    let status = {
      UNDECLARED: {
        color: '#F2A626',
        name: '未申报'
      },
      SUBMITTED: {
        color: '#1677ff',
        name: '申报已提交'
      },
      PROCESSING: {
        color: '#647DFF',
        name: '申报中'
      },
      SUCCESS: {
        color: '#222222',
        name: '申报成功'
      },
      FAIL: {
        color: '#FE4042',
        name: '申报失败'
      },
      EXCEPT: {
        color: '#FE4042',
        name: '申报异常'
      }
    }

    return status[type]
  }
  const initQuery = {
    page: 1,
    page_size: 10,
    shop_id: 0,
    urge_send: 0,
    status: 0,
    after_status: 0,
    un_split_status: 0,
    channel_type: 0,
    ad_id: '',
    ad_account_id: 0,
    coupon_type: 0,
    order_source: 0,
    created_at: `${moment().subtract(6, 'days').format('YYYY-MM-DD')}_${moment().format('YYYY-MM-DD')}`
  }

  return {
    ordersource,
    channelType,
    iconType,
    SEARCH_CONFIG_DATA,
    ACTIONS_DATA,
    modalConfigData,
    staticTitle,
    remarkData,
    cancelOrderData,
    cancelTypeOptions,
    deliverGoodsData,
    areaFormData,
    couponSource,
    isCallbackType,
    orderType,
    logisticsStatusEnums,
    customsState,
    payCustomsState,
    initQuery
  }
}
