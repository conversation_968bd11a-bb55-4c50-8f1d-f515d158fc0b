<template>
  <div class="saleTrend flex mt-16px mb-16px flex-wrap pa-24px">
    <div class="flex w-100% mb-15px">
      <div class="title comm_header_title flex-1">成交总金额涨跌分析</div>
      <a-radio-group v-model:value="state.value" button-style="solid">
        <a-radio-button
          :value="item.value"
          v-for="item in saleTrendTimes"
          :key="item.value"
          @click="onSwitch(item.value, 'value')"
          >{{ item.name }}</a-radio-button
        >
      </a-radio-group>
    </div>
    <a-radio-group v-model:value="state.type">
      <a-radio-button
        :value="item.value"
        v-for="item in saleTrendTypes"
        :key="item.value"
        @click="onSwitch(item.value, 'type')"
        >{{ item.name }}</a-radio-button
      >
    </a-radio-group>
    <Echarts
      id="sale_trend"
      :data="state.setOption"
      parentHeight="270px"
      style="width: 100%"
      @echartClick="handleBarClick"
    />
    <a-modal v-model:open="open" :title="`${title}信息`" :footer="null" width="756px">
      <div class="mt-16px mb-16px">
        <a-select
          v-model:value="sectionValue"
          placeholder="请选择涨幅区间"
          style="width: 256px"
          :options="sectionOptionsReverse.map(item => ({ ...item, label: `${item.key.toString().startsWith('-') ? '跌幅' : '涨幅'}${item.label}` }))"
          :fieldNames="{ label: 'label', value: 'key' }"
          @change="() => {
            tableData.pagination.current = 1
            getSaleTrendDetails()
          }"
          allow-clear
        ></a-select>
        <a-button class="ml-16px mr-6px" type="primary" @click="getSaleTrendDetails">查询</a-button>
        <a-button @click="resetSearch">重置</a-button>
      </div>
      <div class="mb-8px flex tabs_box">
        <a-radio-group v-model:value="timeValue" button-style="solid" class="flex-1">
          <a-radio-button
            :value="item.value"
            v-for="item in saleTrendTimes"
            :key="item.value"
            @click="timeValueSwitch(item.value)"
            >{{ item.name }}</a-radio-button
          >
        </a-radio-group>
        <div>{{ title }}数量：{{ saleTrendDetails.total_num }}
          <a-tooltip v-if="state.type=='product_code'">
            <template #title>列表仅展示top100{{ title }}信息</template>
            <QuestionCircleFilled style="color: #C5C6CC" class="m-l-4px" />
          </a-tooltip>
        </div>
      </div>
      <TableZebraCrossing :data="tableData" @change="pageChange">
        <template #headerCell="{ scope: { column } }">
          <template v-if="column.dataIndex === 'dimension_name'">{{ title }}名称</template>
          <template v-if="column.dataIndex === 'current_sale_amt'">
            <span>成交总金额(元)</span>
          </template>
          <template v-if="column.dataIndex === 'rate'">
            <span>同比</span>
            <a-tooltip>
              <template #title>同比=本期统计数据/上期统计周期数据</template>
              <QuestionCircleFilled style="color: #C5C6CC;" class="m-l-8px" />
            </a-tooltip>
          </template>
        </template>
        <template #bodyCell="{ scope }">
          <template v-if="scope.column.dataIndex === 'dimension_name'">
            <a-tooltip>
              <template #title>{{ state.type=='category_second_id' ? `${scope.record?.extra?.first_category} / ${scope.record?.extra?.second_category}` : scope.record.dimension_name  }}</template>
              <div class="dimension_name flex" v-if="state.type=='category_second_id'">
                <div class="category_second">{{ scope.record?.extra?.first_category  }}</div>
                <div class="category_second mx-2px">/</div>
                <div class="category_second">{{ scope.record?.extra?.second_category  }}</div>
              </div>
              <div class="dimension_name" v-else>{{ scope.record.dimension_name  }}</div>
            </a-tooltip>
            <div class="code mt-2px" v-if="scope.record?.extra?.code">商品ID：{{ scope.record?.extra?.code  }}</div>
          </template>
          <template v-if="scope.column.dataIndex === 'rate'">
            <div class="dimension_name">{{ scope.record.diff_amt  }}</div>
            <div class="code mt-2px" :style="{color: Number(scope.record.diff_amt)>=0?'#E63030':'#91CC75'}">
              <ArrowUpOutlined v-if="Number(scope.record.diff_amt)>=0" />
              <ArrowDownOutlined v-else />
              {{Number(scope.record.diff_amt)>=0?'上涨':'下降'}} {{ scope.record.rate  }}
            </div>
          </template>
        </template>
      </TableZebraCrossing>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
  import { ArrowUpOutlined, ArrowDownOutlined, QuestionCircleFilled } from '@ant-design/icons-vue';
  import { defineAsyncComponent, reactive, ref } from 'vue'
  import { saleTrendApi } from '../index.api'
  // 设置异步组件
  const Echarts = defineAsyncComponent(() => import('@/components/ui/common/Echarts.vue'))
  import datas from '../src/datas'
  const { saleTrendTimes, saleTrendTypes } = datas()
  const state = reactive({
    value: 'day',
    type: 'shop_id',
    setOption: {}
  })
  function onSwitch(v: number, key: string) {
    state[key] = v
    getSaleTrend()
  }
  const sectionOptions = ref<any[]>([])
  const sectionOptionsReverse = ref<any[]>([])
  async function getSaleTrend() {
    try {
      let res = await saleTrendApi({
        day_type: state.value,
        data_type: state.type,
      })
      let xData:string[] = [];
      let yData:number[] = [];
      sectionOptions.value = res.data.charts
      sectionOptionsReverse.value = JSON.parse(JSON.stringify(res.data.charts)).reverse()
      res.data.charts.forEach((item:any) => {
        xData.push(item.label)
        yData.push(item.value)
      })
      // 配置柱状图选项
      state.setOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
        },
        grid: {
          left: '5%',
          right: '4%',
          bottom: '7%'
        },
        xAxis: {
          type: 'category',
          data: xData
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: yData,
          type: 'bar',
          barWidth: 30,
          // 在柱状图上方显示 value 值
          label: {
            show: true,
            position: 'top',
            formatter: '{c}'
          },
          itemStyle: {
            normal: {
              color: function(params: any) {
                const dataLength = xData.length;
                const halfLength = Math.floor(dataLength / 2);
                if (params.dataIndex < halfLength) {
                  return '#91cc75';
                } else if (params.dataIndex === halfLength && dataLength % 2 === 1) {
                  return '#edeff0';
                } else {
                  return '#e57b78';
                }
              }
            }
          }
        }]
      };
    } catch (error) {
      console.error('获取销售趋势数据失败:', error);
    }
  }
  getSaleTrend()
  const open = ref<boolean>(false)
  const title = ref<string>('')
  const sectionValue = ref<string | null>(null)
  const timeValue = ref<string>('')
  const field = ref<string>('')
  const order = ref<string>('')
  function handleBarClick(params:any) {
    title.value = `${saleTrendTypes.find((item:any) => item.value === state.type).name}`
    sectionValue.value = sectionOptions.value[params.dataIndex].key
    timeValue.value = state.value
    open.value = true
    tableData.pagination.current = 1
    getSaleTrendDetails()
  }
  const saleTrendDetails = ref<any>({})
  const tableData = reactive({
    bordered: true,
    dataSource: [],
    loading: false,
    scroll: {
      scrollToFirstRowOnChange: true,
      y: 452
    },
    columns: [
      {
        title: '商品名称',
        dataIndex: 'dimension_name',
        width: 263
      },
      {
        title: '成交总金额(元)',
        dataIndex: 'current_sale_amt',
        align: 'left',
        width: 141,
        sorter: true,
      },
      {
        title: '上期成交总金额(元)',
        dataIndex: 'last_sale_amt',
        align: 'left',
        width: 157,
        sorter: true,
      },
      {
        title: '同比',
        dataIndex: 'rate',
        align: 'left',
        width: 124,
        sorter: true,
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      total: 0,
      showSizeChanger: true,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total: number) => `共 ${total} 条`
    }
  })
  async function getSaleTrendDetails() {
    try {
      let res = await saleTrendApi({
        day_type: timeValue.value,
        data_type: state.type,
        is_detail: 1,
        selected: sectionValue.value,
        page: tableData.pagination.current,
        page_size: tableData.pagination.pageSize,
        field: field.value,
        order: order.value
      })
      saleTrendDetails.value = res.data
      tableData.dataSource = res.data?.list || []
      tableData.pagination.total = res.data.total_num
    } catch (error) {
    }
  }
  function timeValueSwitch(value: string) {
    tableData.pagination.current = 1
    timeValue.value = value
    getSaleTrendDetails()
  }
  function pageChange(pagination: any, _filters: any, sorter: any) {
    if (sorter?.order && Object.keys(sorter).length) {
      field.value = sorter.field
      order.value = !sorter.order ? '' : sorter.order === 'ascend' ? 'asc' : 'desc'
      tableData.pagination.current = 1
    } else {
      field.value = ''
      order.value = ''
      tableData.pagination.current = pagination.current
    }
    tableData.pagination.pageSize = pagination.pageSize
    getSaleTrendDetails()
  }
  function resetSearch() {
    sectionValue.value = null
    tableData.pagination.current = 1
    getSaleTrendDetails()
  }
</script>
<style lang="scss" scoped>
  .saleTrend {
    background: #fff;
    width: 100%; ////先100%，等对比图放开再50%打开
    position: relative;
    border-radius: 8px;
    .title {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 18px;
    }
  }
  .tabs_box {
    align-items: center;
    font-size: 14px;
    color: #2B2B2B;
    line-height: 20px;
  }
  .dimension_name {
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    max-width: 15em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .category_second {
      max-width: 7em;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .code {
    font-size: 12px;
    color: #626366;
    line-height: 17px;
  }
</style>