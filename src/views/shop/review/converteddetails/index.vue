<template>
  <DesTablePage class="page_main common_page_warp common_card_wrapper">
    <template #title>
      <div>转化明细</div>
    </template>
    <template #extra
      ><a-button v-auth="['shopConverteddetailsExport']" type="primary" @click="handleExportCreate"
        >导出</a-button
      ></template
    >
    <template #tableWarp>
      <div class="timer_search flex">
        <a-radio-group v-model:value="data.active" button-style="solid" @change="onSwitch(data.active)">
          <a-radio-button :value="item.value" v-for="item in shopTimes" :key="item.value">{{
            item.name
          }}</a-radio-button>
        </a-radio-group>
        <div class="search_picker">
          <a-range-picker
            v-model:value="data.defaultTime"
            valueFormat="YYYY-MM-DD"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :presets="getDatePresetsOptions()"
          />
        </div>
      </div>
      <SearchBaseLayout :data="searchList" @changeValue="changeValue" :actions="actions" />
      <TableZebraCrossing :data="tableData" @change="pageChange" class="mt-16px">
        <template #headerCell="{ scope }">
          <template v-if="scope?.column?.dataIndex === 'operation'">
            <a-space>
              <span>{{ scope?.column?.title }} </span>
              <!-- <span>
              <SetTableColumns class="cursor-pointer" v-model:data="tableData.columns" :column="columns" />
            </span> -->
            </a-space>
          </template>
        </template>
        <template #bodyCell="{ scope }">
          <template v-if="scope.column.key === 'ad_name'">
            <div>
              <a-tooltip>
                <template #title>
                  <div>{{ scope.record.ad_name }}</div>
                </template>
                <a-space style="display: flex">
                  <div class="item-warp-img">
                    <img v-if="scope.record.channel_type === 1" :src="requireImg('order/o8.png')" alt="" />
                    <img v-if="scope.record.channel_type === 6" :src="requireImg('order/o6.png')" alt="" />
                    <img v-if="scope.record.channel_type === 4" :src="requireImg('order/o1.png')" alt="" />
                    <img v-if="scope.record.channel_type === 8" :src="requireImg('order/o9.png')" alt="" />

                    <!-- 小程序 -->
                    <img
                      style="margin-left: 4px"
                      v-if="scope.record.type === 1"
                      :src="requireImg('order/min_icon.png')"
                      alt=""
                    />
                    <!--拼多多  -->
                    <img
                      style="margin-left: 4px"
                      v-if="scope.record.type === 2"
                      :src="requireImg('order/pdd_icon.png')"
                      alt=""
                    />
                  </div>
                  <div class="text_overflow w150px">{{ scope.record.ad_name }}</div>
                </a-space>
              </a-tooltip>
              <div class="number-id product_id">创意ID：{{ scope.record.creative_id || '--' }}</div>
              <div class="number-id product_id">计划ID：{{ scope.record.tripartite_ad_id || '--' }}</div>
              <div class="number-id product_id flex justify-between">
                <span>账户ID：{{ scope.record.ad_account_id || '--' }}</span>
                <a-tooltip>
                  <template #title>广告账户授权异常，会导致报表数据错误 </template>
                  <ExclamationCircleOutlined v-if="scope.record.is_authorized == 1" class="c-#e63030 mr-20px ml-5px" />
                </a-tooltip>
              </div>
            </div>
          </template>
          <template v-if="scope.column.key === 'product_name'">
            <a-space direction="vertical">
              <a-tooltip>
                <template #title>
                  <div>{{ scope.record.product_name }}</div>
                  <div>{{ scope.record.product_code }}</div>
                </template>
                <div class="product_name text_overflow w125px" @click="onLinkGood(scope.record)">
                  {{ scope.record.product_name }}
                </div>
                <div class="number-id product_id">商品ID:{{ scope.record.product_code }}</div>
              </a-tooltip>
            </a-space>
          </template>
          <template v-if="scope.column.key === 'transform_status'">
            <span>{{ statusData[scope.record.status] || '--' }}</span>
            <check-circle-two-tone two-tone-color="#52c41a" v-if="scope.record.num > 1" />
          </template>
          <template v-if="scope.column.key === 'order_deatil'">
            <template
              v-if="
                scope.record.order_type !== 4 ||
                (scope.record.order_type === 4 && ![12, 13].includes(scope.record.status))
              "
            >
              <template v-if="scope.record.order_num">
                <div>
                  <span>下单金额:</span>
                  <span>￥{{ centsToYuan(scope.record.order_amount) }}</span>
                </div>
                <div>
                  <span>订单编号:</span>
                  <div v-if="scope.record.type == 2">
                    {{
                      scope.record.order_sns && scope.record.order_sns.length
                        ? scope.record.order_sns.join(',')
                        : scope.record.order_num
                    }}
                  </div>
                  <span class="product_name" v-else @click="onLinkOrder(scope.record)">
                    {{ scope.record.order_num }}
                  </span>
                </div>
              </template>
              <template v-else>--</template>
            </template>
            <template v-else> -- </template>
          </template>
          <template v-if="scope.column.key === 'device_type'">
            <span>{{ scope.record.device_type || '--' }}</span>
          </template>
          <template v-if="scope.column.key === 'visit_time'">
            <span>{{ scope.record.visit_time || '--' }}</span>
          </template>
          <template v-if="scope.column.key === 'transform_time'">
            <span>{{ scope.record.transform_time || '--' }}</span>
          </template>
          <template v-if="scope.column.key === 'operation'">
            <a-button
              class="pa-0"
              v-if="scope.record.status == 2 && scope.record.channel_type == 1"
              type="link"
              v-auth="['converteddetailsCopy']"
              @click="onbuttonCopy(scope.record)"
              >复制回传信息</a-button
            >
            <!-- <a-space>
              <a-button
                type="link"
                v-if="![2, 5].includes(scope.record.status) && scope.record.num == 0"
                @click="onBackHaul(scope.record)"
                >手动回传</a-button
              >
              <a-button
                type="link"
                v-if="scope.record.channel_type == 1 && [2, 5].includes(scope.record.status) && scope.record.num == 1"
                @click="onBackHaul(scope.record)"
                >再次回传</a-button
              >
            </a-space> -->
          </template>
        </template>
      </TableZebraCrossing>
    </template>
  </DesTablePage>
</template>
<script setup lang="ts">
  import moment from 'moment'
  import { requireImg, centsToYuan, getDatePresetsOptions, copy } from '@/utils'
  import { exportCreate } from '@/api/common'
  import { setList, adCallBack, callbackInfo } from './index.api'
  import { h, ref, reactive, watch, createVNode } from 'vue'
  import datas from './src/data'
  import { useTheme } from '@/hooks'
  import { message, Modal, notification } from 'ant-design-vue'
  import { CheckCircleTwoTone, ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { useRouter } from 'vue-router'
  const { themeVar } = useTheme()
  // const { goCenter } = useDownloadCenter()
  const { columns, searchList, statusData, getShopList, getCompanyList } = datas()
  const router = useRouter()
  const shopTimes = reactive([
    {
      name: '今日',
      value: 1
    },
    {
      name: '昨日',
      value: 2
    },
    {
      name: '近7日',
      value: 3
    },
    {
      name: ' 近30日',
      value: 4
    }
  ])
  const data = reactive({
    active: 1,
    params: {
      page: 1,
      page_size: 10,
      begin_time: '',
      end_time: ''
    },
    defaultTime: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
  })
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 6
    }
  }
  // 表格数据
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: 1600
    },
    dataSource: [],
    loading: false,
    columns: columns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })
  // 获取列表
  const getList = async () => {
    try {
      tableData.loading = true
      let params = {
        ...data.params,
        admin_id: data.params?.admin_id && data.params?.admin_id.length != 0 ? data.params.admin_id[1] : undefined
      }
      let res = await setList(params)
      tableData.dataSource = res.data?.list || []
      tableData.pagination.total = res.data.total_num || 0
      tableData.pagination.current = res.data.page || 1
      // const _options = (res.data?.admin_list || []).map((v) => {
      //   return {
      //     value: v.key,
      //     label: v.realname
      //   }
      // })
      // searchList[4].props.options = [...searchList[4].props.options, ..._options]
    } catch (error) {
      console.log(error)
    } finally {
      tableData.loading = false
    }
  }
  getShopList(searchList)
  getCompanyList(searchList)

  // 跳转至订单详情
  const onLinkOrder = (row) => {
    // type：1小程序  2拼多多
    if ([2].includes(row.type)) {
      // router.push({ name: 'CidOrderIndex', query: { order_num: row.order_num } })
      router.push({ name: 'CidPddOrder', query: { order_num: row.order_num } })
    } else if (row.type == 3) {
      router.push({ name: 'CidTbOrder', query: { order_num: row.order_num } })
    } else {
      router.push({
        path: '/shop/order/order_details',
        query: row?.sub_order_num
          ? {
              id: row?.order_id,
              active_type: row?.activity_type,
              sub_order_num: row?.sub_order_num
            }
          : {
              id: row?.order_id,
              active_type: row?.activity_type
            }
      })
    }
  }

  // 跳转至商品
  const onLinkGood = (row) => {
    if (row.type == 2) {
      // router.push({ name: 'CidGoodsIndex', query: { product_name: row.product_name } })
      router.push({ name: 'CidPDDGoods', query: { product_name: row.product_name } })
    } else if (row.type == 3) {
      router.push({ name: 'CidTbGoods', query: { product_name: row.product_name } })
    } else {
      router.push({ path: '/shop/goods/goods-detail', query: { id: row.product_id } })
    }
  }

  const onBackHaul = async (row) => {
    if (!row.click_id) return message.warning('缺失click_id')
    try {
      Modal.confirm({
        title: '提示',
        content: createVNode('div', {}, '是否上报此次订单转化行为?'),
        async onOk() {
          let res = await adCallBack({ id: row.id })
          message.success(res.msg || '回传成功')
          getList()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const onSwitch = (v) => {
    data.params.page = 1
    data.params.page_size = 10
    switch (v) {
      case 1:
        data.defaultTime = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
        break
      case 2:
        data.defaultTime = [
          moment().subtract(1, 'days').format('YYYY-MM-DD'),
          moment().subtract(1, 'days').format('YYYY-MM-DD')
        ]
        break
      case 3:
        data.defaultTime = [
          moment().add(-6, 'd').format('YYYY-MM-DD'),
          moment().subtract(0, 'days').format('YYYY-MM-DD')
        ]
        break
      case 4:
        data.defaultTime = [
          moment().add(-29, 'd').format('YYYY-MM-DD'),
          moment().subtract(0, 'days').format('YYYY-MM-DD')
        ]
        break
    }
  }
  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }
  const changeValue = (v) => {
    if (v.status) {
      data.params = {
        ...data.params,
        ...v.formData,
        page: 1
      }
      getList()
    } else {
      data.params = {
        page: 1,
        page_size: 10,
        begin_time: moment().format('YYYY-MM-DD'),
        end_time: moment().format('YYYY-MM-DD')
      }
      data.defaultTime = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      data.active = 1
    }
  }
  // 导出
  const handleExportCreate = async () => {
    try {
      await exportCreate({
        type: 'ad_transform_info',
        params: JSON.stringify(data.params)
      })
      // goCenter('DownloadCenter', 'DownloadCenter')
      notification.success({
        message: '导出成功',
        duration: 2,
        description: '请前往系统->下载中心查看',
        style: {
          color: themeVar.value.primaryColor,
          cursor: 'pointer'
        },
        onClick: () => router.push({ name: 'download' })
      })
    } catch (error) {
      console.log(error)
    }
  }
  //复制回传信息
  const onbuttonCopy = async (row) => {
    try {
      let { data } = await callbackInfo({ id: row.id })
      let time = data.reply_time ? moment(data.reply_time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-'
      let content = `
     数据源ID：${data.user_set_action_id || '-'}
     转化ID：${data.id || '-'}
     小程序appid： ${data.app_id || '-'}
     click_id：${data.click_id || '-'}
     上报时间：${time || '-'}
     请求参数：
     ${data.callback_info || '-'}
     `
      copy(content)
    } catch (error) {
      console.error(error)
    }
  }

  const changEvent = (data) => {
    console.log(data, 9999)
  }
  watch(
    () => data.defaultTime,
    (vld) => {
      if (vld) {
        data.params.page = 1
        data.params.page_size = 10
        data.params.begin_time = vld[0]
        data.params.end_time = vld[1]
        getList()
      }
    },
    {
      immediate: true
    }
  )
  // getList()
</script>

<style lang="scss" scoped>
  .timer_search {
    padding: 0 0 20px 0;
    box-sizing: border-box;
    justify-content: flex-end;
    flex-wrap: wrap;
    .search_picker {
      border-radius: 6px;
      height: 30px;
      background-color: #f0f2f6;
      margin-left: 20px;
    }
  }
  .product_id {
    // margin-top: v-bind('themeVar.marginSmall');
    width: 190px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    font-family: PingFang SC;
    color: #242f5799;
    line-height: 1.5;
  }
  .product_name {
    font-size: 14px;
    color: v-bind('themeVar.infoColor');
    line-height: 24px;
    cursor: pointer;
  }
  @import './src/assets/css/mixin_scss_fn';
  .description-warp {
    border: none;
    @include set_border_radius(--border-radius);
  }
  .column-user-img {
    @include set_node_whb(30px, 30px);
  }
  .description-text {
    @include set_font_config(--font-size-mini, --text-color-gray);
  }
  .description-pl-title span:nth-child(1) {
    @include set_font_config(--font-size-huge, --text-color-base);
  }
  .description-pl-title span:nth-child(2) {
    padding-left: var(--padding-medium);
    @include set_font_config(--font-size, --text-color-gray);
  }
  .tips {
    font-size: 12px;
    font-family: PingFang SC;
    color: v-bind('themeVar.textColorGray');
    line-height: 16px;
  }

  .item-warp-img {
    margin-top: 3px;
    display: flex;
    //修改
    img {
      display: block;
      width: 15px;
      height: 15px;
    }
  }
</style>
