<template>
  <a-space direction="vertical" class="w-full">
    <SearchBaseLayout :data="adSearch" @changeValue="changeValue" :actions="actions" class="m-b-8px" />
    <a-table
      :scroll="{ y: 300 }"
      :columns="state.columns"
      :data-source="state.dataSource"
      :loading="state.loading"
      :pagination="state.pagination"
      @change="changePage"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'ad_url'">
          <div>
            <a-tooltip>
              <template #title>{{ record.name }}</template>
              <span class="text_overflow block">广告名称：{{ record.name }}</span>
            </a-tooltip>
          </div>
          <div v-if="record.ad_account_id">
            <a-space :size="[6, 0]">
              <span>账户ID:</span>
              <span class="text_overflow">{{ record.ad_account_id || '--' }}</span>
            </a-space>
          </div>
          <div class="flex">
            <div>
              {{ record.wechat_app_name || '--' }}
            </div>
            <div class="flex_column">
              <div>
                <span style="margin-left: 8px">APPID: {{ record.wechat_app_id }}</span>
                <CopyOutlined class="copy_icon" @click="copy(record.wechat_app_id)" />
              </div>
              <div>
                <span style="margin-left: 8px">原始ID: {{ record.original_id }}</span>
                <CopyOutlined class="copy_icon" @click="copy(record.original_id)" />
              </div>
            </div>
          </div>
          <div class="flex_center" v-if="record.mini_page">
            <a-tooltip>
              <template #title>{{ record.mini_page }}</template>
              <span class="text_overflow block"
                >{{ record.platform_id === 5 ? '小程序路径 : ' : '' }} {{ record.mini_page }}</span
              >
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.mini_page)" />
          </div>
          <!-- <div class="flex_center" v-if="record.ad_url">
              <a-tooltip>
                <template #title>{{ record.ad_url }}</template>
                <span class="text_overflow block">
                  {{ record.platform_id === 5 ? '落地页(一跳) : ' : '' }}{{ record.ad_url }}</span
                >
              </a-tooltip>
              <CopyOutlined class="copy_icon" @click="copy(record.ad_url)" />
            </div> -->
          <div class="flex" v-if="record.landing_page_url">
            <a-tooltip>
              <template #title>{{ record.landing_page_url }}</template>
              <span class="text_overflow block max-w-360px">
                落地页地址： <span>{{ record.landing_page_url }}</span>
              </span>
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.landing_page_url)" />
          </div>

          <div class="flex" v-if="record.direct_url">
            <a-tooltip>
              <template #title>{{ record.direct_url }}</template>
              <span class="text_overflow block max-w-360px">
                直达链接： <span>{{ record.direct_url }}</span>
              </span>
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.direct_url)" />
          </div>
          <div class="flex" v-if="record.ad_url">
            <a-tooltip>
              <template #title>{{ record.ad_url }}</template>
              <span class="text_overflow block w-360px">
                小程序链接： <span>{{ record.ad_url }}</span>
              </span>
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.ad_url)" />
          </div>
          <div class="flex" v-if="record.report_url">
            <a-tooltip>
              <template #title> {{ record.report_url }}</template>
              <span class="text_overflow block"> 报备链接：{{ record.report_url }}</span>
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.report_url)" />
          </div>
          <div class="flex_align_center">
            <img
              :src="
                [0, 3, 4].includes(record.jump_type)
                  ? requireImg('goods/icon_app.png')
                  : requireImg('goods/icon_h5.png')
              "
              style="margin-right: 10px"
            />
            <span class="item-tag item-blue">{{ record.wechat_landing_type == 1 ? '图文落地页' : '视频落地页' }}</span>
            <template v-if="record.platform_id == 1">
              <span class="item-tag item-green"> {{ platform_text[record.platform_id] }}</span>
              <span class="item-tag item-green">微信支付</span>
            </template>
            <template v-else>
              <span class="item-tag item-cili"> 快手广告</span>
              <span class="item-tag item-cili">{{ record.jump_type == 3 ? '二跳小程序' : '快手磁力建站' }}</span>
            </template>
            <span v-if="record.h5_landing_page_audit" class="item-tag item-green"
              >{{ record.h5_landing_page_audit }} {{ record.h5_landing_page_version }}</span
            >
          </div>
        </template>
        <template v-if="column.dataIndex === 'callback_ratio'">
          <div v-if="record.platform_id !== 4">回传{{ record.callback_ratio }}%</div>
          <template v-else>
            <div v-if="record.callback_type === 1">回传{{ record.callback_ratio }}%</div>
            <div v-else>按金额区间回传</div>
            <div>
              {{
                {
                  1: '整单金额',
                  2: '单件金额',
                  3: '固定金额',
                  4: '固定比例',
                  5: '金额区间'
                }[record.callback_amount_type]
              }}回传
            </div>
          </template>
        </template>
        <template v-if="column.dataIndex === 'created_at'">
          {{ record.created_at ? formatDate(record.created_at * 1000) : '' }}
        </template>
        <template v-if="column.dataIndex === 'style_type'">
          {{ styleType2Name(record.style_type) || '拼多多版' }}
        </template>
        <template v-if="column.dataIndex === 'handle'">
          <div class="handle_btns">
            <div class="flex">
              <div v-click-outside="() => (record.popViseble = false)" v-if="record.jump_type == 0">
                <a-popover
                  v-model:open="record.popViseble"
                  trigger="click"
                  :overlayInnerStyle="{ width: '200px', padding: '15px', fontSize: '16px' }"
                >
                  <template #title>
                    <div @click.stop="record.popViseble = true">
                      <div class="flex_ju_sp pop_title">
                        <span>落地页预览</span>
                        <img
                          class="close"
                          src="@/assets/icon/close.png"
                          alt=""
                          @click.stop="record.popViseble = false"
                        />
                      </div>
                      <div class="text_align_center">
                        <img
                          :src="state.preview_url"
                          style="min-width: 150px; min-height: 150px"
                          alt=""
                          v-if="state.preview_url"
                        />
                        <img
                          src="@/assets/images/empty/no_content.png"
                          style="width: 150px; height: 150px"
                          alt=""
                          v-else
                        />
                        <p>{{ state.preview_url ? '手机微信扫码预览' : '二维码加载中' }}</p>
                      </div>
                    </div>
                  </template>
                  <a-button type="link" @click="onPreview(record)">预览</a-button>
                </a-popover>
              </div>
            </div>
          </div>
        </template>
      </template>
    </a-table>
  </a-space>
</template>

<script setup>
  import datas from '../data'
  import { onMounted, reactive } from 'vue'
  import { requireImg, formatDate, copy } from '@/utils'
  import { CopyOutlined, ExclamationCircleOutlined, FormOutlined } from '@ant-design/icons-vue'
  import { get_ad_list, get_mini_code, getAddList } from '../index.api'
  const props = defineProps(['data', 'date'])
  const { adSearch } = datas()
  const state = reactive({
    columns: [
      {
        title: '广告投放链接',
        dataIndex: 'ad_url',
        key: 'ad_url'
      },
      {
        title: '落地页版本',
        dataIndex: 'style_type',
        key: 'style_type',
        width: 120
      },
      {
        title: '券后订单回传',
        dataIndex: 'callback_ratio',
        key: 'callback_ratio',
        width: 185
      },
      {
        title: '转化数',
        dataIndex: 'conver_num',
        key: 'conver_num',
        width: 80
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180
      },
      {
        title: '操作',
        dataIndex: 'handle',
        key: 'handle',
        width: 150
      }
    ],
    dataSource: [],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    preview_url: null,
    loading: false,
    query: {
      page: 1,
      page_size: 10,
      product_id: props.data.product_id || 2801, // 2801,
      useShop: 'shop_id', //店铺id
      jump_type: -1,
      begin_time: props.date.begin_time,
      end_time: props.date.end_time
    },
    formData: null
  })
  const changeValue = (values) => {
    let params = { ...values.formData, page: 1 }
    if (values.formData?.style_type?.length) {
      params.style_type = params.style_type.join(',')
    } else {
      params.style_type = undefined
    }
    if (!values.status) {
      params.jump_type = -1
      values.formData.jump_type = -1
    }
    state.formData = params
    initPage()
  }
  const styleType2Name = (type) => {
    let obj = {
      1: '大众版',
      2: '老年版',
      3: '淘宝版',
      4: '拼多多版'
    }
    return obj[type]
  }
  if (props.data.product_type == 2) {
    adSearch.value = adSearch.value.filter((it) => it.field !== 'style_type')
  }
  const changePage = (page) => {
    state.query.page = page.current
    state.query.page_size = page.pageSize
    state.pagination.current = page.current
    state.pagination.pageSize = page.pageSize
    if (state.formData) {
      state.formData.page = page.current
    }
    initPage()
  }
  const onPreview = async (item) => {
    ;(state.dataSource || []).forEach((v) => {
      if (v.id == item.id) {
        v.popViseble = item.popViseble == true ? false : true
      } else {
        v.popViseble = false
      }
    })
    try {
      state.preview_url = null
      const resp = await get_mini_code({ id: item.id, useShop: 'shop_id' })
      state.preview_url = resp.data.image
    } catch (error) {
      console.error(error)
    }
  }
  const platform_text = {
    1: '腾讯广告',
    4: '磁力引擎',
    5: 'Bilibili'
  }
  const initPage = async () => {
    state.loading = true
    let result
    if (props.data.product_type == 2) {
      //拼多多商品广告
      result = await getAddList({ ...state.query, ...state.formData, product_prim_id: props.data.product_id, from: 1 })
    } else {
      result = await get_ad_list({ ...state.query, ...state.formData, from: 1 })
    }
    console.log(result)
    state.dataSource = result?.data?.list || []
    state.pagination.current = result?.data.page
    state.pagination.pageSize = result.data.size
    state.pagination.total = result.data.total_num
    state.loading = false
  }
  onMounted(() => {
    initPage()
  })
</script>

<style scoped lang="scss">
  @import './src/assets/css/mixin_scss_fn.scss';
  :deep(.el-divider--horizontal) {
    margin: 10px 0;
  }
  :deep(.ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr > th) {
    font-size: 12px;
  }
  :deep(.ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr > th) {
    padding: 14px 16px;
  }

  // .text_overflow {
  //   @include text_overflow(2);
  // }

  .icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .pop_content {
    .tips {
      color: #999;
      margin: 5px 0;
    }

    .mar {
      margin: 5px 0;
    }
  }

  .ad_name {
    .name {
      margin-right: 30px;
    }
  }

  .copy_icon {
    margin-left: 10px;
    cursor: pointer;
    color: var(--el-color-primary);
  }

  :deep(.el-table) {
    border-radius: 10px;
    margin-top: 20px;
  }

  :deep(.el-dialog__body) {
    padding: 0 30px 30px 30px !important;
  }

  :deep(.el-table .cell) {
    padding: 0 12px;
    color: #080f1e;
  }

  :deep(.el-popper.is-dark) {
    max-width: 450px;
    line-height: 1.4;
    color: #fff;
    font-weight: 400;
    word-break: break-all;
  }
  :deep(.custom-searchForm) {
    margin-top: 20px;
    margin-bottom: 20px;
    .comp_searchForm {
      .form_item {
        width: 180px;
      }
      .kk_btn_group {
        display: none;
      }
    }
  }

  .pop_title {
    margin-bottom: 10px;

    .close {
      cursor: pointer;
    }
  }
  .item-blue {
    border: 1px solid #3f9fff;
    color: #3f9fff;
  }
  .item-green {
    border: 1px solid #70b606;
    color: #70b606;
  }
  .item-cili {
    border: 1px solid #fe4a08;
    color: #fe4a08;
  }
  .item-tag {
    border-radius: 5px;
    font-size: 12px;
    padding: 0 6px;
    margin-right: 16px;
  }
  .space_between {
    justify-content: space-between;
  }

  .text_align_center {
    text-align: center;
  }

  .handle_btns {
    user-select: none;

    span {
      color: var(--el-color-primary);
      cursor: pointer;
      margin-right: 10px;
    }
  }

  .copy_icon {
    margin-left: 10px;
    cursor: pointer;
    color: var(--el-color-primary);
  }

  .divider {
    margin-top: 5px;
    padding-bottom: 5px;
    border-bottom: var(--el-border);

    &:nth-last-of-type(1) {
      border-bottom: none;
    }
  }
</style>
