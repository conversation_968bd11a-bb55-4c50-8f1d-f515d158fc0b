<template>
  <div class="warp">
    <SearchBaseLayout ref="searchFormDataRef" class="mb-16px" :data="searchList" @changeValue="changeValue" />
    <a-table
      sticky
      :columns="fixedColumns"
      :data-source="state.dataSource"
      :pagination="state.pagination"
      :loading="state.loading"
      :scroll="{ x: 2000 }"
      :rowKey="(record) => record.key"
      :sortDirections="['descend', 'ascend']"
      @change="pageChange"
      :showSorterTooltip="false"
      bordered
      size="small"
    >
      <template #headerCell="{ column }">
        <template v-if="headerCell.includes(column.dataIndex)">
          <div>
            <span>{{ column.title }}</span>
            <a-tooltip>
              <template #title>{{ column.text }}</template>
              <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
            </a-tooltip>
          </div>
        </template>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'product_type'">
          <div class="flex">
            <img :src="getProductType(record.product_type)" alt="" class="w-16px h-16px" />
          </div>
        </template>
        <template v-if="column.dataIndex === 'product_name'">
          <div class="item-warp">
            <div class="item-warp-img">
              <a-image
                class="img"
                style="width: 34px; height: 34px; border-radius: 2px"
                :src="record.product_image"
                alt=""
              />
              <!-- <img :src="requireImg('order/o8.png')" alt="" /> -->
            </div>
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>{{ record.product_name || '--' }}</template>
                  <span class="item-warp-content-title">{{ record.product_name || '--' }}</span>
                </a-tooltip>
              </div>
              <div class="item-warp-content-code">ID：{{ record.product_code || '--' }}</div>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'product_remark'">
          <div class="item-warp">
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>{{ record.product_remark }}</template>
                  <span class="item-warp-content-title">{{ record.product_remark || '--' }}</span>
                </a-tooltip>
              </div>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'shop_name'">
          <div class="item-warp">
            <!--            <div class="item-warp-img">-->
            <!--              &lt;!&ndash;              <img :src="record.logo" alt="" />&ndash;&gt;-->
            <!--            </div>-->
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>
                    <div>店铺名称：{{ record.shop_name || '--' }}</div>
                    <div>所属公司：{{ record.company_name || '--' }}</div>
                  </template>
                  <div class="item-warp-content-title">店铺名称：{{ record.shop_name || '--' }}</div>
                  <div class="item-warp-content-title item-warp-content-code">
                    所属公司：{{ record.company_name || '--' }}
                  </div>
                </a-tooltip>
              </div>
              <!--              <div class="item-warp-content-code">ID：{{ record.shop_id || '&#45;&#45;' }}</div>-->
            </div>
          </div>
        </template>

        <template v-if="column.dataIndex === 'complaint_rate'">
          <span>{{ record.complaint_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount'">
          <span>¥ {{ centsToYuan(record.order_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount_turnover'">
          <span>¥ {{ centsToYuan(record.order_amount_turnover) }}</span>
        </template>
        <template v-if="column.dataIndex === 'product_loss_roi'">
          <span class="loss_roi"> {{ record.product_loss_roi }}</span>
        </template>
        <template v-if="column.dataIndex === 'not_callback_amount'">
          <span>¥ {{ centsToYuan(record.not_callback_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'cost'">
          <span>¥ {{ centsToYuan(record.cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'product_loss_amount'">
          <span>¥ {{ centsToYuan(record.product_loss_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'thousand_display_price'">
          <span>¥ {{ centsToYuan(record.thousand_display_price) }}</span>
        </template>

        <template v-if="column.dataIndex === 'click_order_amount_avg'">
          <span>¥ {{ centsToYuan(record.click_order_amount_avg) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount_avg'">
          <span>¥ {{ centsToYuan(record.order_amount_avg) }}</span>
        </template>

        <template v-if="column.dataIndex === 'conversions_cost'">
          <span>¥ {{ centsToYuan(record.conversions_cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'refund_amount'">
          <span>¥ {{ centsToYuan(record.refund_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'pay_time_refund_amount'">
          <span>¥ {{ centsToYuan(record.pay_time_refund_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'pay_time_refund_rate'">
          <span>{{ record.pay_time_refund_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'fast_refund_ratio'">
          <span>{{ record.fast_refund_ratio || 0 }}%</span>
        </template>
        <template v-if="column.dataIndex === 'refund_rate'">
          <span>{{ record.refund_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'conversions_rate'">
          <span>{{ record.conversions_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'ctr'">
          <span>{{ record.ctr }}%</span>
        </template>
        <template v-if="column.dataIndex === 'arrival_rate'">
          <span>{{ record.arrival_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'order_conversion_rate'">
          <span>{{ record.order_conversion_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'pay_conversion_rate'">
          <span>{{ record.pay_conversion_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'weixin_today_rate'">
          <span>{{ record.weixin_today_rate || 0 }}%</span>
        </template>
        <template v-if="column.dataIndex === 'mini_today_rate'">
          <span>{{ record.mini_today_rate || 0 }}%</span>
        </template>
        <template v-if="column.dataIndex === 'platform_today_rate'">
          <span>{{ record.platform_today_rate || 0 }}%</span>
        </template>
        <template v-if="column.dataIndex === 'create_order_rate'">
          <span>{{ record.create_order_rate || 0 }}%</span>
        </template>

        <template v-if="column.dataIndex === 'updated_at'">
          <span>{{ record.updated_at || '--' }}</span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import datas from '../data'
  import { centsToYuan, requireImg, randomWord } from '@/utils'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import { get_statement_product_latitude } from '../index.api'
  import moment from 'moment'
  import { useApp, useRouter } from '@/hooks'

  const { isMobile, useInfo } = useApp()
  const props = defineProps(['data', 'after_sale_dimension'])
  const emits = defineEmits(['eventBtn', 'getDimension'])
  const { setFilder, headerCell } = datas()
  const getProductType = (type) => {
    const productData = {
      1: 'datacenter/icon_applet_check.png',
      2: 'datacenter/icon_pdd_check.png',
      3: 'datacenter/icon_tb_check.png',
      4: 'datacenter/icon_jd_check.png'
    }
    return requireImg(productData[type])
  }
  const searchList = ref([
    {
      type: 'input.text',
      field: 'product_code',
      value: undefined,
      props: {
        placeholder: '请输入商品ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    },

    {
      type: 'input.text',
      field: 'product_name',
      value: undefined,
      props: {
        placeholder: '请输入商品名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 4
      }
    }
  ])
  const fixedColumns = ref([
    {
      title: '商品信息',
      dataIndex: 'product_name',
      fixed: isMobile.value ? false : 'left',
      width: 240
    },

    {
      title: '店铺信息',
      dataIndex: 'shop_name',
      fixed: isMobile.value ? false : 'left',
      width: 240
    },
    {
      title: '总消耗',
      dataIndex: 'cost',
      width: 140,
      text: '广告投放总共付出的费用成本',
      sorter: true
    },
    {
      title: '转化数',
      dataIndex: 'conversions_count',
      width: 120,
      sorter: true
    },

    {
      title: '支付ROI',
      dataIndex: 'roi',
      width: 120,
      sorter: true
    },
    {
      title: '支付订单数',
      dataIndex: 'order_count',
      width: 120,
      text: '支付成功的订单数量',
      sorter: true
    },
    {
      title: '支付总金额',
      dataIndex: 'order_amount',
      width: 120,
      text: '支付成功订单的总金额',
      sorter: true
    },
    {
      title: '退款总金额',
      dataIndex: 'refund_amount',
      width: 130,
      text: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '平均客单价',
      dataIndex: 'order_amount_avg',
      width: 160,
      text: '支付总金额/支付订单数',
      sorter: true
    },
    {
      title: '转化成本',
      dataIndex: 'conversions_cost',
      width: 120,
      text: '总消耗/转化数',
      sorter: true
    },
    {
      title: '退单量',
      dataIndex: 'refund_count',
      width: 120,
      text: '指定时间段内完成退款的售后单数量（仅退款、退货退款），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '退单率',
      dataIndex: 'refund_rate',
      width: 120,
      text: '退款单数（退款完成时间）/支付订单数*100%',
      sorter: true
    },
    // {
    //   title: '极速退款率',
    //   dataIndex: 'fast_refund_ratio',
    //   width: 130,
    //   text: '极速退款数（退款完成时间）/支付订单数*100%',
    //   sorter: true
    // },
    {
      title: '成交总金额',
      dataIndex: 'order_amount_turnover',
      width: 160,
      text: '支付总金额-退款总金额',
      sorter: true
    },
    {
      title: '成交ROI',
      dataIndex: 'turnover_roi',
      width: 140,
      text: '成交总金额/总消耗',
      sorter: true
    }
  ])
  const state = reactive({
    loading: true,
    dataSource: [],
    defaultExpandedRowKeys: [],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    sum: {},
    initQuery: {
      product_category_chart: 1,
      after_sale_dimension: props.after_sale_dimension,
      chart_base_product_code: props.data.product_code,
      begin_time: moment().format('YYYY-MM-DD'),
      end_time: moment().format('YYYY-MM-DD'),
      page: 1,
      page_size: 10,
      field: undefined,
      order: undefined
    },
    filtration: null
  })
  // 搜索
  const changeValue = (data) => {
    state.initQuery = {
      ...state.initQuery,
      ...data.formData,
      page: 1
    }
    initPage()
  }
  const initPage = async (data) => {
    state.loading = true
    const result = await get_statement_product_latitude({ ...state.initQuery })
    state.dataSource = (result?.data?.list || []).map((item) => {
      return {
        ...item,
        key: randomWord(true, 5, 10)
      }
    })
    state.pagination.pageSize = result?.data?.size || 10
    state.pagination.total = result?.data?.total_num || 0
    state.pagination.current = result?.data?.page || 1
    state.loading = false
  }

  const pageChange = (data, filters, sorter) => {
    if (sorter?.order) {
      state.initQuery.field = sorter.field
      state.initQuery.order = sorter.order === 'ascend' ? 'asc' : 'desc'
    } else {
      state.initQuery.field = undefined
      state.initQuery.order = undefined
    }
    state.initQuery.page = data.current
    state.initQuery.page_size = data.pageSize
    state.pagination.current = data.current
    if (state.filtration) {
      state.filtration.page = data.current
    }
    initPage()
  }

  onMounted(async () => {
    initPage()
  })
  defineExpose({
    initPage
  })
</script>

<style scoped lang="scss">
  @import '../../../../../assets/css/mixin_scss_fn';
  .warp {
    :deep(.ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table-column-sorters:hover .ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table) {
      border-radius: 4px !important;
      overflow: hidden;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-thead > tr > th) {
      font-weight: 400;
      background: #f8f8f9;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-tbody > tr > td) {
      // vertical-align: top;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td) {
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td) {
      background-color: #f7f9fc;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-summary td) {
      background: #f7f9fc;
      border-color: #e8e9ec !important;
    }
  }
  .item-warp {
    width: 100%;
    display: flex;

    .item-warp-img {
      width: 34px;
      height: 34px;
      margin-top: 3px;
      margin-right: 4px;
      border-radius: 2px;
      .img {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 2px;
      }
    }
    .item-warp-content {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      .item-warp-content-title {
        @include text_overflow(1);
      }
      .item-warp-content-code {
        color: var(--text-color-5);
        line-height: 1;
      }
    }
  }
  .actions-warp {
    margin-bottom: 0px !important;
  }
  .loss_roi {
    color: var(--primary-color);
  }
  :deep(.ant-table-sticky-scroll) {
    display: none;
  }
  .item-echarts {
    position: relative;
    padding: 16px 0;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(198, 198, 198, 0.33);
    border-radius: 8px;
  }
  .left {
    width: 28%;
  }
  .center {
    width: 28%;
  }
  .right {
    width: 42%;
  }
  .item-time {
    position: absolute;
    left: 249px;
    top: 21px;
  }
  .item-icon {
    font-size: 12px;
    transition: transform 0.3s;
    // transform: rotate(-180deg);
  }
  .rotate {
    transform: rotate(-90deg); // 设置旋转效果为 90 度
  }
</style>
