<template>
  <div class="warp">
    <a-table
      sticky
      :columns="state.fixedColumns"
      :data-source="state.dataSource"
      :pagination="state.pagination"
      :loading="state.loading"
      :scroll="{ x: 2000 }"
      :defaultExpandedRowKeys="state.defaultExpandedRowKeys"
      :rowKey="(record) => record.key"
      :sortDirections="['descend', 'ascend']"
      @change="pageChange"
      :showSorterTooltip="false"
      bordered
      size="small"
    >
      <template #headerCell="{ column }">
        <template v-if="headerCell.includes(column.dataIndex)">
          <div>
            <span>{{ column.title }}</span>
            <a-tooltip>
              <template #title>{{ column.text }}</template>
              <QuestionCircleFilled class="m-l-4px font-size-12px c-#939599" />
            </a-tooltip>
          </div>
        </template>
        <template v-if="column?.dataIndex === 'actions'">
          <a-space>
            <span>{{ column?.title }} </span>
            <span>
              <SetTableColumns
                class="cursor-pointer"
                v-model:data="state.fixedColumns"
                :column="newFixedColumns"
                :isChange="isChange"
              />
            </span>
          </a-space>
        </template>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'product_type'">
          <div class="flex">
            <img :src="getProductType(record.product_type)" alt="" class="w-16px h-16px" />
          </div>
        </template>
        <template v-if="column.dataIndex === 'product_name'">
          <div class="item-warp">
            <div class="item-warp-img">
              <a-image
                class="img"
                style="width: 34px; height: 34px; border-radius: 2px"
                :src="record.product_image"
                alt=""
              />
              <!-- <img :src="requireImg('order/o8.png')" alt="" /> -->
            </div>
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>{{ record.product_name || '--' }}</template>
                  <span class="item-warp-content-title">{{ record.product_name || '--' }}</span>
                </a-tooltip>
              </div>
              <div class="item-warp-content-code">ID：{{ record.product_code || '--' }}</div>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'product_remark'">
          <div class="item-warp">
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>{{ record.product_remark }}</template>
                  <span class="item-warp-content-title">{{ record.product_remark || '--' }}</span>
                </a-tooltip>
              </div>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'shop_name'">
          <div class="item-warp">
            <!--            <div class="item-warp-img">-->
            <!--              &lt;!&ndash;              <img :src="record.logo" alt="" />&ndash;&gt;-->
            <!--            </div>-->
            <div class="item-warp-content">
              <div>
                <a-tooltip>
                  <template #title>
                    <div>店铺名称：{{ record.shop_name || '--' }}</div>
                    <div>所属公司：{{ record.company_name || '--' }}</div>
                  </template>
                  <div class="item-warp-content-title">店铺名称：{{ record.shop_name || '--' }}</div>
                  <div class="item-warp-content-title item-warp-content-code">
                    所属公司：{{ record.company_name || '--' }}
                  </div>
                </a-tooltip>
              </div>
              <!--              <div class="item-warp-content-code">ID：{{ record.shop_id || '&#45;&#45;' }}</div>-->
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'channel'">
          <span>{{ formatChannel(record) }}</span>
        </template>
        <template v-if="column.dataIndex === 'actions'">
          <a-space :size="[0, 0]" class="actions-warp" wrap>
            <a-button style="padding-left: 0px" size="small" type="link" @click="actionChange('Advertisement', record)"
              >广告投放</a-button
            >
            <!-- <a-button style="padding-left: 0px" size="small" type="link" @click="actionChange('SetProfitLoss', record)"
              >设置盈亏ROI</a-button
            > -->
            <a-button style="padding-left: 0px" size="small" type="link" @click="actionChange('PeriodReport', record)"
              >时段报表</a-button
            >
            <a-button
              v-if="useInfo.user_type === 3"
              style="padding-left: 0px"
              size="small"
              type="link"
              @click="LookCreative(record)"
              >查看创意</a-button
            >
            <a-button
              style="padding-left: 0px"
              size="small"
              type="link"
              @click="actionChange('PromotedAccount', record)"
              >推广账户</a-button
            >
            <a-button
              style="padding-left: 0px"
              v-auth="['goodsCompare']"
              size="small"
              type="link"
              @click="actionChange('GoodsCompare', record)"
              >商品对比</a-button
            >
          </a-space>
        </template>
        <template v-if="column.dataIndex === 'complaint_rate'">
          <span>{{ record.complaint_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount'">
          <span>¥ {{ centsToYuan(record.order_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount_turnover'">
          <span>¥ {{ centsToYuan(record.order_amount_turnover) }}</span>
        </template>
        <template v-if="column.dataIndex === 'product_loss_roi'">
          <span class="loss_roi"> {{ record.product_loss_roi }}</span>
        </template>
        <template v-if="column.dataIndex === 'not_callback_amount'">
          <span>¥ {{ centsToYuan(record.not_callback_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'cost'">
          <span>¥ {{ centsToYuan(record.cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'product_loss_amount'">
          <span>¥ {{ centsToYuan(record.product_loss_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'thousand_display_price'">
          <span>¥ {{ centsToYuan(record.thousand_display_price) }}</span>
        </template>

        <template v-if="column.dataIndex === 'click_order_amount_avg'">
          <span>¥ {{ centsToYuan(record.click_order_amount_avg) }}</span>
        </template>
        <template v-if="column.dataIndex === 'order_amount_avg'">
          <span>¥ {{ centsToYuan(record.order_amount_avg) }}</span>
        </template>

        <template v-if="column.dataIndex === 'conversions_cost'">
          <span>¥ {{ centsToYuan(record.conversions_cost) }}</span>
        </template>
        <template v-if="column.dataIndex === 'refund_amount'">
          <span>¥ {{ centsToYuan(record.refund_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'pay_time_refund_amount'">
          <span>¥ {{ centsToYuan(record.pay_time_refund_amount) }}</span>
        </template>
        <template v-if="column.dataIndex === 'pay_time_refund_rate'">
          <span>{{ record.pay_time_refund_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'refund_rate'">
          <span>{{ record.refund_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'conversions_rate'">
          <span>{{ record.conversions_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'ctr'">
          <span>{{ record.ctr }}%</span>
        </template>
        <template v-if="column.dataIndex === 'arrival_rate'">
          <span>{{ record.arrival_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'order_conversion_rate'">
          <span>{{ record.order_conversion_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'pay_conversion_rate'">
          <span>{{ record.pay_conversion_rate }}%</span>
        </template>
        <template v-if="column.dataIndex === 'weixin_today_rate'">
          <span>{{ record.weixin_today_rate || 0 }}%</span>
        </template>
        <template v-if="column.dataIndex === 'mini_today_rate'">
          <span>{{ record.mini_today_rate || 0 }}%</span>
        </template>
        <template v-if="column.dataIndex === 'platform_today_rate'">
          <span>{{ record.platform_today_rate || 0 }}%</span>
        </template>
        <template v-if="column.dataIndex === 'create_order_rate'">
          <span>{{ record.create_order_rate || 0 }}%</span>
        </template>

        <template v-if="column.dataIndex === 'updated_at'">
          <span>{{ record.updated_at || '--' }}</span>
        </template>
      </template>

      <template #expandColumnTitle>
        <span style="color: red"></span>
      </template>
      <template #expandIcon="{ expanded, record }">
        <div class="flex flex-y-center">
          <SvgIcon
            icon="t_arrow"
            :class="expanded ? '' : 'rotate'"
            class="item-icon cursor-pointer"
            @click="showReport(record)"
          />
        </div>
      </template>
      <template #expandedRowRender="{ record, index, indent, expanded }">
        <div class="flex justify-between" v-if="expanded && orderNumData[record.key]">
          <div class="item-echarts left">
            <Echarts
              :id="'refundTime' + record.key"
              :data="refundTimeData[record.key]"
              parentHeight="248px"
              style="width: 100%; height: 100%"
            />
          </div>
          <div class="item-echarts center">
            <Echarts
              :id="'payTime' + record.key"
              parentHeight="248px"
              :data="payTimeData[record.key]"
              style="width: 100%; height: 100%"
            />
          </div>
          <div class="item-echarts right">
            <span class="item-time c-#9399A3">{{ state.filtration?.begin_time || state.initQuery.begin_time }}</span>
            <Echarts
              :id="'orderNum' + record.key"
              parentHeight="248px"
              :data="orderNumData[record.key]"
              style="width: 100%; height: 100%"
            />
          </div>
        </div>
      </template>

      <template #summary>
        <a-table-summary fixed v-if="state.dataSource.length">
          <a-table-summary-row>
            <template v-for="(item, index) in state.fixedColumns">
              <template v-if="index === 0">
                <a-table-summary-cell :index="index"></a-table-summary-cell>
              </template>
              <template v-if="index === 1">
                <a-table-summary-cell :index="index"
                  ><div v-if="item.dataIndex == 'product_name'">
                    总计: {{ state.pagination.total }}
                  </div></a-table-summary-cell
                >
              </template>
              <template
                v-else-if="
                  ['shop_name', 'updated_at', 'product_created_at', 'admin_name', 'actions', 'complaint_rate'].includes(
                    item.dataIndex
                  )
                "
              >
                <a-table-summary-cell :index="index"></a-table-summary-cell>
              </template>
              <template v-else
                ><a-table-summary-cell :index="index">
                  <span
                    v-if="
                      [
                        'ctr',
                        'conversions_rate',
                        'refund_rate',
                        'arrival_rate',
                        'create_order_rate',
                        'order_conversion_rate',
                        'pay_conversion_rate',
                        'pay_time_refund_rate',
                        'arrival_rate'
                      ].includes(item.dataIndex)
                    "
                    >{{ state?.sum[item.dataIndex] }}%</span
                  >
                  <span v-else>{{ setFider(setFilder, item.dataIndex) }}</span>
                </a-table-summary-cell></template
              >
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue'
  import datas from '../data'
  import { centsToYuan, requireImg, randomWord } from '@/utils'
  import { QuestionCircleFilled } from '@ant-design/icons-vue'
  import {
    get_statement_product_latitude,
    get_after_sale_dimension,
    fetch_pay_time_report,
    fetch_refund_time_report,
    fetch_order_num_report
  } from '../index.api'
  import moment from 'moment'
  import { useApp, useRouter } from '@/hooks'
  import Echarts from '@/components/ui/common/Echarts.vue'
  import { cloneDeep } from 'lodash-es'
  const { routerToDialogPage } = useRouter()
  const { isMobile, useInfo } = useApp()
  const props = defineProps(['time'])
  const emits = defineEmits(['eventBtn', 'getDimension'])
  const { setFilder, headerCell } = datas()
  const platformArr = ['weixin_num', 'mini_num', 'platform_num', 'product_type']

  const setFider = (arr, key) => {
    if (arr.includes(key)) {
      return `¥ ${centsToYuan(state?.sum[key])}`
    } else if (platformArr.includes(key)) {
      return ''
    } else {
      return state?.sum[key]
    }
  }
  const getProductType = (type) => {
    const productData = {
      1: 'datacenter/icon_applet_check.png',
      2: 'datacenter/icon_pdd_check.png',
      3: 'datacenter/icon_tb_check.png',
      4: 'datacenter/icon_jd_check.png'
    }
    return requireImg(productData[type])
  }
  const after_sale_dimension = ref(1)
  const newFixedColumns = ref([])
  const refundTimeData = ref({})
  const payTimeData = ref({})
  const orderNumData = ref({})

  const fixedColumns = ref([
    {
      title: '类型',
      dataIndex: 'product_type',
      fixed: 'left',
      align: 'left',
      width: 50
    },
    {
      title: '商品信息',
      dataIndex: 'product_name',
      fixed: isMobile.value ? false : 'left',
      width: 240
    },

    {
      title: '店铺信息',
      dataIndex: 'shop_name',
      fixed: isMobile.value ? false : 'left',
      width: 240
    },
    {
      title: '商品类目',
      dataIndex: 'category_names',
      width: 240
    },
    {
      title: '商户拓展',
      dataIndex: 'channel',
      width: 240
    },
    {
      title: '投诉率',
      dataIndex: 'complaint_rate',
      width: 100,
      text: '所有投诉类型的投诉量/订单量',
      sorter: true
    },
    {
      title: '支付订单数',
      dataIndex: 'order_count',
      width: 120,
      text: '支付成功的订单数量',
      sorter: true
    },
    {
      title: '支付总金额',
      dataIndex: 'order_amount',
      width: 120,
      text: '支付成功订单的总金额',
      sorter: true
    },
    {
      title: '支付ROI',
      dataIndex: 'roi',
      width: 120,
      sorter: true
    },
    {
      title: '支付转化率',
      dataIndex: 'pay_conversion_rate',
      width: 140,
      text: '(支付人数/访客数)X 100%',
      sorter: true
    },
    {
      title: '成交订单数',
      dataIndex: 'order_count_turnover',
      width: 160,
      text: '支付订单数-退款量',
      sorter: true
    },
    {
      title: '成交总金额',
      dataIndex: 'order_amount_turnover',
      width: 160,
      text: '支付总金额-退款总金额',
      sorter: true
    },
    {
      title: '下单支付率',
      dataIndex: 'create_order_rate',
      width: 160,
      text: '支付订单数/下单订单数',
      sorter: true
    },
    {
      title: '成交ROI',
      dataIndex: 'turnover_roi',
      width: 140,
      text: '成交总金额/总消耗',
      sorter: true
    },
    {
      title: '盈亏ROI',
      dataIndex: 'product_loss_roi',
      width: 140,
      text: '（总盈亏-成交总金额）/总消耗',
      sorter: true
    },
    {
      title: '扣除回传数',
      dataIndex: 'not_callback_count',
      text: '未回传的订单数量，支付订单数-扣除回传后订单数',
      width: 120,
      sorter: true
    },
    {
      title: '扣除回传金额',
      dataIndex: 'not_callback_amount',
      text: '未回传的订单金额，支付订单金额-扣除回传金额后金额',
      width: 150,
      sorter: true
    },
    {
      title: '总花费',
      dataIndex: 'cost',
      width: 120,
      sorter: true
    },
    {
      title: '总盈亏',
      dataIndex: 'product_loss_amount',
      width: 120,
      text: '成交总金额-总消耗*盈亏ROI',
      sorter: true
    },
    {
      title: '展示数',
      dataIndex: 'view_count',
      width: 120,
      sorter: true
    },
    {
      title: '千次展示均价',
      dataIndex: 'thousand_display_price',
      width: 160,
      text: '总消耗/曝光量*1000',
      sorter: true
    },
    {
      title: '点击数',
      dataIndex: 'valid_click_count',
      width: 110,
      sorter: true
    },
    {
      title: '点击率',
      dataIndex: 'ctr',
      width: 110,
      text: '点击量/曝光量*100%',
      sorter: true
    },
    {
      title: '抵达率',
      dataIndex: 'arrival_rate',
      width: 100,
      text: '小程序访客数/计划点击数',
      sorter: true
    },
    {
      title: '平均点击单价',
      dataIndex: 'click_order_amount_avg',
      width: 160,
      sorter: true
    },
    {
      title: '平均客单价',
      dataIndex: 'order_amount_avg',
      width: 160,
      text: '支付总金额/支付订单数',
      sorter: true
    },
    {
      title: '转化数',
      dataIndex: 'conversions_count',
      width: 120,
      sorter: true
    },
    {
      title: '转化成本',
      dataIndex: 'conversions_cost',
      width: 120,
      text: '总消耗/转化数',
      sorter: true
    },
    {
      title: '转化率',
      dataIndex: 'conversions_rate',
      width: 100,
      text: '转化数/点击量',
      sorter: true
    },
    {
      title: '下单转化率',
      dataIndex: 'order_conversion_rate',
      width: 120,
      text: '(下单人数/访客数)X 100%',
      sorter: true
    },
    {
      title: '退单量',
      dataIndex: 'refund_count',
      width: 210,
      text: '指定时间段内完成退款的售后单数量（仅退款、退货退款），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '退单量',
      dataIndex: 'pay_time_refund_count',
      width: 180,
      text: '指定时间段内完成退款的售后单数量（仅退款、退货退款），按照订单支付时间进行统计',
      sorter: true
    },
    {
      title: '退款总金额',
      dataIndex: 'refund_amount',
      width: 240,
      text: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款），按照退款完成时间进行统计',
      sorter: true
    },
    {
      title: '退款总金额',
      dataIndex: 'pay_time_refund_amount',
      width: 220,
      text: '指定时间段内完成退款的售后单退款金额总和（仅退款、退货退款），按照订单支付时间进行统计',
      sorter: true
    },
    {
      title: '退单率',
      dataIndex: 'refund_rate',
      width: 210,
      text: '退款单数（退款完成时间）/支付订单数*100%',
      sorter: true
    },
    {
      title: '退单率',
      dataIndex: 'pay_time_refund_rate',
      text: '退款单数（支付时间）/支付订单数*100%',
      width: 200,
      sorter: true
    },
    {
      title: '微信支付投诉量',
      dataIndex: 'weixin_num',
      width: 150,
      text: '选定时间范围内对应商品微信支付投诉量',
      sorter: true
    },
    {
      title: '小程序投诉量',
      dataIndex: 'mini_num',
      width: 140,
      text: '选定时间范围内对应商品小程序投诉量',
      sorter: true
    },
    {
      title: '平台投诉量',
      dataIndex: 'platform_num',
      width: 140,
      text: '选定时间范围内对应商品自有平台投诉量',
      sorter: true
    },
    {
      title: '支付投诉率',
      dataIndex: 'weixin_today_rate',
      width: 140,
      text: '',
      sorter: true
    },
    {
      title: '小程序投诉率',
      dataIndex: 'mini_today_rate',
      width: 140,
      text: '',
      sorter: true
    },
    {
      title: '平台投诉率',
      dataIndex: 'platform_today_rate',
      width: 140,
      text: '',
      sorter: true
    },

    {
      title: '更新时间',
      dataIndex: 'updated_at',
      width: 180
    },
    {
      title: '商品创建时间',
      dataIndex: 'product_created_at',
      width: 180
    },
    {
      title: '负责人',
      dataIndex: 'admin_name',
      width: 100
    },
    {
      title: '商品备注',
      dataIndex: 'product_remark',
      width: 180
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 150,
      disabled: true,
      fixed: isMobile.value ? false : 'right'
    }
  ])
  const state = reactive({
    loading: true,
    dataSource: [],
    defaultExpandedRowKeys: [],
    fixedColumns: cloneDeep(newFixedColumns),
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    },
    sum: {},
    initQuery: {
      page: 1,
      page_size: 10,
      begin_time: moment().format('YYYY-MM-DD'),
      end_time: moment().format('YYYY-MM-DD'),
      field: undefined,
      order: undefined
    },
    filtration: null
  })

  const formatChannel = (record) => {
    const parts = []
    if (record.channel1) parts.push(record.channel1)
    if (record.channel2) parts.push(record.channel2)
    if (record.channel3) parts.push(record.channel3)
    return parts.join(' > ')
  }
  //查看创意
  const LookCreative = (item) => {
    let params = {
      ...props.time,
      product_id: item.product_id
    }
    routerToDialogPage('ShopProjectReport', params)
  }
  // 操作时间
  const actionChange = (type, data) => {
    emits('eventBtn', { type, data })
  }
  function createHash(hashLength = 10) {
    return Array.from(Array(Number(hashLength) || 24), () => Math.floor(Math.random() * 36).toString(36)).join('')
  }
  const isChange = ref(createHash())

  const tabColumns = (type) => {
    type = type ? type : state.initQuery.after_sale_dimension
    const pay_time_arr = ['pay_time_refund_rate', 'pay_time_refund_count', 'pay_time_refund_amount']
    const refund_time_arr = ['refund_rate', 'refund_count', 'refund_amount']
    const filterArr = type == 1 ? pay_time_arr : refund_time_arr
    newFixedColumns.value = fixedColumns.value.filter((item) => !filterArr.includes(item.dataIndex))
    state.fixedColumns = newFixedColumns.value
    isChange.value = createHash()
  }

  const initPage = async (data) => {
    state.loading = true
    if (data) {
      tabColumns(data?.after_sale_dimension)
    }
    state.filtration = data ? data : state.filtration
    const result = await get_statement_product_latitude({ ...state.initQuery, ...state.filtration })
    state.dataSource = (result?.data?.list || []).map((item) => {
      return {
        ...item,
        key: randomWord(true, 5, 10)
      }
    })
    state.pagination.pageSize = result?.data?.size || 10
    state.pagination.total = result?.data?.total_num || 0
    state.pagination.current = result?.data?.page || 1
    state.sum = result?.data?.sum || {}
    state.loading = false
  }

  const pageChange = (data, filters, sorter) => {
    if (sorter?.order) {
      state.initQuery.field = sorter.field
      state.initQuery.order = sorter.order === 'ascend' ? 'asc' : 'desc'
    } else {
      state.initQuery.field = undefined
      state.initQuery.order = undefined
    }
    state.initQuery.page = data.current
    state.initQuery.page_size = data.pageSize
    state.pagination.current = data.current
    if (state.filtration) {
      state.filtration.page = data.current
    }
    initPage()
  }

  const showReport = (item) => {
    console.log(item, 'item---------', item.key)

    const index = state.defaultExpandedRowKeys.indexOf(item.key)
    if (index === -1) {
      // 如果 id 不在数组中，添加它
      state.defaultExpandedRowKeys.push(item.key)
      getReportData(item)
    } else {
      // 如果 id 已经存在于数组中，移除它
      state.defaultExpandedRowKeys.splice(index, 1)
    }
  }
  const getReportData = async (item) => {
    const params = {
      product_id: item.product_id,
      product_type: item.product_type,
      admin_ids: state.filtration?.admin_ids || undefined,
      media_type: item.media_type,
      begin_time: state.filtration?.begin_time || state.initQuery.begin_time,
      end_time: state.filtration?.end_time || state.initQuery.end_time
    }
    const hourParmas = {
      ...params,
      product_code: item.product_code
    }
    let [{ data: refundData }, { data: payData }, { data: hourData }] = await Promise.all([
      fetch_refund_time_report(params),
      fetch_pay_time_report(params),
      fetch_order_num_report(hourParmas)
    ])

    refundTimeData.value[item.key] = createRefundTimeData(refundData)
    payTimeData.value[item.key] = createPayTimeData(payData)
    orderNumData.value[item.key] = createOrderNumData(hourData)
  }
  const createOrderNumData = (hourData) => {
    const xAxisData = []
    const seriesMoney = []
    const seriesOrder = []
    if (hourData?.length) {
      hourData.forEach((item) => {
        item.hour = Number(item.hour) == 0 ? '00' : item.hour
        xAxisData.push(item.hour + ':00')
        seriesMoney.push(item.total_amount)
        seriesOrder.push(item.order_count)
      })
    }
    return {
      title: {
        text: '商品每小时订单数及总金额',
        left: '16px'
        // top: 'center',
        // textStyle: {
        //   fontSize: 30
        // },
        // subtextStyle: {
        //   fontSize: 20
        // }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(100, 100, 100, 0.72)',
        borderWidth: 0,
        formatter: (params) => {
          let tooltipContent = ''
          let currentDate = state.filtration?.begin_time || state.initQuery.begin_time
          tooltipContent += '<span>' + currentDate + ' ' + '</span>'
          let nameAdded = false
          for (let i = 0; i < params.length; i++) {
            let seriesName = params[i].seriesName // 获取系列名
            let color = params[i].color // 获取颜色
            let value = params[i].value // 获取值
            let name = params[i].name // 获取名称
            if (!nameAdded) {
              tooltipContent += '<span>' + name + '</span><br/>'
              nameAdded = true // 将标记设为 true，表示名称已添加
            }
            tooltipContent +=
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' +
              color +
              '"></span>' // 显示颜色代表
            tooltipContent += seriesName + ': ' + value + '<br/>' // 显示系列名、名称和值
          }

          return tooltipContent
        },
        textStyle: {
          color: '#FFFFFF'
        },
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#FE9D35'
          },
          lineStyle: {
            color: '#FE9D35' // 设置竖直轴线颜色为红色
          }
        }
      },
      color: ['#ECBE7A', '#ED6D6D'],
      legend: {
        data: ['总金额', '订单数'],
        itemWidth: 16,
        itemHeight: 6,
        right: '10px'
      },
      grid: {
        top: 70,
        left: '10%',
        right: '5%',
        bottom: '9%'
      },
      xAxis: [
        {
          type: 'category',
          data: xAxisData,
          axisPointer: {
            label: {
              backgroundColor: '#FE9D35', // 设置数值背景色为红色
              padding: [5, 10] // 设置背景色的内边距
            }
          },
          axisTick: {
            show: false
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '总金额(元)',
          axisPointer: {
            label: {
              backgroundColor: '#FE9D35', // 设置数值背景色为红色
              padding: [5, 10] // 设置背景色的内边距
            }
          },
          splitLine: {
            show: false // 隐藏 y 轴的分隔线
          },
          splitArea: {
            show: true, // 显示 y 轴的分隔区域
            areaStyle: {
              color: ['#F9F9F9', '#fff'] // 设置 y 轴分隔区域的背景色为灰白相间
            }
          }
        },
        {
          type: 'value',
          name: '订单数',
          // min: 0,
          axisLabel: {
            show: false
          },
          axisPointer: {
            label: {
              backgroundColor: '#FE9D35', // 设置数值背景色为红色
              padding: [5, 10] // 设置背景色的内边距
            }
          },
          splitLine: {
            show: false // 隐藏 y 轴的分隔线
          },
          splitArea: {
            show: true, // 显示 y 轴的分隔区域
            areaStyle: {
              color: ['#F9F9F9', '#fff'] // 设置 y 轴分隔区域的背景色为灰白相间
            }
          }
        }
      ],
      series: [
        {
          name: '总金额',
          type: 'bar',
          barWidth: 16,
          tooltip: {
            valueFormatter: function (value) {
              return value + '元'
            }
          },
          data: seriesMoney || []
        },

        {
          name: '订单数',
          type: 'line',
          yAxisIndex: 1,
          areaStyle: {
            color: '#ED6D6D',
            opacity: 0.07
          },
          tooltip: {
            valueFormatter: function (value) {
              return value
            }
          },
          data: seriesOrder
        }
      ]
    }
  }
  const createRefundTimeData = (refundData) => {
    return {
      title: {
        text: '商品退款时间分布',
        left: 'center'
      },
      color: ['#EC907C', '#ECBE7A', '#B6A2DE'],
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(100, 100, 100, 0.72)',
        borderWidth: 0,
        textStyle: {
          color: '#FFFFFF'
        }
      },
      grid: {
        left: '10%',
        right: '10%'
      },

      // legend: {
      //   orient: 'vertical',
      //   left: 'left'
      // },
      series: [
        {
          name: '商品退款时间分布',
          type: 'pie',
          radius: ['0%', '60%'],
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}( {d}%)'
          },
          data: [
            { value: refundData.five_minutes?.value || 0, name: '5分钟' },
            { value: refundData.half_hour?.value || 0, name: '30分钟' },
            { value: refundData.one_hour?.value || 0, name: '1小时' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  }
  const createPayTimeData = (payData) => {
    const timeKeys = [
      'one_minutes',
      'two_minutes',
      'three_minutes',
      'five_minutes',
      'ten_minutes',
      'twenty_minutes',
      'half_hour'
    ]
    const payTimeValues = timeKeys.map((key) => payData[key]?.value || 0)
    const payTimePer = timeKeys.map((key) => payData[key]?.per || 0)

    return {
      title: {
        text: '商品支付时间分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        // axisPointer: {
        //   type: 'shadow'
        // },
        backgroundColor: 'rgba(100, 100, 100, 0.72)',
        borderWidth: 0,
        textStyle: {
          color: '#FFFFFF'
        }
      },
      color: ['#ECBE7A'],
      grid: {
        bottom: '9%',
        right: '3%'
      },
      xAxis: [
        {
          type: 'category',
          data: ['1分钟', '2分钟', '3分钟', '5分钟', '10分钟', '20分钟', '30分钟'],
          axisTick: {
            show: false
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: false // 隐藏 y 轴的分隔线
          },
          splitArea: {
            show: true, // 显示 y 轴的分隔区域
            areaStyle: {
              color: ['#F9F9F9', '#fff'] // 设置 y 轴分隔区域的背景色为灰白相间
            }
          }
        }
      ],
      series: [
        {
          type: 'bar',
          barWidth: '60%',
          data: payTimeValues,
          label: {
            show: true,
            position: 'top',
            formatter: function (params) {
              console.log(params, 'params')
              let percentData = payTimePer[params.dataIndex]
              return params.data + '\n' + percentData
            }
          }
        }
      ]
    }
  }
  onMounted(async () => {
    const data = await get_after_sale_dimension()
    state.initQuery.after_sale_dimension = data.data
    after_sale_dimension.value = data.data
    tabColumns(after_sale_dimension.value)
    emits('getDimension', data.data)
    initPage()
  })
  defineExpose({
    initPage
  })
</script>

<style scoped lang="scss">
  @import '../../../../../assets/css/mixin_scss_fn';
  .warp {
    :deep(.ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table-column-sorters:hover .ant-table-column-sorter) {
      color: #939599;
    }
    :deep(.ant-table-wrapper .ant-table) {
      border-radius: 4px !important;
      overflow: hidden;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-thead > tr > th) {
      font-weight: 400;
      background: #f8f8f9;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-tbody > tr > td) {
      // vertical-align: top;
      color: #313233;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table.ant-table-bordered .ant-table-tbody > tr > td) {
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-wrapper .ant-table-tbody > tr.ant-table-row:hover > td) {
      background-color: #f7f9fc;
      border-color: #e8e9ec !important;
    }
    :deep(.ant-table-summary td) {
      background: #f7f9fc;
      border-color: #e8e9ec !important;
    }
  }
  .item-warp {
    width: 100%;
    display: flex;

    .item-warp-img {
      width: 34px;
      height: 34px;
      margin-top: 3px;
      margin-right: 4px;
      border-radius: 2px;
      .img {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 2px;
      }
    }
    .item-warp-content {
      flex: 1;
      box-sizing: border-box;
      overflow: hidden;
      .item-warp-content-title {
        @include text_overflow(1);
      }
      .item-warp-content-code {
        color: var(--text-color-5);
        line-height: 1;
      }
    }
  }
  .actions-warp {
    margin-bottom: 0px !important;
  }
  .loss_roi {
    color: var(--primary-color);
  }
  :deep(.ant-table-sticky-scroll) {
    display: none;
  }
  .item-echarts {
    position: relative;
    padding: 16px 0;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(198, 198, 198, 0.33);
    border-radius: 8px;
  }
  .left {
    width: 28%;
  }
  .center {
    width: 28%;
  }
  .right {
    width: 42%;
  }
  .item-time {
    position: absolute;
    left: 249px;
    top: 21px;
  }
  .item-icon {
    font-size: 12px;
    transition: transform 0.3s;
    // transform: rotate(-180deg);
  }
  .rotate {
    transform: rotate(-90deg); // 设置旋转效果为 90 度
  }
</style>
