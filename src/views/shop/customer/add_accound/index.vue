<template>
  <DesTablePage class="page_main common_page_warp">
    <template #title>
      <div>广告账户管理</div>
    </template>
    <template #tableWarp>
      <TabList :active="data.type" @change="tabsChange" />
      <template v-if="data.type !== 'ocean'">
        <SearchBaseLayout :data="searchList" @changeValue="changeValue" :actions="actions" />
        <TableZebraCrossing :data="tableData" @change="pageChange" class="mt-16px">
          <template #bodyCell="{ scope }">
            <template v-if="scope.column.key === 'token_account_id'">
              <span>{{ scope.record.token_account_id }}</span>
              <a-tooltip>
                <template #title>{{ scope.record.report_err }}</template>
                <ExclamationCircleOutlined
                  v-if="scope.record.report_err && data.type == 'gd'"
                  class="c-#e63030 ml-10px"
                />
              </a-tooltip>
            </template>
            <template v-if="scope.column.key === 'account_name'">
              <span>{{ scope.record.account_name || '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'company_name'">
              <span>{{ scope.record.company_name || '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'creator'">
              <span v-if="data.type === 'ocean_cid'">{{ scope.record.admin_name || '--' }}</span>
              <span v-else>{{ scope.record.creator || '--' }}</span>
            </template>
            <template v-if="scope.column.key === 'created_at'">
              <span v-if="data.type === 'ocean_cid'">{{ scope.record.created_at || '--' }}</span>
              <span v-else>{{ formatDate(scope.record.created_at * 1000) || '--' }}</span>
            </template>
          </template>
        </TableZebraCrossing>
      </template>
      <OceanPage v-else />
    </template>
  </DesTablePage>
</template>
<script setup name="AdDmp">
  import TabList from './components/TabList.vue'
  import { reactive, onMounted, ref } from 'vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import datas from './src/datas'
  import { useRoute, useRouter } from 'vue-router'
  import OceanPage from './components/ocean/index.vue'
  import { formatDate } from '@/utils'
  import { dmpList, get_oceanengine_list, agnetism_list } from './index.api.js'
  const { searchList, columns } = datas()
  const route = useRoute()
  const router = useRouter()
  const data = reactive({
    type: 'gd',
    params: {
      page: 1,
      page_size: 10
    }
  })
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 6
    }
  }
  // 表格数据
  const tableData = reactive({
    bordered: true,
    scroll: {
      scrollToFirstRowOnChange: true,
      x: 1000
    },
    dataSource: [],
    loading: false,
    columns: columns,
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      pageSize: 10,
      current: 1,
      size: 'small',
      showTotal: (total) => `共${total}条数据`
    }
  })
  const changeValue = (v) => {
    if (v.status) {
      data.params = {
        ...data.params,
        ...v.formData
      }
      getList()
    } else {
      data.params = {
        page: 1,
        page_zise: 10
      }
      getList()
    }
  }
  // 获取广告列表
  const getList = async () => {
    try {
      if (data.type === 'ocean') return
      let res = null
      let _params = {
        ...data.params
      }
      if (data.type === 'gd') {
        res = await dmpList(_params)
      }
      if (data.type === 'cl') {
        _params.advertiser_id = _params.token_account_id
        res = await agnetism_list(_params)
        if (res.data?.list) {
          res.data.list.forEach((item) => {
            item.token_account_id = item.advertiser_id
            item.account_name = item.advertiser_name
            item.created_at_format = item.created_at
          })
        }
      }

      if (data.type === 'ocean_cid') {
        const params = {
          ..._params,
          is_cid: 1,
          type: 2
        }
        res = await get_oceanengine_list(params)
      }
      tableData.dataSource = res.data?.list || []
      tableData.pagination.total = res.data?.total_num || res.data?.total || 0
      tableData.pagination.current = res.data?.page || 1
    } catch (error) {
      console.error(error)
    }
  }
  const pageChange = (pagination) => {
    data.params.page = pagination.current
    data.params.page_size = pagination.pageSize
    tableData.pagination.pageSize = pagination.pageSize
    getList()
  }

  const tabsChange = (val) => {
    data.type = val
    data.params.page = 1
    getList()
  }
  getList()

  onMounted(() => {
    const type = route.query.type
    if (type) {
      data.type = type
    }
  })
</script>
<style lang="scss" scoped>
  .pd {
    padding: 20px 30px 4px 30px;
    border-bottom: 1px solid #eef0f3;
  }
  .kk_btn_group {
    margin-bottom: 20px;
    :deep(.el-radio-button__inner) {
      padding: 9px 15px;
    }
  }
  .round {
    width: 8px;
    height: 8px;
    background: #60a13b;
    border-radius: 50%;
  }
  .red {
    color: red;
  }
  .bgred {
    background: red;
  }
  .btn {
    color: #118bce;
    cursor: pointer;
    user-select: none;
    margin-right: 5px;
  }
  .mr {
    margin-right: 15px;
  }
  :deep(.ant-radio-button-wrapper-checked) {
    border-color: #d9d9d9 !important;
    &::before {
      background-color: #d9d9d9 !important;
    }
  }
</style>
