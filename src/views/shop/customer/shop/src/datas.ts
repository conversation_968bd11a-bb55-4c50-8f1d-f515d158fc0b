import { reactive } from 'vue'
export default function datas() {
  const searchList = reactive([
    {
      type: 'input.text',
      field: 'name',
      value: undefined,
      props: {
        placeholder: '请输入店铺ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'input.text',
      field: 'shop_name',
      value: undefined,
      props: {
        placeholder: '请输入店铺名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'select',
      field: 'company_id',
      value: undefined,
      props: {
        placeholder: '请选择所属公司',
        filterable: true,
        options: []
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'select',
      field: 'status',
      value: undefined,
      props: {
        placeholder: '请选择状态',
        filterable: true,
        options: [
          {
            value: 1,
            label: '启用'
          },
          {
            value: 2,
            label: '禁用'
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: [],
      props: {
        placeholder: ['创建店铺时间', '']
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'date',
      field: 'order_create_at',
      value: [],
      props: {
        placeholder: ['订单支付时间', '']
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'input.text',
      field: 'mch_id',
      value: undefined,
      props: {
        placeholder: '请输入商户号'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'select',
      field: 'creator_id',
      value: undefined,
      props: {
        placeholder: '请选择创建人',
        options: []
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'channel_id',
      field: 'channel_ids',
      value: undefined,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
  ])

  const columns = reactive([
    {
      dataIndex: 'code',
      key: 'code',
      title: '店铺ID',
      width: 150,
      fixed: 'left'
    },
    {
      dataIndex: 'logo',
      key: 'logo',
      title: '店铺LOGO',
      width: 115,
      slot: true
    },
    {
      dataIndex: 'name',
      key: 'name',
      title: '店铺名称',
      width: 100
    },
    {
      dataIndex: 'business_number',
      key: 'business_number',
      title: '商户号',
      width: 100
    },
    {
      dataIndex: 'channel_name',
      key: 'channel_name',
      title: '商户拓展',
      width: 240
    },
    {
      dataIndex: 'belong_company_name',
      key: 'belong_company_name',
      title: '所属公司',
      width: 240
    },
    {
      dataIndex: 'on_sale',
      key: 'on_sale',
      title: '在售商品',
      width: 100
    },
    {
      dataIndex: 'visit',
      key: 'visit',
      title: '浏览量',
      width: 100
    },
    {
      dataIndex: 'domain',
      key: 'domain',
      title: '域名',
      width: 100
    },
    {
      dataIndex: 'complaint_ratio',
      key: 'complaint_ratio',
      title: '投诉率',

      width: 100
    },
    {
      dataIndex: 'ratio',
      key: 'ratio',
      title: '分佣比例',
      width: 170
    },
    {
      dataIndex: 'recharge',
      key: 'recharge',
      title: '充值打款',
      width: 190
    },
    // {
    //   dataIndex: 'handle_complaint_ratio',
    //   key: 'handle_complaint_ratio',
    //   title: '投诉处理及时率',
    //   width: 140,
    //   slot: true
    // },
    {
      dataIndex: 'charge_service_money',
      key: 'charge_service_money',
      title: '额外技术服务费',
      sorter: true,
      width: 140
    },
    {
      dataIndex: 'free_service_money ',
      key: 'free_service_money ',
      title: '技术服务费减免',
      sorter: true,
      width: 140
    },
    {
      dataIndex: 'status',
      key: 'status',
      title: '状态',
      width: 90
    },
    {
      dataIndex: 'merchant_complaint_switch',
      key: 'merchant_complaint_switch',
      title: '接管微信支付投诉',
      width: 170
    },
    {
      dataIndex: 'mini_complaint_switch',
      key: 'mini_complaint_switch',
      title: '接管小程序投诉',
      width: 150
    },
    {
      dataIndex: 'platform_complaint_switch',
      key: 'platform_complaint_switch',
      title: '接管平台自有投诉',
      width: 180
    },
    {
      dataIndex: 'comment_switch',
      key: 'comment_switch',
      title: '是否接管用户评价',
      tips: '开启后，该店铺下的用户评价将由总后台接管处理',
      width: 180
    },
    {
      dataIndex: 'fast_refund_control',
      key: 'fast_refund_control',
      title: '商家自行控制是否极速退款',
      width: 200
    },
    {
      dataIndex: 'customer_service_switch',
      key: 'customer_service_switch',
      title: '售前客服',
      width: 150
    },
    {
      dataIndex: 'kefu_task_over',
      key: 'kefu_task_over',
      title: '接管售后客服',
      width: 150
    },
    {
      dataIndex: 'douyin_entities',
      key: 'douyin_entities',
      title: '是否支持巨量引擎',
      width: 160
    },
    {
      dataIndex: 'video_switch',
      key: 'video_switch',
      title: '视频',
      width: 130
    },
    {
      dataIndex: 'gift_switch',
      key: 'gift_switch',
      title: '送礼物',
      width: 130
    },
    {
      dataIndex: 'auth_phone',
      key: 'auth_phone',
      title: '下单验证手机号',
      tips: '开启后，该店铺发布商品时可自由选择是否开启下单验证手机号功能',
      width: 140
    },
    {
      dataIndex: 'creator',
      key: 'creator',
      title: '创建人',
      width: 100
    },
    {
      dataIndex: 'created_at',
      key: 'created_at',
      title: '创建时间',
      width: 120,
      slot: true
    },
    {
      dataIndex: 'handle',
      key: 'handle',
      title: '操作',
      width: 110,
      fixed: 'right'
    }
  ])

  const statusType = (val: string | number) => {
    let status = {
      1: {
        text: '启用',
        color: '#60A13B'
      },
      2: {
        text: '禁用',
        color: '#E63030'
      }
    }
    return (status as any)[val]
  }

  return { searchList, columns, statusType }
}
