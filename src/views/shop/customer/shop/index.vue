<template>
  <DesTablePage>
    <template #title>
      <div>店铺管理</div>
    </template>
    <template #extra>
      <a-button
        v-auth="['sysShopAdd']"
        class="btn"
        type="primary"
        :icon="h(PlusOutlined)"
        @click="router.push({ path: '/shop/customer/add-shop' })"
        >新建店铺</a-button
      >
      <a-button v-auth="['sysShopExport']" type="primary" @click="onExport">导出</a-button>
    </template>
    <template #search>
      <SearchBaseLayout
        style="margin-bottom: 16px"
        :data="searchConfig.data"
        @changeValue="searchForm"
        :actions="searchConfig.options"
      />
    </template>
    <template #tableWarp>
      <div class="flex justify-end">
        <a-button v-auth="['sysShopUpdateRecords']" type="link" @click="queryUpdateRecords">查看操作记录</a-button>
      </div>
      <TableZebraCrossing
        :data="{
          dataSource: tableData.list,
          columns,
          pagination: false,
          scroll: { scrollToFirstRowOnChange: false, x: 1600 },
          bordered: true,
          loading: false,
          rowKey: 'id'
        }"
        @change="pageChange"
      >
        <template #headerCell="{ scope }">
          <div
            v-if="
              [
                'merchant_complaint_switch',
                'mini_complaint_switch',
                'platform_complaint_switch',
                'customer_service_switch',
                'gift_switch',
                'kefu_task_over'
              ].includes(scope.column.key)
            "
          >
            <span>{{ scope.title }}</span>
            <a-tooltip>
              <template #title>{{
                scope.title === '售前客服'
                  ? '控制该店铺下所有商品详情页中是否展示客服入口。'
                  : scope.title === '接管售后客服'
                    ? '开启后，平台客服将接管该店铺有订单的用户会话，商家客服可同步回复'
                    : scope.title === '送礼物'
                      ? '开启后，创建商品时可自由的选择是否开启送礼物功能'
                      : '关闭商家可自行处理投诉；开启后商家不可处理投诉，仅平台可处理投诉'
              }}</template>
              <QuestionCircleFilled class="m-l-5px c-#C5C6CC" />
            </a-tooltip>
          </div>
          <div v-if="['comment_switch', 'auth_phone'].includes(scope.column.key)">
            <span>{{ scope.title }}</span>
            <a-tooltip>
              <template #title>{{ scope.column.tips }}</template>
              <QuestionCircleFilled class="m-l-5px c-#C5C6CC" />
            </a-tooltip>
          </div>
        </template>
        <template #bodyCell="{ scope }">
          <template v-if="scope.column.dataIndex === 'logo'">
            <a-image style="width: 60px; height: 60px" :src="scope.record.logo"></a-image>
          </template>
          <template v-if="scope.column.dataIndex === 'name'">
            <span
              class="link"
              @click="router.push({ path: '/shop/customer/shop-detail', query: { id: scope.record.shop_info.id } })"
              >{{ scope.record?.name }}</span
            >
          </template>
          <template v-if="scope.column.dataIndex === 'belong_company_name'">
            <a-tooltip>
              <template #title>{{ scope.record?.belong_company_name }}</template>
              <div class="channel_name">
                {{ scope.record?.belong_company_name }}
              </div>
            </a-tooltip>
          </template>
          <template v-if="scope.column.dataIndex === 'domain'">
            <div class="flex">
              <a-button type="link" class="p-0!" @click="handleDomainList(scope.record, 'domain')">{{
                scope.record.domains || 0
              }}</a-button>
              <!-- <EditTwoTone      
                class="font-size-20px ml-8px"
                @click="onCheckPart(scope.record)"
              /> -->
            </div>
          </template>
          <template v-if="scope.column.dataIndex === 'complaint_ratio'">
            <span class="font_red">{{ scope.record.complaint_ratio || 0 }}%</span>
          </template>
          <template v-if="scope.column.dataIndex === 'handle_complaint_ratio'">
            <span class="font_red">{{ scope.handle_complaint_ratio }}%</span>
          </template>
          <template v-if="scope.column.dataIndex === 'charge_service_money'">
            <a-button type="link" class="h-auto! p-0!" @click="technicalHanlder('charge_service_money', scope.record)"
              >¥{{ scope.record.charge_service_money || 0 }}</a-button
            >
          </template>
          <template v-if="scope.column.dataIndex === 'free_service_money '">
            <a-button type="link" class="h-auto! p-0!" @click="technicalHanlder('free_service_money', scope.record)"
              >¥{{ scope.record.free_service_money || 0 }}</a-button
            >
          </template>
          <template v-if="scope.column.dataIndex === 'status'">
            <a-space :size="[6, 0]">
              <span class="round" :style="{ background: statusType(scope.record?.status)?.color }"></span>
              <span>{{ statusType(scope.record?.status)?.text }}</span>
            </a-space>
          </template>
          <template v-if="scope.column.dataIndex === 'merchant_complaint_switch'">
            <a-switch
              ref="merchant_complaint_switch"
              @click="onSwitch($event, scope.record, 1)"
              :checkedValue="1"
              :unCheckedValue="0"
              v-model:checked="scope.record.merchant_complaint_switch"
            />
          </template>
          <template v-if="scope.column.dataIndex === 'channel_name'">
            <a-tooltip>
              <template #title>{{ scope.record?.channel_name }}</template>
              <div class="channel_name">
                {{ scope.record?.channel_name }}
              </div>
            </a-tooltip>
          </template>

          <template v-if="scope.column.dataIndex === 'mini_complaint_switch'">
            <a-switch
              ref="mini_complaint_switch"
              @click="onSwitch($event, scope.record, 2)"
              :checkedValue="1"
              :unCheckedValue="0"
              v-model:checked="scope.record.mini_complaint_switch"
            />
          </template>
          <template v-if="scope.column.dataIndex === 'platform_complaint_switch'">
            <a-switch
              ref="platform_complaint_switch"
              @click="onSwitch($event, scope.record, 3)"
              :checkedValue="1"
              :unCheckedValue="0"
              v-model:checked="scope.record.platform_complaint_switch"
            />
          </template>
          <template v-if="scope.column.dataIndex === 'fast_refund_control'">
            <a-switch
              ref="fast_refund_control"
              @click="onSwitch($event, scope.record, 4)"
              :checkedValue="1"
              :unCheckedValue="0"
              v-model:checked="scope.record.fast_refund_control"
            />
          </template>
          <template v-if="scope.column.dataIndex === 'customer_service_switch'">
            <a-switch
              ref="customer_service_switch"
              @click="customerServiceSwitch($event, scope.record)"
              :checkedValue="1"
              :unCheckedValue="0"
              v-model:checked="scope.record.customer_service_switch"
            />
          </template>
          <template v-if="scope.column.dataIndex === 'comment_switch'">
            <a-switch
              ref="comment_switch"
              @click="commentSwitch($event, scope.record)"
              :checkedValue="1"
              :unCheckedValue="0"
              v-model:checked="scope.record.comment_switch"
            />
          </template>
          <template v-if="scope.column.dataIndex === 'kefu_task_over'">
            <a-switch
              ref="kefu_task_over"
              @click="kefuTaskOverSwitch($event, scope.record)"
              :checkedValue="1"
              :unCheckedValue="2"
              v-model:checked="scope.record.kefu_task_over"
            />
          </template>
          <template v-if="scope.column.dataIndex === 'douyin_entities'">
            <div class="flex">
              <a-switch
                ref="douyin_entities"
                @click="douyinEntitiesSwitch(scope.record)"
                :checked="isChecked(scope.record.douyin_entities)"
              />
              <EditTwoTone
                v-if="scope.record.douyin_entities"
                class="font-size-20px ml-8px"
                @click="onCheckPart(scope.record)"
              />
            </div>
          </template>
          <template v-if="scope.column.dataIndex === 'video_switch'">
            <a-switch
              ref="video_switch"
              @click="videoSwitch($event, scope.record)"
              :checkedValue="1"
              :unCheckedValue="2"
              v-model:checked="scope.record.open_video"
            />
          </template>
          <template v-if="scope.column.dataIndex === 'gift_switch'">
            <a-switch
              ref="gift_switch"
              @click="giftSwitch($event, scope.record)"
              :checkedValue="1"
              :unCheckedValue="0"
              v-model:checked="scope.record.send_gift_switch"
            />
          </template>

          <template v-if="scope.column.dataIndex === 'auth_phone'">
            <a-switch
              ref="phone_switch"
              @click="phoneSwitch($event, scope.record)"
              :checkedValue="1"
              :unCheckedValue="2"
              v-model:checked="scope.record.auth_phone"
            />
          </template>
          <template v-if="scope.column.dataIndex === 'ratio'">
            <div>
              <div>自然流量：{{ scope.record.ratio || '0' }}%</div>
              <div>广告流量订单：{{ scope.record.ad_ratio || '0' }}%</div>
              <div>回流流量订单：{{ scope.record.callback_ratio || '0' }}%</div>
              <div>
                微信小店：<span v-if="scope.record.store_ratio">{{ scope.record.store_ratio || '0' }}%</span
                ><span v-else>--</span>
              </div>
            </div>
          </template>
          <template v-if="scope.column.dataIndex === 'recharge'">
            <div>
              <div>充值金额：¥{{ scope.record.account_money.recharge_money || '0' }}</div>
              <div>打款金额：¥{{ scope.record.account_money.transfer_money || '0' }}</div>
              <div>
                可用余额：¥{{ scope.record.account_money.remain_money || '0' }}
                <span class="link ml-10px" @click="balanceAccount(scope.record)"> 平账 </span>
              </div>
            </div>
          </template>
          <template v-if="scope.column.dataIndex === 'created_at'">
            <span>{{ formatDate(scope.record.created_at * 1000, 'YYYY-MM-DD HH:mm:ss') }}</span>
          </template>
          <template v-if="scope.column.dataIndex === 'handle'">
            <span v-auth="['sysShopChange']" class="link" @click="onStatus(scope.record)">
              {{ scope.record.status == 1 ? '禁用' : '启用' }}
            </span>
            <span
              v-auth="['sysShopEdit']"
              class="link"
              @click="router.push({ path: '/shop/customer/add-shop', query: { id: scope.record.shop_info.id } })"
            >
              编辑
            </span>
            <span
              class="link block"
              v-auth="['merchantAccessManage']"
              @click="handleMerchantAccess(scope.record, 'merchant')"
              >商户号管理</span
            >
            <span class="link block" v-auth="['securityConfig']" @click="handleSecurity('security', scope.record)"
              >保证金管理</span
            >
            <!-- <span class="link block" v-auth="['juliangConfig']" @click="handleJuliang(scope.record)">巨量配置</span> -->
            <span class="link block" v-auth="['juliangConfig']" @click="handleJuliang(scope.record)">巨量配置</span>
          </template>
        </template>
      </TableZebraCrossing>
      <div>
        <Pagination
          v-model:page="data.params.page"
          v-model:pageSize="data.params.page_size"
          :total="tableData.total"
          @change="getList"
        ></Pagination>
      </div>
      <a-modal
        v-model:open="dialog.visible"
        :title="dialog.title"
        :width="dialog.width"
        :footer="null"
        destroyOnClose
        @cancel="modalCancel"
      >
        <BalanceAccount @event="onEvent" :item="dialog.item" v-if="dialog.type == 'balanceAccount'" />
        <MerchantAccess v-if="dialog.type == 'merchant'" @event="onEvent" :item="dialog.item" />

        <additional-technical-service-charge-modal
          v-if="dialog.type === 'charge_service_money'"
          dialog_type="charge_service_money"
          :item="dialog.item"
          @event="onEvent"
        />
        <FreeServiceMoney
          v-if="dialog.type === 'free_service_money'"
          dialog_type="free_service_money"
          :item="dialog.item"
          @event="onEvent"
        />
        <UpdateRecords v-if="dialog.type === 'updateRecord'" ref="updateRecordsCom" />
        <SecurityFund v-if="dialog.type == 'security'" :item="dialog.item" @event="onEvent" />
        <CheckPart v-if="dialog.type == 'checkPart'" :item="dialog.item" @event="onEvent" />
        <DomainList v-if="dialog.type == 'domain'" :item="dialog.item" @event="onEvent" />
      </a-modal>
      <a-modal
        v-model:open="juLiangDialog.visible"
        :title="juLiangDialog.title"
        :width="juLiangDialog.width"
        :destroyOnClose="true"
        @ok="() => (juLiangDialog.visible = flase)"
        :footer="null"
      >
        <JuLiangTem ref="juliangRef" :item="juLiangDialog.item" @event="onEvent" />
      </a-modal>
    </template>
  </DesTablePage>
</template>

<script setup lang="tsx">
  defineOptions({ name: 'CustomerShop' })
  import Pagination from '@/components/ui/common/PaginationComponent/index.vue'
  import AdditionalTechnicalServiceChargeModal from './components/additionalTechnicalServiceChargeModal.vue'
  import FreeServiceMoney from './components/FreeServiceMoney.vue'
  import MerchantAccess from './components/MerchantAccess.vue'
  import BalanceAccount from './components/BalanceAccount.vue'
  import UpdateRecords from './components/UpdateRecords.vue'
  import JuLiangTem from './components/JuLiangConfig.vue'
  import SecurityFund from './components/SecurityFund.vue'
  import CheckPart from './components/CheckPart.vue'
  import DomainList from './components/DomainList.vue'
  import { createVNode, reactive, h, ref, nextTick, onActivated } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { message, Modal, notification } from 'ant-design-vue'
  import { ExclamationCircleOutlined, PlusOutlined, EditTwoTone, QuestionCircleFilled } from '@ant-design/icons-vue'
  import datas from './src/datas'
  import { exportCreate } from '@/api/common'
  import { formatDate } from '@/utils'
  import {
    setShopList,
    editShop,
    complaintSwitchApi,
    changeFastRefundApi,
    customerServiceSwitchApi,
    commentSwitchApi,
    videoSwitchApi,
    getCreateApi,
    giftSwitchApi,
    supportDouyin
  } from './shop.api'
  import { useTheme } from '@/hooks'
  import { cloneDeep } from 'lodash-es'
  import { setCompanyList } from '../../customer/company/index.api'
  import { useRouterBack, useAuth } from '@/hooks'
  const { routeParams, resetRouteParams } = useRouterBack()
  const router = useRouter()
  const route = useRoute()
  const merchant_complaint_switch = ref(null)
  const mini_complaint_switch = ref(null)
  const platform_complaint_switch = ref(null)
  const fast_refund_control = ref(null)
  const { isAuth } = useAuth()
  // 从hooks获取主题数据
  const { themeVar } = useTheme()

  const { searchList, columns, statusType } = datas()
  const tableData = reactive({
    list: [],
    total: 0,
    loading: false
  })

  const dialogText = ref('关闭后，商家可自行处理微信支付投诉，确认是否关闭?')

  const data = reactive({
    fast_refund_status: undefined, //判断是否可以开关是否极速退款按钮
    switchLoading: false,
    params: {
      page: 1,
      page_size: 10
    },
    companyList: [],
    switchParams: {
      type: null,
      switch: null,
      shop_id: null
    }
  })

  // 弹框
  const dialog = reactive({
    visible: false,
    title: '商户号管理',
    width: 1000,
    item: null,
    type: ''
  })
  const juLiangDialog = reactive({ visible: false, title: '巨量配置', width: 600, item: null, type: '' })
  const searchConfig = reactive({
    data: searchList,
    options: {
      foldNum: 0,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  })

  let company_id = route.query?.id || undefined
  data.params.company_id = company_id
  if (company_id) {
    searchList[2].value = typeof company_id !== 'object' ? Number(company_id) : undefined
  }
  const isChecked = (value: any) => {
    // 如果值是非空字符串，则认为开关是打开的
    return !!value
  }
  //接管售后客服开关
  const kefuTaskOverSwitch = async (checked: any, row: Object) => {
    row.kefu_task_over = checked == 2 ? 1 : 2
    try {
      let res = await customerServiceSwitchApi({ switch: checked, shop_id: row.id, type: 1 })
      message.success(res.msg)
      getList()
    } catch (error) {
      console.error(error)
    }
  }
  //是否支持巨量引擎
  const douyinEntitiesSwitch = (record: any) => {
    if (!record.douyin_entities) {
      onCheckPart(record)
    } else {
      Modal.confirm({
        title: '提示',
        content: '确认要关闭巨量引擎吗？',
        async onOk() {
          try {
            await supportDouyin({
              shop_id: record.id,
              douyin_entities: ''
            })
            message.success('操作成功')
            getList()
          } catch (error) {
            console.error('error', error)
          }
        }
      })
    }
  }
  // 商户号管理
  const onCheckPart = (record: any) => {
    dialog.item = record
    dialog.type = 'checkPart'
    dialog.title = '主体域名配置'
    dialog.visible = true
    dialog.width = 500
  }
  const searchForm = (values) => {
    data.params = {
      ...data.params,
      ...values.formData
    }
    data.params.created_at = !values.formData.created_at?.length
      ? ''
      : `${values.formData.created_at[0]}_${values.formData.created_at[1]}`
    data.params.order_create_at = !values.formData.order_create_at?.length
      ? ''
      : `${values.formData.order_create_at[0]}_${values.formData.order_create_at[1]}`
    data.params.page = 1
    getList()
  }

  // 获取店铺列表
  const getList = async () => {
    try {
      tableData.loading = true
      let res = await setShopList(data.params)
      tableData.list = (res.data.list || []).map((item) => {
        return {
          ...item,
          ...item.shop_info,
          id: item.shop_info.id,
          company_id: item.shop_info.company_id,
          code: item.shop_info.code,
          logo: item.shop_info.logo,
          name: item.shop_info.name,
          company_name: item.shop_info.company_name,
          on_sale: item.shop_info.on_sale,
          visit: item.shop_info.visit,
          complaint_ratio: item.shop_info.complaint_ratio,
          handle_complaint_ratio: item.shop_info.handle_complaint_ratio,
          status: item.shop_info.status,
          created_at: item.shop_info.created_at,
          creator: item.shop_info.creator,
          business_number: item.shop_info.business_number,
          ratio: item.shop_info.ratio,
          ad_ratio: item.shop_info.ad_ratio,
          callback_ratio: item.shop_info.callback_ratio,
          merchant_complaint_switch: item.shop_info.merchant_complaint_switch,
          mini_complaint_switch: item.shop_info.mini_complaint_switch,
          platform_complaint_switch: item.shop_info.platform_complaint_switch,
          fast_refund_control: item.shop_info.fast_refund_control,
          free_service_money: item.shop_info.free_service_money,
          charge_service_money: item.shop_info.charge_service_money
        }
      })
      tableData.total = res.data.total
      data.fast_refund_status = res.data?.fast_refund_status || undefined
    } catch (error) {
      console.error('获取店铺列表数据失败：', error)
    } finally {
      tableData.loading = false
    }
  }

  // 下单验证手机号开关事件
  const phoneSwitch = (checked: any, row: Object) => {
    row.auth_phone = checked == 2 ? 1 : 2
    let text = checked == 2 ? '确认要关闭下单验证手机号吗?' : '确认要开启下单验证手机号吗?'
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: text,
      async onOk() {
        try {
          let res = await customerServiceSwitchApi({ switch: checked, shop_id: row.id, type: 2 })
          message.success(res.msg)
          getList()
        } catch (error) {
          console.error(error)
        }
      }
    })
  }
  // 视频开关事件
  const videoSwitch = (checked: any, row: Object) => {
    row.open_video = checked == 2 ? 1 : 2
    let text = checked == 2 ? '关闭后，该店铺不显示【视频】' : '开启后，该店铺显示【视频】'
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: text,
      async onOk() {
        try {
          let res = await videoSwitchApi({ switch: checked, shop_id: row.id })
          message.success(res.msg)
          getList()
        } catch (error) {
          console.error(error)
        }
      }
    })
  }

  // 售前客服开关事件
  const customerServiceSwitch = (checked: any, row: Object) => {
    row.customer_service_switch = checked == 0 ? 1 : 0
    let text = checked == 0 ? '关闭后，该店铺商品详情页将不展示客服入口' : '开启后，该店铺商品详情页将展示客服入口'
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: text,
      async onOk() {
        try {
          let res = await customerServiceSwitchApi({ switch: checked, shop_id: row.id })
          message.success(res.msg)
          getList()
        } catch (error) {
          console.error(error)
        }
      }
    })
  }
  // 接管评价开关事件
  const commentSwitch = async (checked: any, row: Object) => {
    row.comment_switch = checked == 0 ? 1 : 0
    let text = '确认由总后台接管该店铺下的评价吗'
    if (checked) {
      Modal.confirm({
        title: '提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: text,
        async onOk() {
          try {
            onSwitchComment(checked, row)
          } catch (error) {
            console.error(error)
          }
        }
      })
    } else {
      onSwitchComment(checked, row)
    }
  }

  const onSwitchComment = async (checked, row) => {
    try {
      let res = await commentSwitchApi({ switch: checked, shop_id: row.id })
      message.success(res.msg)
      getList()
    } catch (error) {}
  }

  // 送礼物开关事件
  const giftSwitch = (checked: any, row: Object) => {
    row.send_gift_switch = checked == 0 ? 1 : 0
    let text = checked == 0 ? '确认要关闭送礼物功能吗？' : '确认要开启送礼物功能吗？'
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: text,
      async onOk() {
        try {
          let res = await giftSwitchApi({ switch: checked, shop_id: row.id })
          message.success(res.msg)
          getList()
        } catch (error) {
          console.error(error)
        }
      }
    })
  }

  // 开关点击事件
  const onSwitch = (checked: any, row: Object, type: Number) => {
    if (data.fast_refund_status != 1) {
      row.fast_refund_control = 2
      message.warning('平台暂不支持极速退款，无法自行控制')
      return
    }
    data.switchParams.type = type
    data.switchParams.switch = checked
    data.switchParams.shop_id = row.shop_info.id
    let text = type == 1 ? '微信支付投诉' : type == 2 ? '小程序投诉' : '平台自有投诉'
    if (checked == 0 || checked == 2) {
      dialogText.value =
        type == 4
          ? '小程序允许极速退款时，商家必须极速退款，无法自行控制是否开启'
          : `关闭后，商家可自行处理${text}，确认是否关闭?`
      switch (type) {
        case 1:
          row.merchant_complaint_switch = 1
          break
        case 2:
          row.mini_complaint_switch = 1
          break
        case 3:
          row.platform_complaint_switch = 1
          break
        case 4:
          row.fast_refund_control = 1
          break
      }
    } else {
      dialogText.value =
        type == 4
          ? '小程序允许极速退款，商家可自行控制是否开启极速退款'
          : `开启后，商家将不能处理${text}，确认是否开启?`
      switch (type) {
        case 1:
          row.merchant_complaint_switch = 0
          break
        case 2:
          row.mini_complaint_switch = 0
          break
        case 3:
          row.platform_complaint_switch = 0
          break
        case 4:
          row.fast_refund_control = 2
          break
      }
    }
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: dialogText.value,
      async onOk() {
        try {
          type == 4 ? fastMoneyBack(row) : complaintSwitch()
        } catch (error) {
          console.error(error)
        }
      }
    })
  }
  //商家自行控制是否极速退款
  const fastMoneyBack = async (row) => {
    try {
      data.switchLoading = false
      await changeFastRefundApi({
        fast_refund_control: row.fast_refund_control == 1 ? 2 : 1,
        shop_id: row.shop_info.id
      })
      message.success('操作成功')
    } catch (error) {
      console.error(error)
    } finally {
      data.switchLoading = true
      await getList()
    }
  }
  // 开关请求函数
  const complaintSwitch = async () => {
    try {
      let res = await complaintSwitchApi(data.switchParams)
      data.switchLoading = false
      data.open = false
      nextTick(() => {
        merchant_complaint_switch.value.blur()
        mini_complaint_switch.value.blur()
        platform_complaint_switch.value.blur()
        fast_refund_control.value.blur()
      })
      message.success(res.msg)
      getList()
    } catch (error) {}
  }

  // 获取所属公司列表
  const getCompanyList = async () => {
    try {
      let res = await setCompanyList({
        page: 1,
        page_size: 9999,
        status: 0
      })
      searchList[2].props.options = (res.data.list || []).map((item) => {
        return {
          label: item.company_info.name,
          value: item.company_info.id
        }
      })
    } catch (error) {
      // console.error('公司管理列表：', error)
    }
  }

  getCompanyList()
  getList()

  onActivated(() => {
    if (routeParams.params?.page) {
      if (routeParams.params?.page === 'add') {
        data.params.page = 1
        data.params.page_size = 10
      }
      getList()
      resetRouteParams()
    }
  })
  const pageChange = (datas, filters, sorter) => {
    console.log(datas, filters, sorter)
    data.params.order_str = sorter.field
    if (sorter?.order) {
      data.params.order_str_by = sorter.order === 'ascend' ? -1 : 1
    } else {
      data.params.order_str_by = undefined
      data.params.order_str = undefined
    }
    getList()
  }
  // 修改状态
  const onStatus = async (val) => {
    let text = {}
    text = val.status == 1 ? { name: '禁用', status: 2 } : { name: '启用', status: 1 }
    let title =
      val.status == 1 ? '店铺下所有商品将会强制下架，启用后会自动恢复上架，请确认是否禁用店铺' : '是否启用此店铺？'
    try {
      Modal.confirm({
        title: '提示',
        icon: createVNode(ExclamationCircleOutlined),
        content: title,
        async onOk() {
          try {
            let res = await editShop({
              id: val.shop_info.id,
              status: text.status,
              company_id: val.shop_info.company_id
            })
            message.success(res.msg)
            getList()
          } catch (error) {
            console.error(error)
          }
        }
      })
    } catch (error) {
      console.error('店铺列表修改状态失败：', error)
    }
  }

  // 导出
  const onExport = async () => {
    try {
      const v = cloneDeep(data.params)

      let params = {
        ...data.params,
        ...v,
        status: v.status || 0
      }
      let res = await exportCreate({
        type: 'shop',
        params: JSON.stringify(params)
      })
      notification.success({
        message: '导出成功',
        description: '请前往系统->下载中心查看',
        style: {
          color: themeVar.value.primaryColor,
          cursor: 'pointer'
        },
        onClick: () => router.push({ path: '/system/setUp/download' })
      })
    } catch (error) {
      console.error('店铺列表导出失败：', error)
    }
  }
  // 商户号管理
  const handleDomainList = (record: any, type) => {
    dialog.item = record
    dialog.type = type
    dialog.title = '域名'
    dialog.visible = true
    dialog.width = 680
  }
  // 商户号管理
  const handleMerchantAccess = (record: any, type) => {
    dialog.item = record
    dialog.type = type
    dialog.title = '商户号管理'
    dialog.visible = true
    dialog.width = 1000
    dialog.type = 'merchant'
  }
  // 平账
  const balanceAccount = (record: any) => {
    dialog.item = record
    dialog.visible = true
    dialog.title = '充值打款平账'
    dialog.width = 500
    dialog.type = 'balanceAccount'
  }
  // 巨量配置
  const handleJuliang = (record: any) => {
    juLiangDialog.item = record
    juLiangDialog.width = 600
    juLiangDialog.title = '巨量配置'
    juLiangDialog.visible = true
  }
  const onEvent = (obj: { cmd: any; data?: any }) => {
    switch (obj.cmd) {
      case 'close':
        dialog.visible = false
        juLiangDialog.visible = false
        if (['charge_service_money', 'free_service_money', 'balanceAccount'].includes(dialog.type)) {
          getList()
        }
        break
      case 'success':
        juLiangDialog.visible = false
        dialog.visible = false
        getList()
        break
    }
  }
  const modalCancel = () => {
    dialog.visible = false
    if (['charge_service_money', 'free_service_money'].includes(dialog.type)) {
      getList()
    }
  }
  const handleSecurity = (type: any, record: any) => {
    dialog.item = record
    dialog.type = type
    dialog.width = 600
    dialog.title = '保证金扣除'
    dialog.visible = true
  }
  const technicalHanlder = (type: string, item: any) => {
    if (!isAuth([type])) return
    dialog.item = item
    dialog.type = type
    dialog.width = 1000
    switch (type) {
      case 'charge_service_money':
        dialog.title = '额外技术服务费明细'
        dialog.visible = true
        break
      case 'free_service_money':
        dialog.title = '技术服务费减免明细'
        dialog.visible = true
        break
      case 'success':
        dialog.visible = false
        getList()
        break
    }
  }
  //获取创建人
  const getCreate = async () => {
    try {
      let res = await getCreateApi()
      searchList.forEach((item) => {
        if (item.field == 'creator_id') {
          let list = res.data.user_list
          list.forEach((target) => {
            target.value = target.id
            target.label = target.name
          })
          item.props.options = list
        }
      })
    } catch (error) {}
  }
  getCreate()

  //查看操作记录
  const queryUpdateRecords = () => {
    dialog.type = 'updateRecord'
    dialog.visible = true
    dialog.width = 1000
    dialog.title = '查看操作记录'
  }
</script>

<style lang="scss" scoped>
  :deep(.ant-picker-separator) {
    display: none;
  }
  .link {
    color: var(--primary-color);
    cursor: pointer;
    margin-right: 5px;
  }

  .link:nth-last-of-type(1) {
    margin-right: 0;
  }

  .font_red {
    color: var(--error-color);
  }

  .round {
    width: 8px;
    height: 8px;
    background: #404040;
    border-radius: 50%;
    display: block;
  }
  .channel_name {
    width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
