<template>
  <div class="page_goods_details">
    <a-card style="margin-bottom: 10px">
      <div class="flex flex-justify-between">
        <a-breadcrumb separator="/">
          <a-breadcrumb-item>
            <router-link :to="{ path: '/shop/goods/list' }">商品列表</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>商品详情</a-breadcrumb-item>
        </a-breadcrumb>
        <!-- <div v-if="msgOldData.audit_num" @click="state.showOldData = true" class="btn-compare">
          <img :src="requireImg('goods/compare.png')" alt="" class="mr-8px" />
          查看版本对比
        </div> -->
      </div>

      <div class="flex">
        <!-- 首次审核，不显示 -->
        <div class="model_block bor_6 left-info" style="padding: 20px 10px 10px" v-if="msgOldData.on_sale > 0">
          <div
            v-if="msgOldData.on_sale > 0"
            class="font-size-14px bg-#EBF0EB c-#1AAD19 h-33px line-height-33px text-center border-rd-2px mb-8px"
          >
            线上版本
          </div>
          <a-descriptions class="descriptions" title="小店信息" :column="1" :colon="false" v-if="msgOldData.type === 4">
            <a-descriptions-item label="小店名称:" :class="addDiffClass(['wechat_store_name'])">{{
              msgOldData.wechat_store_name || '--'
            }}</a-descriptions-item>
            <a-descriptions-item label="小店商品:" :class="addDiffClass(['wechat_store_product_name'])">
              <span>{{ msgOldData.wechat_store_product_name || '--' }}</span>
              <a-popover
                :overlay-inner-style="{ padding: 0 }"
                trigger="click"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
              >
                <template #content>
                  <!-- <a-qrcode :value="state.oldQrCode" :bordered="false" />
                      -->
                  <img v-show="state.oldQrCode" :src="state.oldQrCode" alt="" class="w134px h134px" />
                </template>
                <span
                  class="ml24px c-#1677ff cursor-pointer"
                  v-show="msgOldData.wechat_store_product_id"
                  @click="requestQrcode('old', msgOldData.wechat_store_appid, msgOldData.wechat_store_product_id)"
                  >查看详情</span
                >
              </a-popover>
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="基本信息" :column="1" :colon="false">
            <template v-if="msgOldData.type !== 4">
              <a-descriptions-item label="商品类型:">{{
                msgOldData.type === 1 ? '普通商品' : msgOldData.type === 2 ? '跨境商品' : '香港直邮'
              }}</a-descriptions-item>
            </template>
            <template v-else>
              <a-descriptions-item label="商品类型:">微信小店</a-descriptions-item>
            </template>
            <a-descriptions-item label="商品类目:" :class="addDiffClass(['category_names'])">{{
              msgOldData.category_names || '--'
            }}</a-descriptions-item>

            <template v-if="msgOldData.shop_certificate_product_name">
              <a-descriptions-item label="主体名称:" :class="addDiffClass(['shop_entity_name'])">{{
                msgOldData.shop_entity_name || '--'
              }}</a-descriptions-item>
              <a-descriptions-item label="医药类型:" :class="addDiffClass(['shop_certificate_name'])">{{
                msgOldData.shop_certificate_name || '--'
              }}</a-descriptions-item>
              <a-descriptions-item label="医药资质:" :class="addDiffClass(['certificate_img_list'])">
                <div class="flex flex-wrap">
                  <div
                    class="mb-8px text-center w-80px"
                    v-for="item in msgOldData.certificate_img_list?.filter((it) => it.url)"
                  >
                    <div class="h-60px flex-y-center justify-center">
                      <a-image :width="40" :height="40" class="image" :src="item.url"></a-image>
                    </div>
                    <div class="mt-5px">
                      {{ item.expire_at !== '1970-01-01' ? item.expire_at : '长期有效' }}
                    </div>
                  </div>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="产品名称:" :class="addDiffClass(['shop_certificate_product_name'])">{{
                msgOldData.shop_certificate_product_name || '--'
              }}</a-descriptions-item>
              <a-descriptions-item label="备案号:" :class="addDiffClass(['shop_certificate_product_code'])"
                >{{ msgOldData.shop_certificate_product_code || '--' }}
                <a
                  class="ml-12px c-#647DFF"
                  target="_blank"
                  href="https://www.nmpa.gov.cn/datasearch/home-index.html#category=ylqx"
                  >国家药品监督管理局</a
                >
              </a-descriptions-item>

              <a-descriptions-item label="备案证书:" :class="addDiffClass(['shop_certificate_product_img_list'])">
                <div class="flex flex-wrap">
                  <div class="mb-8px mr-8px" v-for="item in msgOldData.shop_certificate_product_img_list">
                    <a-image :width="40" :height="40" class="image" :src="item"></a-image>
                  </div>
                </div>
              </a-descriptions-item>
            </template>

            <a-descriptions-item label="商品名称:" :class="addDiffClass(['title'])">{{
              msgOldData.title || '--'
            }}</a-descriptions-item>
            <a-descriptions-item label="详情页标题:" :class="addDiffClass(['goods_sub_title'])">{{
              msgOldData.goods_sub_title || '--'
            }}</a-descriptions-item>
            <a-descriptions-item label="备注:" :class="addDiffClass(['desc'])">{{
              msgOldData.desc || '--'
            }}</a-descriptions-item>
            <a-descriptions-item
              label="推荐标签:"
              :class="addDiffClass(['recommend_tag_color', 'recommend_tag_txt', 'recommend_tag'])"
            >
              <a-tag
                :color="msgOldData.recommend_tag_color"
                style="color: #fff"
                :style="{ background: msgOldData.recommend_tag_color }"
                >{{ msgOldData.recommend_tag || '--' }}</a-tag
              >
              <span
                style="font-size: 14px; margin-left: 10px"
                :style="{
                  color:
                    msgOldData.recommend_tag_color == 'linear-gradient(90deg, #ff9000 0%, #ff5000 100%)'
                      ? '#ff5000'
                      : msgOldData.recommend_tag_color
                }"
                >{{ msgOldData.recommend_tag_txt || '--' }}</span
              >
            </a-descriptions-item>
            <a-descriptions-item
              label="销售标签:"
              :class="addDiffClass(['sale_tag_color', 'sale_tag_txt', 'sale_tag'])"
            >
              <a-tag :color="msgOldData.sale_tag_color" style="color: #fff" :style="{ background: '#BC8855' }">{{
                msgOldData.sale_tag || '--'
              }}</a-tag>
              <span
                style="font-size: 14px; margin-left: 10px"
                :style="{
                  color:
                    msgOldData.sale_tag_color == 'linear-gradient(90deg, #ff9000 0%, #ff5000 100%)'
                      ? '#ff5000'
                      : msgOldData.sale_tag_color
                }"
                >{{ msgOldData.sale_tag_txt || '--' }}</span
              >
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="销售信息" :column="1" :colon="false">
            <a-descriptions-item v-if="msgOldData.type === 2" label="税费:">免税</a-descriptions-item>
            <a-descriptions-item label="活动销售价:" :class="addDiffClass(['price'])"
              >￥{{ msgOldData.price?.toFixed(2) || '--' }}</a-descriptions-item
            >
            <a-descriptions-item label="划线价:" :class="addDiffClass(['old_price'])"
              >￥{{ msgOldData.old_price?.toFixed(2) || '--' }}</a-descriptions-item
            >
            <!-- <a-descriptions-item label="总库存:" :class="addDiffClass(['stock_num'])">{{
            msgData.stock_num || '--'
          }}</a-descriptions-item> -->
            <a-descriptions-item label="商品编码:" :class="addDiffClass(['product_code'])">{{
              msgOldData.product_code || '--'
            }}</a-descriptions-item>
            <!-- <a-descriptions-item label="同步ERP:" v-if="!useInfo.is_private_platform">
              <span v-if="form.erp_id">{{
                `${form.erp_id == 1 ? '聚水潭' : form.erp_id == 2 ? '旺店通' : '店管家'} ${form.name || '-'}    ${
                  form.erp_shop_name || '-'
                }  ${form.erp_shop_id || '-'}`
              }}</span>
              <span v-else>--</span>
            </a-descriptions-item> -->
            <!-- <a-descriptions-item label="活动文案:">{{ msgData.active_desc || '--' }}</a-descriptions-item> -->
            <a-descriptions-item label="价格设置:" class="sku-item" :class="addDiffClass(['sku_list', 'spec_list'])">
              <div class="table_box" :style="{ height: calcHeight(oldTableData.list, tableData.list, 'sku') + 'px' }">
                <a-table
                  :dataSource="oldTableData.list"
                  :columns="oldTableData.columns"
                  :row-class-name="(_record) => (_record._isDifferent ? 'hight-row' : null)"
                  :scroll="{ x: 'max-content' }"
                  bordered
                  :pagination="false"
                >
                  <template #bodyCell="{ column, record, index }">
                    <template v-if="column.isLabel">
                      <div class="min-w60px">
                        <span class="cursor-pointer absolute" style="top: 16px; right: 5px">
                          <LabelTag
                            v-if="setTag(record, column.key)"
                            :label-text="setTag(record, column.key)"
                            :icon="setTag(record, column.key) === '热销'"
                          ></LabelTag>
                        </span>
                        <a-tooltip placement="topLeft">
                          <template #title>{{ record[column.key] }}</template>
                          <div class="text_overflow_row2 mt20px">
                            <span>{{ record[column.key] }}</span>
                          </div>
                        </a-tooltip>
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'idx'">{{ index + 1 }}</template>
                    <template v-if="column.dataIndex === 'name'">
                      <a-image style="width: 50px; height: 50px" :src="record.image_url" fit="cover" :fallback="error">
                        <template #error>
                          <div class="error">暂无图片</div>
                        </template>
                      </a-image>
                    </template>
                    <template v-if="column.dataIndex === 'spec_key'">
                      {{ record.spec_key }}
                    </template>
                    <template v-if="column.dataIndex === 'price'">
                      {{ record.price?.toFixed(2) }}
                    </template>
                    <template v-if="column.dataIndex === 'old_price'">
                      {{ record.old_price?.toFixed(2) }}
                    </template>
                    <!-- <template v-if="column.dataIndex === 'stock_num'"> {{ record.stock_num }}件 </template> -->
                    <template v-if="column.dataIndex === 'code'"> {{ record.code }} </template>
                    <template v-if="column.dataIndex === 'isSell'">
                      {{ record.on_sale == 1 ? '售卖' : '缺货' }}
                    </template>
                    <template v-if="column.dataIndex === 'isDefaultPrice'">
                      <a-radio :checked="true" v-if="record.is_default_price == 1"></a-radio>
                    </template>
                    <template v-if="column.dataIndex === 'isDefault'">
                      <a-radio :checked="true" v-if="record.is_default == 1"></a-radio>
                    </template>
                    <template v-if="column.dataIndex === 'tax_rate'"> {{ record.tax_rate || 0 }}% </template>
                  </template>
                </a-table>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="规格展现形式:" :class="addDiffClass(['sku_show_style'])">
              {{ msgOldData.sku_show_style === 2 ? '大图模式' : '列表模式' }}
            </a-descriptions-item>
            <!-- <a-descriptions-item v-else label="价格设置:">
              <a-empty style="height: 265px" />
            </a-descriptions-item> -->
          </a-descriptions>

          <!-- 跨境信息 -->
          <!-- <CrossBorderInfo v-if="msgOldData.type === 2" :msgData="msgOldData" :addDiffClass="addDiffClass" /> -->

          <a-descriptions class="descriptions" title="商品介绍" :column="1" :colon="false">
            <a-descriptions-item label="主图视频:">
              <div class="item flex" v-if="msgOldData?.product_video">
                <!-- <video :src="msgOldData?.product_video" controls="controls" class="imgg">
                  您的浏览器不支持 video 标签。
                </video> -->
                <FilePreview :src="msgOldData?.product_video" style="width: 101px; height: 101px"></FilePreview>
                <div class="item-mark flex-center" v-if="[0, 4, 5].includes(msgOldData.product_video_status)">
                  <SvgIcon class="w-24px h-24px" icon="video-tips" />
                  <span class="font-size-12px c-#fff mt-16px line-height-17px">{{
                    msgOldData.product_video_status == 4
                      ? '视频已下线'
                      : msgOldData.product_video_status == 5
                        ? '视频已禁用'
                        : '视频已失效'
                  }}</span>
                </div>
              </div>
              <div v-else class="h-111px">--</div>
            </a-descriptions-item>
            <a-descriptions-item
              label="主图:"
              :class="addDiffClass(['image_ids'])"
              :style="{ height: calcHeight(msgOldData?.image_ids, msgData?.image_ids, 'mainPic') + 'px' }"
            >
              <div class="image_list" v-if="msgOldData?.image_ids?.length">
                <a-image-preview-group>
                  <div
                    class="img_box"
                    v-for="(item, i) in msgOldData.image_ids"
                    :key="item"
                    :class="{ hight: item._isDifferent }"
                  >
                    <a-image class="img" :src="item.url" fit="cover" :preview-src-list="msgOldData.image_ids">
                    </a-image>
                    <div class="title text_overflow" v-if="i == 0">商品主图</div>
                  </div>
                </a-image-preview-group>
              </div>
              <div v-else>--</div>
            </a-descriptions-item>
            <a-descriptions-item label="商品详情:" :class="addDiffClass(['detail_image'])">
              <div
                class="image_list overflow-auto content-start"
                v-if="msgOldData?.detail_image?.length"
                :style="{ height: calcHeight(msgOldData?.detail_image, msgData?.detail_image, 'detailPic') + 'px' }"
              >
                <a-image-preview-group>
                  <div
                    class="img_box"
                    v-for="(item, i) in msgOldData.detail_image"
                    :key="item"
                    :class="{ hight: item._isDifferent }"
                  >
                    <a-image class="img" :src="item.src"></a-image>
                  </div>
                </a-image-preview-group>
              </div>
              <div
                v-else
                :style="{ height: calcHeight(msgOldData?.detail_image, msgData?.detail_image, 'detailPic') + 'px' }"
              >
                --
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="商品属性:" :class="addDiffClass(['attribute'])">
              <div
                style="display: inline-block; overflow: auto"
                :style="{ height: calcHeight(oldAttrTableData.list, attrTableData.list, 'attribute') + 'px' }"
              >
                <a-table
                  :dataSource="oldAttrTableData.list"
                  :columns="oldAttrTableData.columns"
                  :row-class-name="(_record) => (_record._isDifferent ? 'hight-row' : null)"
                  bordered
                  :pagination="false"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'attr_val'">
                      <template v-if="record.attr_val"> {{ record.attr_val || '--' }} </template>
                    </template>
                  </template>
                </a-table>
              </div>
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="物流信息" :column="1" :colon="false">
            <a-descriptions-item
              label="商品运费:"
              :class="addDiffClass(['freight_type', 'freight', 'deliver_temp_id'])"
            >
              <template v-if="msgOldData.freight_type == 1">商家承担运费 </template>
              <template v-else-if="msgOldData.freight_type == 3">{{ msgOldData.deliver_temp_name || '--' }} </template>
              <template v-else> 固定运费(￥{{ msgOldData.freight?.toFixed(2) }}元) </template>
            </a-descriptions-item>
            <a-descriptions-item label="寄件地址:" :class="addDiffClass(['send_address_id'])">
              <template v-if="msgOldData.send_address_id">
                <div v-if="msgOldData.senderAddressInfo" style="height: 50px">
                  <div>{{ `${msgOldData.senderAddressInfo.area_name}${msgOldData.senderAddressInfo.address}` }}</div>
                  <div>{{ `${msgOldData.senderAddressInfo.name} ${msgOldData.senderAddressInfo.phone}` }}</div>
                </div>
                <div v-else>--</div>
              </template>
              <template v-else> <div style="height: 50px">--</div></template>
            </a-descriptions-item>
            <a-descriptions-item label="售后地址:" :class="addDiffClass(['shop_address_id'])">
              <template v-if="msgOldData.shop_address_id">
                <div v-if="msgOldData.shopAddressInfo" style="height: 50px">
                  <div>{{ `${msgOldData.shopAddressInfo.area_name}${msgOldData.shopAddressInfo.address}` }}</div>
                  <div>{{ `${msgOldData.shopAddressInfo.name} ${msgOldData.shopAddressInfo.phone}` }}</div>
                  <a-tooltip>
                    <template #title> {{ `${msgOldData.shopAddressInfo.warehouse_name}` }} </template>
                    <div class="text_overflow max-w-240px">
                      {{ `${msgOldData.shopAddressInfo.warehouse_name}` || '--' }}
                    </div>
                  </a-tooltip>
                  <a-tooltip>
                    <template #title> {{ `${msgOldData.shopAddressInfo.warehouse_desc}` }} </template>
                    <div class="text_overflow max-w-240px">
                      {{ `${msgOldData.shopAddressInfo.warehouse_desc}` || '--' }}
                    </div>
                  </a-tooltip>
                </div>
                <div v-else>--</div>
              </template>
              <template v-else> <div style="height: 50px">--</div></template>
            </a-descriptions-item>
            <!-- <a-descriptions-item label="非配送区域:" :class="addDiffClass(['undeliver_id'])">
          <div class="wrapper">
            <input :id="'exp1'" class="exp" type="checkbox" />
            <div class="text">
              <label class="btn" :for="'exp1'"></label>
              {{ msgData.citys || '--' }}
            </div>
          </div>
        </a-descriptions-item> -->
          </a-descriptions>
          <a-descriptions class="descriptions" title="评论设置" :column="1" :colon="false">
            <a-descriptions-item label="评论组:" :class="addDiffClass(['comment_temp_name'])">
              <div class="flex">
                {{ msgOldData.comment_temp_name || `--` }}
                <a-button
                  type="link"
                  class="h-0 pt-0"
                  v-if="msgOldData.comment_temp_name"
                  @click="onShowDialog('comment', msgOldData)"
                  >查看详情</a-button
                >
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="好评标签:" :class="addDiffClass(['tag_list'])">
              <GoodReviewsLabel
                v-model="msgOldData.tag_list"
                :style="{ height: calcHeight(msgOldData.tag_list, msgData.tag_list, 'attribute') + 'px' }"
              />
            </a-descriptions-item>
            <a-descriptions-item label="问答组:" :class="addDiffClass(['question_temp_name'])">
              <div class="flex">
                {{ msgOldData.question_temp_name || `--` }}
                <a-button
                  type="link"
                  class="h-0 pt-0"
                  v-if="msgOldData.question_temp_name"
                  @click="onShowDialog('qa', msgOldData)"
                  >查看详情</a-button
                >
              </div>
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="支付设置" :column="1" :colon="false">
            <a-descriptions-item label="限购方式:">
              {{ msgOldData.limit_type == 1 ? '不限购' : '限购' }}
              <a-tooltip>
                <template #title> 商品自然流量及广告引流订单，均生效限购设置 </template>
                <img :src="requireImg('goods/bangzhu.png')" class="w-12px h-12px cursor-pointer ml-3px" />
              </a-tooltip>
            </a-descriptions-item>
            <a-descriptions-item label="营销文案:" v-if="msgOldData.limit_type == 2">
              {{ msgOldData.limit_desc || `--` }}
              <a-tooltip placement="right">
                <template #title>
                  <img :src="requireImg('goods/goods-sku.png')" />
                </template>
                <img :src="requireImg('goods/bangzhu.png')" class="w-12px h-12px cursor-pointer ml-3px" />
              </a-tooltip>
            </a-descriptions-item>
            <a-descriptions-item label="限购数量:" v-if="msgOldData.limit_type == 2" :class="compareData">
              每次最多购买{{ msgOldData.limit_once_max_num || '--' }}件 <span class="mr-10px"></span> 累计最多购买{{
                msgOldData.limit_total_num || '--'
              }}件 <span class="mr-10px"></span>每次最少购买{{ msgOldData.limit_once_min_num || '--' }}件
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="营销方案" :column="1" :colon="false">
            <a-descriptions-item label="销量:" :class="addDiffClass(['sales_type'])">{{
              msgOldData.sales_initial
            }}</a-descriptions-item>
            <a-descriptions-item label="跑马灯样式:" :class="addDiffClass(['rolling'])">
              <Supernatant :type="msgOldData.rolling" />
            </a-descriptions-item>
            <a-descriptions-item label="限时活动:" :class="addDiffClass(['limit_active'])">
              <span class="flex">
                <!-- <span v-if="msgData.limit_active == 1" style="margin-right: 20px;">{{ msgData.active_desc }}</span> -->
                <Supernatant :type="msgOldData.limit_active == 1 ? 8 : 4" />
              </span>
            </a-descriptions-item>
            <!-- <a-descriptions-item
              label="活动文案:"
              v-if="msgOldData.limit_active != 2"
              :class="addDiffClass(['active_desc'])"
            >
              <span class="flex">
                <span style="margin-right: 20px">{{ msgOldData.active_desc || '--' }}</span>
              </span>
            </a-descriptions-item> -->
            <a-descriptions-item label="按钮文案:" :class="addDiffClass(['button_text'])">
              {{ msgOldData.button_text }}
            </a-descriptions-item>
            <a-descriptions-item label="购物保障:" :class="addDiffClass(['ensure'])">
              {{ msgOldData.ensure == 1 ? '显示' : '不显示' }}
            </a-descriptions-item>
            <a-descriptions-item label="添加到店铺首页:" :class="addDiffClass(['is_brand_shop'])">
              {{ msgOldData.is_brand_shop == 1 ? '显示' : '不显示' }}
            </a-descriptions-item>
            <a-descriptions-item label="店铺入口:" :class="addDiffClass(['is_show_shop'])">
              {{ msgOldData.is_show_shop == 1 ? '显示' : '不显示' }}
            </a-descriptions-item>
            <a-descriptions-item
              label="售后返现:"
              :class="addDiffClass(['rebate_config'])"
              :style="{
                height: calcHeight(msgOldData.rebate_config.list, msgData.rebate_config.list, 'rebate_config') + 'px'
              }"
            >
              <div class="flex-col">
                {{ msgOldData.rebate_config ? '开启' : '关闭' }}
                <div v-if="msgOldData.rebate_config">
                  <div>返现规则：{{ msgOldData.rebate_config.type == 1 ? '适用所有规格' : '指定金额' }}</div>
                  <div v-if="msgOldData.rebate_config.type == 2">
                    <div v-for="item in msgOldData.rebate_config.list" class="ml-70px">
                      <div>实付金额满{{ item.min_price }}元，返现{{ item.money }}元</div>
                    </div>
                  </div>
                  <div v-if="msgOldData.rebate_config.type == 1">返现{{ msgOldData.rebate_config.money }}元</div>
                </div>
              </div>
            </a-descriptions-item>
            <a-descriptions-item
              label="送礼物:"
              :class="addDiffClass(['send_gift_switch'])"
              v-if="msgOldData.type == 1"
            >
              {{ msgOldData.send_gift_switch == 1 ? '开启' : '关闭' }}
            </a-descriptions-item>
          </a-descriptions>
          <!-- <a-descriptions class="descriptions" title="腾讯广告商品库" :column="1" :colon="false">
            <a-descriptions-item label="投放设置:">
              <span class="c-#404040">{{ msgOldData.is_async_gdt ? '同步至微信商品库' : '--' }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="商品库:">
              <span class="flex">
                <span style="margin-right: 20px">{{ msgOldData.gdt_library_id || '--' }}</span>
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="微信类目:">
              {{ msgOldData.gdt_category_name }}
            </a-descriptions-item>
            <a-descriptions-item
              label="商品资质:"
              :style="{ height: calcHeight(msgOldData?.gdt_aptitude_img, msgData?.gdt_aptitude_img, 'cardPic') + 'px' }"
            >
              <div class="table_box">
                <div class="image_list" v-if="msgOldData?.gdt_aptitude_img?.length">
                  <a-image-preview-group>
                    <div class="img_box" v-for="(item, i) in msgOldData.gdt_aptitude_img" :key="i">
                      <a-image class="img" :src="item.img" />
                      <div class="c-#404040">{{ cardInfo[item.card_type] }}</div>
                    </div>
                  </a-image-preview-group>
                </div>
                <div v-else>--</div>
              </div>
            </a-descriptions-item>
          </a-descriptions> -->
          <a-descriptions
            class="descriptions"
            title="服务保障"
            :column="1"
            :colon="false"
            :class="addDiffClass(['protection', 'send_time'])"
          >
            <a-descriptions-item
              label="购物保障:"
              class="des_lable_hide"
              :class="addDiffClass(['protection', 'send_time'])"
            >
              <!-- <a-checkbox-group :value="[1, 2, 3]" class="guarantees"> -->
              <div class="guarantees">
                <template v-for="v in guaranteeData">
                  <a-checkbox
                    v-if="msgOldData.protection && msgOldData.protection.includes(v.id)"
                    :key="v.id"
                    :label="v.id"
                    :checked="true"
                    :disabled="true"
                  >
                    <div class="label">{{ v.label }}</div>
                    <div class="desc">{{ v.value }}</div>
                    <template v-if="v.id == 2">
                      <div class="mt-3px font-size-14px font-600 c-#404040">发货时效</div>
                      <div class="deliver_radio">
                        <a-radio v-if="msgOldData.send_time == 1" :checked="true" :disabled="true" size="large"
                          >24H发货</a-radio
                        >
                        <a-radio v-else-if="msgOldData.send_time == 2" :checked="true" size="large">48H</a-radio>
                        <a-radio v-else-if="msgOldData.send_time == 3" :checked="true" size="large">72H</a-radio>
                        <a-radio v-else-if="msgOldData.send_time == 4" :checked="true" size="large">12H</a-radio>
                      </div>
                      <div class="desc" v-if="[1, 2, 3, 4].includes(msgOldData.send_time)">
                        商家已经开通{{
                          sendTimeFilter(msgOldData.send_time)
                        }}小时发货延必赔服务，承诺在支付成功次日零时起{{
                          sendTimeFilter(msgOldData.send_time)
                        }}小时内为您发货，如商家未能如约发货，买家可在此笔订单中申请违约赔付；
                      </div>
                      <!-- 下架春节停发-暂时注释 -->
                      <template v-if="false">
                        <a-checkbox
                          class="mb-6px! mt-20px min-h-20px!"
                          disabled
                          :checked="msgOldData.year_stop_send === 1 ? true : false"
                        >
                          <span class="c-#404040" :class="addDiffClass(['year_stop_send'])">春节停发</span>
                        </a-checkbox>
                        <div
                          v-if="msgOldData.year_stop_send === 1"
                          class="c-#404040"
                          :class="addDiffClass(['stop_start_time', 'stop_end_time'])"
                        >
                          {{ dayjs.unix(msgOldData?.stop_start_time).format('YYYY-MM-DD') }}至{{
                            dayjs.unix(msgOldData?.stop_end_time).format('YYYY-MM-DD')
                          }}不发货
                        </div>
                      </template>
                    </template>
                  </a-checkbox>
                </template>
              </div>
              <!-- </a-checkbox-group> -->
            </a-descriptions-item>
          </a-descriptions>
          <!-- 下单验证 -->
          <a-descriptions
            class="descriptions"
            title="下单验证"
            :column="1"
            :colon="false"
            v-if="msgOldData?.shop_auth_phone == 1"
          >
            <a-descriptions-item label="下单验证手机号：">
              {{ msgOldData?.auth_phone == 1 ? '开启' : '关闭' }}
            </a-descriptions-item>
          </a-descriptions>
          <UserNotice :addDiffClass="addDiffClass" :msg-data="msgOldData" />
        </div>
        <div
          class="model_block bor_6 right-info pos-relative"
          style="padding: 20px 10px 10px"
          v-if="msgData.status == 1 || msgData.status == 3"
        >
          <!-- <CloseOutlined
            v-if="state.showOldData"
            class="right-close pos-absolute right-12px"
            @click="state.showOldData = false"
          /> -->
          <div
            v-if="msgData.status == 1 || msgData.status == 3"
            class="font-size-14px bg-#F0F0F0 c-#0E1726 h-33px line-height-33px text-center border-rd-2px mb-8px"
          >
            {{ msgData.status == 3 ? '审核被拒版本' : '审核版本' }}
          </div>
          <a-descriptions class="descriptions" title="小店信息" :column="1" :colon="false" v-if="msgData.type === 4">
            <a-descriptions-item label="小店名称:" :class="addDiffClass(['wechat_store_name'])">{{
              msgData.wechat_store_name || '--'
            }}</a-descriptions-item>
            <a-descriptions-item label="小店商品:" :class="addDiffClass(['wechat_store_product_name'])">
              <span>{{ msgData.wechat_store_product_name || '--' }}</span>

              <a-popover
                :overlay-inner-style="{ padding: 0 }"
                trigger="click"
                :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
              >
                <template #content>
                  <!-- <a-qrcode :value="state.newQrCode" :bordered="false" /> -->
                  <img v-if="state.newQrCode" :src="state.newQrCode" alt="" class="w134px h134px" />
                </template>
                <span
                  class="ml24px c-#1677ff cursor-pointer"
                  v-show="msgData.wechat_store_product_id"
                  @click="requestQrcode('new', msgData.wechat_store_appid, msgData.wechat_store_product_id)"
                  >查看详情</span
                >
              </a-popover>
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="基本信息" :column="1" :colon="false">
            <template v-if="msgData.type !== 4">
              <a-descriptions-item label="商品类型:">{{
                msgData.type === 1 ? '普通商品' : msgData.type === 2 ? '跨境商品' : '香港直邮'
              }}</a-descriptions-item>
            </template>
            <template v-else>
              <a-descriptions-item label="商品类型:">微信小店</a-descriptions-item>
            </template>
            <a-descriptions-item label="商品类目:" :class="addDiffClass(['category_names'])">{{
              msgData.category_names || '--'
            }}</a-descriptions-item>

            <template v-if="msgData.shop_certificate_product_name">
              <a-descriptions-item label="主体名称:" :class="addDiffClass(['shop_entity_name'])">{{
                msgData.shop_entity_name || '--'
              }}</a-descriptions-item>
              <a-descriptions-item label="医药类型:" :class="addDiffClass(['shop_certificate_name'])">{{
                msgData.shop_certificate_name || '--'
              }}</a-descriptions-item>
              <a-descriptions-item label="医药资质:" :class="addDiffClass(['certificate_img_list'])">
                <div class="flex flex-wrap">
                  <div
                    class="mb-8px text-center w-80px"
                    v-for="item in msgData.certificate_img_list.filter((it) => it.url)"
                  >
                    <div class="h-60px flex-y-center justify-center">
                      <a-image :width="40" :height="40" class="image" :src="item.url"></a-image>
                    </div>
                    {{ item.expire_at !== '1970-01-01' ? item.expire_at : '长期有效' }}
                  </div>
                </div>
              </a-descriptions-item>
              <a-descriptions-item label="产品名称:" :class="addDiffClass(['shop_certificate_product_name'])">{{
                msgData.shop_certificate_product_name || '--'
              }}</a-descriptions-item>
              <a-descriptions-item label="备案号:" :class="addDiffClass(['shop_certificate_product_code'])"
                >{{ msgData.shop_certificate_product_code || '--' }}
                <a
                  class="ml-12px c-#647DFF"
                  target="_blank"
                  href="https://www.nmpa.gov.cn/datasearch/home-index.html#category=ylqx"
                  >国家药品监督管理局</a
                >
              </a-descriptions-item>

              <a-descriptions-item label="备案证书:" :class="addDiffClass(['shop_certificate_product_img_list'])">
                <div class="flex flex-wrap">
                  <div class="mb-8px mr-8px" v-for="item in msgData.shop_certificate_product_img_list">
                    <a-image :width="40" :height="40" class="image" :src="item"></a-image>
                  </div>
                </div>
              </a-descriptions-item>
            </template>

            <a-descriptions-item label="商品名称:" :class="addDiffClass(['title'])">{{
              msgData.title || '--'
            }}</a-descriptions-item>
            <a-descriptions-item label="详情页标题:" :class="addDiffClass(['goods_sub_title'])">{{
              msgData.goods_sub_title || '--'
            }}</a-descriptions-item>
            <a-descriptions-item label="备注:" :class="addDiffClass(['desc'])">{{
              msgData.desc || '--'
            }}</a-descriptions-item>
            <a-descriptions-item
              label="推荐标签:"
              :class="addDiffClass(['recommend_tag_color', 'recommend_tag_txt', 'recommend_tag'])"
            >
              <a-tag
                :color="msgData.recommend_tag_color"
                style="color: #fff"
                :style="{ background: msgData.recommend_tag_color }"
                >{{ msgData.recommend_tag || '--' }}</a-tag
              >
              <span
                style="font-size: 14px; margin-left: 10px"
                :style="{
                  color:
                    msgData.recommend_tag_color == 'linear-gradient(90deg, #ff9000 0%, #ff5000 100%)'
                      ? '#ff5000'
                      : msgData.recommend_tag_color
                }"
                >{{ msgData.recommend_tag_txt || '--' }}</span
              >
            </a-descriptions-item>
            <a-descriptions-item
              label="销售标签:"
              :class="addDiffClass(['sale_tag_color', 'sale_tag_txt', 'sale_tag'])"
            >
              <a-tag :color="msgData.sale_tag_color" style="color: #fff" :style="{ background: '#BC8855' }">{{
                msgData.sale_tag || '--'
              }}</a-tag>
              <span
                style="font-size: 14px; margin-left: 10px"
                :style="{
                  color:
                    msgData.sale_tag_color == 'linear-gradient(90deg, #ff9000 0%, #ff5000 100%)'
                      ? '#ff5000'
                      : msgData.sale_tag_color
                }"
                >{{ msgData.sale_tag_txt || '--' }}</span
              >
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="销售信息" :column="1" :colon="false">
            <a-descriptions-item v-if="msgData.type === 2" label="税费:">免税</a-descriptions-item>
            <a-descriptions-item label="活动销售价:" :class="addDiffClass(['price'])"
              >￥{{ msgData.price?.toFixed(2) || '--' }}</a-descriptions-item
            >
            <a-descriptions-item label="划线价:" :class="addDiffClass(['old_price'])"
              >￥{{ msgData.old_price?.toFixed(2) || '--' }}</a-descriptions-item
            >
            <!-- <a-descriptions-item label="总库存:" :class="addDiffClass(['stock_num'])">{{
            msgData.stock_num || '--'
          }}</a-descriptions-item> -->
            <a-descriptions-item label="商品编码:" :class="addDiffClass(['product_code'])">{{
              msgData.product_code || '--'
            }}</a-descriptions-item>
            <!-- <a-descriptions-item label="同步ERP:" v-if="!useInfo.is_private_platform">
              <span v-if="form.erp_id">{{
                `${form.erp_id == 1 ? '聚水潭' : form.erp_id == 2 ? '旺店通' : '店管家'} ${form.name || '-'}    ${
                  form.erp_shop_name || '-'
                }  ${form.erp_shop_id || '-'}`
              }}</span>
              <span v-else>--</span>
            </a-descriptions-item> -->
            <!-- <a-descriptions-item label="活动文案:">{{ msgData.active_desc || '--' }}</a-descriptions-item> -->
            <a-descriptions-item label="价格设置:" class="sku-item" :class="addDiffClass(['sku_list', 'spec_list'])">
              <div class="table_box" :style="{ height: calcHeight(oldTableData.list, tableData.list, 'sku') + 'px' }">
                <a-table
                  :dataSource="tableData.list"
                  :columns="tableData.columns"
                  :row-class-name="(_record) => (_record._isDifferent ? 'hight-row' : null)"
                  :scroll="{ x: 'max-content' }"
                  bordered
                  :pagination="false"
                >
                  <template #bodyCell="{ column, record, index }">
                    <template v-if="column.isLabel">
                      <div class="min-w60px">
                        <span class="cursor-pointer absolute" style="top: 16px; right: 5px">
                          <LabelTag
                            v-if="setTag(record, column.key)"
                            :label-text="setTag(record, column.key)"
                            :icon="setTag(record, column.key) === '热销'"
                          ></LabelTag>
                        </span>
                        <a-tooltip placement="topLeft">
                          <template #title>{{ record[column.key] }}</template>
                          <div class="text_overflow_row2 mt20px">
                            <span>{{ record[column.key] }}</span>
                          </div>
                        </a-tooltip>
                      </div>
                    </template>
                    <template v-if="column.dataIndex === 'idx'">{{ index + 1 }}</template>
                    <template v-if="column.dataIndex === 'name'">
                      <a-image style="width: 50px; height: 50px" :src="record.image_url" fit="cover" :fallback="error">
                        <template #error>
                          <div class="error">暂无图片</div>
                        </template>
                      </a-image>
                    </template>
                    <template v-if="column.dataIndex === 'spec_key'">
                      {{ record.spec_key }}
                    </template>
                    <template v-if="column.dataIndex === 'price'">
                      {{ record.price?.toFixed(2) }}
                    </template>
                    <template v-if="column.dataIndex === 'old_price'">
                      {{ record.old_price?.toFixed(2) }}
                    </template>
                    <!-- <template v-if="column.dataIndex === 'stock_num'"> {{ record.stock_num }}件 </template> -->
                    <template v-if="column.dataIndex === 'code'"> {{ record.code }} </template>
                    <template v-if="column.dataIndex === 'isSell'">
                      {{ record.on_sale == 1 ? '售卖' : '缺货' }}
                    </template>
                    <template v-if="column.dataIndex === 'isDefaultPrice'">
                      <a-radio :checked="true" v-if="record.is_default_price == 1"></a-radio>
                    </template>
                    <template v-if="column.dataIndex === 'isDefault'">
                      <a-radio :checked="true" v-if="record.is_default == 1"></a-radio>
                    </template>
                    <template v-if="column.dataIndex === 'tax_rate'"> {{ record.tax_rate || 0 }}% </template>
                  </template>
                </a-table>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="规格展现形式:" class="sku-item" :class="addDiffClass(['sku_show_style'])">
              {{ msgData.sku_show_style === 2 ? '大图模式' : '列表模式' }}
            </a-descriptions-item>
          </a-descriptions>

          <!-- 跨境信息 -->
          <!-- <CrossBorderInfo v-if="msgData.type === 2" :msgData="msgData" :addDiffClass="addDiffClass" /> -->

          <a-descriptions class="descriptions" title="商品介绍" :column="1" :colon="false">
            <a-descriptions-item label="主图视频:" :class="addDiffClass(['product_video'])">
              <div class="item flex" v-if="msgData?.product_video">
                <!-- <video :src="msgData?.product_video" controls="controls" class="imgg">
                  您的浏览器不支持 video 标签。
                </video> -->
                <FilePreview :src="msgData?.product_video" style="width: 101px; height: 101px"></FilePreview>
                <div class="item-mark flex-center" v-if="[0, 4, 5].includes(msgData.product_video_status)">
                  <SvgIcon class="w-24px h-24px" icon="video-tips" />
                  <span class="font-size-12px c-#fff mt-16px line-height-17px">{{
                    msgData.product_video_status == 4
                      ? '视频已下线'
                      : msgData.product_video_status == 5
                        ? '视频已禁用'
                        : '视频已失效'
                  }}</span>
                </div>
              </div>
              <div v-else class="h-111px">--</div>
            </a-descriptions-item>
            <a-descriptions-item
              label="主图:"
              :class="addDiffClass(['image_ids'])"
              :style="{ height: calcHeight(msgOldData?.image_ids, msgData?.image_ids, 'mainPic') + 'px' }"
            >
              <div class="image_list" v-if="msgData?.image_ids?.length">
                <a-image-preview-group>
                  <div
                    class="img_box"
                    v-for="(item, i) in msgData.image_ids"
                    :key="item"
                    :class="{
                      hight: item._isDifferent,
                      'border-red border': state.err_image_ids.includes(item.url) && !state.confirm_images[item.url]
                    }"
                  >
                    <a-image class="img" :src="item.url" fit="cover" :preview-src-list="msgData.image_ids"> </a-image>
                    <div class="title text_overflow" v-if="i == 0">商品主图</div>
                    <div
                      class="text-13px label_red"
                      v-if="state.shop_confirm_images[item.url] && state.err_image_ids.includes(item.url)"
                    >
                      商家标记无误
                    </div>
                    <a-checkbox
                      v-model:checked="state.confirm_images[item.url]"
                      @change="confirmImages($event, item.url, msgData.image_ids)"
                      v-if="state.err_image_ids.includes(item.url) && msgOldData.status == 1"
                      class="mb-20px"
                      >确认无误</a-checkbox
                    >
                  </div>
                </a-image-preview-group>
              </div>
              <div v-else>--</div>
            </a-descriptions-item>
            <a-descriptions-item>
              <div class="ml-60px text-13px label_red" v-if="msgData?.image_ids?.length && state.err_image_ids?.length">
                <ExclamationCircleOutlined class="mr-10px" />{{ state.image_tips }}
              </div>
            </a-descriptions-item>

            <a-descriptions-item label="商品详情:" :class="addDiffClass(['detail_image'])">
              <div
                class="image_list content-start"
                v-if="msgData?.detail_image?.length"
                :style="{ height: calcHeight(msgOldData?.detail_image, msgData?.detail_image, 'detailPic') + 'px' }"
              >
                <a-image-preview-group>
                  <div
                    class="img_box"
                    v-for="(item, i) in msgData.detail_image"
                    :key="item"
                    :class="{
                      hight: item._isDifferent,
                      'border-red border': state.err_detail_imgs.includes(item.src) && !state.confirm_images[item.src]
                    }"
                  >
                    <a-image class="img" :src="item.src"></a-image>

                    <div
                      class="text-13px label_red"
                      v-if="state.shop_confirm_images[item.src] && state.err_detail_imgs.includes(item.src)"
                    >
                      商家标记无误
                    </div>
                    <a-checkbox
                      v-model:checked="state.confirm_images[item.src]"
                      @change="confirmImages($event, item.src, msgData.detail_image)"
                      v-if="state.err_detail_imgs.includes(item.src) && msgOldData.status == 1"
                      class="mb-20px"
                      >确认无误</a-checkbox
                    >
                  </div>
                </a-image-preview-group>
              </div>
              <div
                :style="{ height: calcHeight(msgOldData?.detail_image, msgData?.detail_image, 'detailPic') + 'px' }"
                v-else
              >
                --
              </div>
            </a-descriptions-item>
            <a-descriptions-item>
              <div
                class="ml-60px text-13px label_red"
                v-if="msgData?.detail_image?.length && state.err_detail_imgs?.length"
              >
                <ExclamationCircleOutlined class="mr-10px" />{{ state.detail_image_tips }}
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="商品属性:" :class="addDiffClass(['attribute'])">
              <div
                style="display: inline-block; overflow: auto"
                :style="{ height: calcHeight(oldAttrTableData.list, attrTableData.list, 'attribute') + 'px' }"
              >
                <a-table
                  :dataSource="attrTableData.list"
                  :columns="attrTableData.columns"
                  :row-class-name="(_record) => (_record._isDifferent ? 'hight-row' : null)"
                  bordered
                  :pagination="false"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'attr_val'">
                      <template v-if="record.attr_val"> {{ record.attr_val || '--' }} </template>
                    </template>
                  </template>
                </a-table>
              </div>
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="物流信息" :column="1" :colon="false">
            <a-descriptions-item
              label="商品运费:"
              :class="addDiffClass(['freight_type', 'freight', 'deliver_temp_id'])"
            >
              <template v-if="msgData.freight_type == 1">商家承担运费 </template>
              <template v-else-if="msgData.freight_type == 3">{{ msgData.deliver_temp_name || '--' }} </template>
              <template v-else> 固定运费(￥{{ msgData.freight?.toFixed(2) }}元) </template>
            </a-descriptions-item>
            <a-descriptions-item label="寄件地址:" :class="addDiffClass(['send_address_id'])">
              <template v-if="msgData.send_address_id">
                <div v-if="msgData.senderAddressInfo" style="height: 50px">
                  <div>{{ `${msgData.senderAddressInfo.area_name}${msgData.senderAddressInfo.address}` }}</div>
                  <div>{{ `${msgData.senderAddressInfo.name} ${msgData.senderAddressInfo.phone}` }}</div>
                </div>
                <div v-else>--</div>
              </template>
              <template v-else> <div style="height: 50px">--</div></template>
            </a-descriptions-item>
            <a-descriptions-item label="售后地址:" :class="addDiffClass(['shop_address_id'])">
              <template v-if="msgData.shop_address_id">
                <div v-if="msgData.shopAddressInfo" style="height: 50px">
                  <div>{{ `${msgData.shopAddressInfo.area_name}${msgData.shopAddressInfo.address}` }}</div>
                  <div>{{ `${msgData.shopAddressInfo.name} ${msgData.shopAddressInfo.phone}` }}</div>
                  <a-tooltip>
                    <template #title> {{ `${msgData.shopAddressInfo.warehouse_name}` }} </template>
                    <div class="text_overflow max-w-240px">
                      {{ `${msgData.shopAddressInfo.warehouse_name}` || '--' }}
                    </div>
                  </a-tooltip>
                  <a-tooltip>
                    <template #title> {{ `${msgData.shopAddressInfo.warehouse_desc}` }} </template>
                    <div class="text_overflow max-w-240px">
                      {{ `${msgData.shopAddressInfo.warehouse_desc}` || '--' }}
                    </div>
                  </a-tooltip>
                </div>
                <div v-else>--</div>
              </template>
              <template v-else> <div style="height: 50px">--</div></template>
            </a-descriptions-item>
            <!-- <a-descriptions-item label="非配送区域:" :class="addDiffClass(['undeliver_id'])">
          <div class="wrapper">
            <input :id="'exp1'" class="exp" type="checkbox" />
            <div class="text">
              <label class="btn" :for="'exp1'"></label>
              {{ msgData.citys || '--' }}
            </div>
          </div>
        </a-descriptions-item> -->
          </a-descriptions>
          <a-descriptions class="descriptions" title="评论设置" :column="1" :colon="false">
            <a-descriptions-item label="评论组:" :class="addDiffClass(['comment_temp_name'])">
              <div class="flex">
                {{ msgData.comment_temp_name || `--` }}
                <a-button
                  type="link"
                  class="h-0 pt-0"
                  v-if="msgData.comment_temp_name"
                  @click="onShowDialog('comment', msgData)"
                  >查看详情</a-button
                >
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="好评标签:" 好评标签 :class="addDiffClass(['tag_list'])">
              <GoodReviewsLabel
                v-model="msgData.tag_list"
                :style="{ height: calcHeight(msgOldData.tag_list, msgData.tag_list, 'attribute') + 'px' }"
              />
            </a-descriptions-item>
            <a-descriptions-item label="问答组:" :class="addDiffClass(['question_temp_name'])">
              <div class="flex">
                {{ msgData.question_temp_name || `--` }}
                <a-button
                  type="link"
                  v-if="msgData.question_temp_name"
                  class="h-0 pt-0"
                  @click="onShowDialog('qa', msgData)"
                  >查看详情</a-button
                >
              </div>
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="支付设置" :column="1" :colon="false">
            <a-descriptions-item label="限购方式:" :class="addDiffClass(['limit_type'])">
              {{ msgData.limit_type == 1 ? '不限购' : '限购' }}
              <a-tooltip>
                <template #title> 商品自然流量及广告引流订单，均生效限购设置 </template>
                <img :src="requireImg('goods/bangzhu.png')" class="w-12px h-12px cursor-pointer ml-3px" />
              </a-tooltip>
            </a-descriptions-item>
            <a-descriptions-item label="营销文案:" v-if="msgData.limit_type == 2" :class="addDiffClass(['limit_desc'])">
              {{ msgData.limit_desc || `--` }}
              <a-tooltip placement="right">
                <template #title>
                  <img :src="requireImg('goods/goods-sku.png')" />
                </template>
                <img :src="requireImg('goods/bangzhu.png')" class="w-12px h-12px cursor-pointer ml-3px" />
              </a-tooltip>
            </a-descriptions-item>
            <a-descriptions-item label="限购数量:" v-if="msgData.limit_type == 2" :class="compareData">
              每次最多购买{{ msgData.limit_once_max_num || '--' }}件 <span class="mr-10px"></span> 累计最多购买{{
                msgData.limit_total_num || '--'
              }}件 <span class="mr-10px"></span>每次最少购买{{ msgData.limit_once_min_num || '--' }}件
            </a-descriptions-item>
          </a-descriptions>
          <a-descriptions class="descriptions" title="营销方案" :column="1" :colon="false">
            <a-descriptions-item label="销量:" :class="addDiffClass(['sales_type'])">{{
              msgData.sales_initial
            }}</a-descriptions-item>
            <a-descriptions-item label="跑马灯样式:" :class="addDiffClass(['rolling'])">
              <Supernatant :type="msgData.rolling" />
            </a-descriptions-item>
            <a-descriptions-item label="限时活动:" :class="addDiffClass(['limit_active'])">
              <span class="flex">
                <!-- <span v-if="msgData.limit_active == 1" style="margin-right: 20px;">{{ msgData.active_desc }}</span> -->
                <Supernatant :type="msgData.limit_active == 1 ? 8 : 4" />
              </span>
            </a-descriptions-item>
            <!-- <a-descriptions-item
              label="活动文案:"
              v-if="msgData.limit_active != 2"
              :class="addDiffClass(['active_desc'])"
            >
              <span class="flex">
                <span style="margin-right: 20px">{{ msgData.active_desc || '--' }}</span>
              </span>
            </a-descriptions-item> -->
            <a-descriptions-item label="按钮文案:" :class="addDiffClass(['button_text'])">
              {{ msgData.button_text }}
            </a-descriptions-item>
            <a-descriptions-item label="购物保障:" :class="addDiffClass(['ensure'])">
              {{ msgData.ensure == 1 ? '显示' : '不显示' }}
            </a-descriptions-item>
            <a-descriptions-item label="添加到店铺首页:" :class="addDiffClass(['is_brand_shop'])">
              {{ msgData.is_brand_shop == 1 ? '显示' : '不显示' }}
            </a-descriptions-item>
            <a-descriptions-item label="店铺入口:" :class="addDiffClass(['is_show_shop'])">
              {{ msgData.is_show_shop == 1 ? '显示' : '不显示' }}
            </a-descriptions-item>
            <a-descriptions-item
              label="售后返现:"
              :class="addDiffClass(['rebate_config'])"
              :style="{
                height: calcHeight(msgOldData.rebate_config.list, msgData.rebate_config.list, 'rebate_config') + 'px'
              }"
            >
              <div class="flex-col">
                {{ msgData.rebate_config ? '开启' : '关闭' }}
                <div v-if="msgData.rebate_config">
                  <div>返现规则：{{ msgData.rebate_config.type == 1 ? '适用所有规格' : '指定金额' }}</div>
                  <div v-if="msgData.rebate_config.type == 2">
                    <div v-for="item in msgData.rebate_config.list" class="ml-70px">
                      <div>实付金额满{{ item.min_price }}元，返现{{ item.money }}元</div>
                    </div>
                  </div>
                  <div v-if="msgData.rebate_config.type == 1">返现{{ msgData.rebate_config.money }}元</div>
                </div>
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="送礼物:" :class="addDiffClass(['send_gift_switch'])" v-if="msgData.type == 1">
              {{ msgData.send_gift_switch == 1 ? '开启' : '关闭' }}
            </a-descriptions-item>
          </a-descriptions>
          <!-- 代码仅隐藏不生效，暂不删除 -->
          <!-- <a-descriptions class="descriptions" title="腾讯广告商品库" :column="1" :colon="false" >
            <a-descriptions-item label="投放设置:" :class="addDiffClass(['is_async_gdt'])">
              <span class="c-#404040">{{ msgData.is_async_gdt ? '同步至微信商品库' : '--' }}</span>
            </a-descriptions-item>

            <a-descriptions-item label="商品库:" :class="addDiffClass(['gdt_library_id'])">
              <span class="flex">
                <span style="margin-right: 20px">{{ msgData.gdt_library_id || '--' }}</span>
              </span>
            </a-descriptions-item>
            <a-descriptions-item label="微信类目:" :class="addDiffClass(['gdt_category_name'])">
              {{ msgData.gdt_category_name }}
            </a-descriptions-item>
            <a-descriptions-item
              label="商品资质:"
              :class="addDiffClass(['gdt_aptitude_img'])"
              :style="{ height: calcHeight(msgData?.gdt_aptitude_img, msgData?.gdt_aptitude_img, 'cardPic') + 'px' }"
            >
              <div class="table_box">
                <div class="image_list" v-if="msgData?.gdt_aptitude_img?.length">
                  <a-image-preview-group>
                    <div class="img_box" v-for="(item, i) in msgData.gdt_aptitude_img" :key="i">
                      <a-image class="img" :src="item.img" />
                      <div class="c-#404040">{{ cardInfo[item.card_type] }}</div>
                    </div>
                  </a-image-preview-group>
                </div>
                <div v-else>--</div>
              </div>
            </a-descriptions-item>
          </a-descriptions> -->
          <a-descriptions
            class="descriptions"
            title="服务保障"
            :column="1"
            :colon="false"
            :class="addDiffClass(['protection', 'send_time'])"
          >
            <a-descriptions-item
              label="购物保障:"
              class="des_lable_hide"
              :class="addDiffClass(['protection', 'send_time'])"
            >
              <!-- <a-checkbox-group :value="[1, 2, 3]" class="guarantees"> -->
              <div class="guarantees">
                <template v-for="v in guaranteeData">
                  <a-checkbox
                    v-if="msgData.protection && msgData.protection.includes(v.id)"
                    :key="v.id"
                    :label="v.id"
                    :checked="true"
                    :disabled="true"
                  >
                    <div class="label">{{ v.label }}</div>
                    <div class="desc">{{ v.value }}</div>
                    <template v-if="v.id == 2">
                      <div class="mt-3px font-size-14px font-600 c-#404040">发货时效</div>
                      <div class="deliver_radio">
                        <a-radio v-if="msgData.send_time == 1" :checked="true" :disabled="true" size="large"
                          >24H发货</a-radio
                        >
                        <a-radio v-else-if="msgData.send_time == 2" :checked="true" size="large">48H</a-radio>
                        <a-radio v-else-if="msgData.send_time == 3" :checked="true" size="large">72H</a-radio>
                        <a-radio v-else-if="msgData.send_time == 4" :checked="true" size="large">12H</a-radio>
                      </div>
                      <div class="desc" v-if="[1, 2, 3, 4].includes(msgData.send_time)">
                        商家已经开通{{
                          sendTimeFilter(msgData.send_time)
                        }}小时发货延必赔服务，承诺在支付成功次日零时起{{
                          sendTimeFilter(msgData.send_time)
                        }}小时内为您发货，如商家未能如约发货，买家可在此笔订单中申请违约赔付；
                      </div>
                      <template v-if="false">
                        <a-checkbox
                          class="mb-6px! mt-20px min-h-20px!"
                          disabled
                          :checked="msgData.year_stop_send === 1 ? true : false"
                        >
                          <span class="c-#404040" :class="addDiffClass(['year_stop_send'])">春节停发</span>
                        </a-checkbox>
                        <div
                          v-if="msgData.year_stop_send === 1"
                          class="c-#404040"
                          :class="addDiffClass(['stop_start_time', 'stop_end_time'])"
                        >
                          {{ dayjs.unix(msgData?.stop_start_time).format('YYYY-MM-DD') }}至{{
                            dayjs.unix(msgData?.stop_end_time).format('YYYY-MM-DD')
                          }}不发货
                        </div>
                      </template>
                    </template>
                  </a-checkbox>
                </template>
              </div>
              <!-- </a-checkbox-group> -->
            </a-descriptions-item>
          </a-descriptions>
          <!-- 下单验证 -->
          <a-descriptions
            class="descriptions"
            title="下单验证"
            :column="1"
            :colon="false"
            v-if="msgData?.shop_auth_phone == 1"
          >
            <a-descriptions-item label="下单验证手机号：">
              {{ msgData?.auth_phone == 1 ? '开启' : '关闭' }}
            </a-descriptions-item>
          </a-descriptions>
          <UserNotice :addDiffClass="addDiffClass" :msg-data="msgData" />
        </div>
      </div>

      <a-descriptions class="descriptions" title="商品日志" :column="1" :colon="false">
        <a-descriptions-item label="操作日志:">
          <div class="table_box">
            <a-table :dataSource="logTableData.list" :columns="logTableData.columns" bordered :pagination="false">
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'created_time'">
                  {{ record.created_time }}
                </template>
                <template v-if="column.dataIndex === 'remark'">
                  <span>{{ record.remark }}</span>
                  <a-image
                    class="log_image m-r-5px"
                    style="width: 41px; height: 41px"
                    v-for="(item, i) in record.images"
                    :src="item"
                    :key="item"
                    fit="contain"
                    :initial-index="i"
                    :preview-src-list="record.images"
                  ></a-image>
                </template>
              </template>
            </a-table>
          </div>
        </a-descriptions-item>
      </a-descriptions>

      <a-modal v-model:open="state.preview" title="扫码预览" :width="230" :footer="null">
        <img style="width: 200px; height: 200px" :src="msgData.qrCode" alt="" />
      </a-modal>
    </a-card>
    <a-card class="model_block bottom pos-relative">
      <div class="follower-wrapper">
        <span>标记为跟审人：</span>
        <a-radio-group
          v-model:value="state.follower"
          @change="followerChange"
          :options="[
            { label: '是', value: 2 },
            { label: '否', value: 1 }
          ]"
        />
      </div>
      <a-button @click="onBack()">返回</a-button>
      <!-- <a-button type="primary" @click="openCodeDialog()" :debounce="500" :disabled="state.previewLoading">
          预览
        </a-button> -->
      <template v-if="msgOldData.status == 1">
        <!-- v-auth:GoodsListDetails="['reviewPass']" -->
        <a-button
          v-auth="['shopGoodsReviewPass']"
          type="primary"
          @click="onShowDialog('approve', {})"
          :disabled="hasFalse"
        >
          审核通过</a-button
        >
        <!-- v-auth:GoodsListDetails="['reviewReject']" -->
        <a-button v-auth="['shopGoodsReviewReject']" type="primary" @click="onShowDialog('audit', { type: 2 })"
          >审核不通过</a-button
        >
      </template>
      <template v-if="msgOldData.on_sale == 1 && msgOldData.status != 1">
        <!-- v-auth:GoodsListDetails="['takeOff']" -->
        <a-button v-auth="['shopGoodsTakeOff']" type="primary" @click="onShowDialog('soldOut', { type: 1 })"
          >违规下架</a-button
        >
      </template>
    </a-card>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
    >
      <DialogSoldOut
        v-if="state.dialog.type === 'soldOut'"
        :item="state.dialog.item"
        @event="onEvent"
        :ids="state.dialog.ids"
        :follower="state.follower"
        :type="state.dialog.item.type"
      ></DialogSoldOut>
      <CommentAndQA
        v-if="['qa', 'comment'].includes(state.dialog.type)"
        :item="state.dialog.item"
        :data="state.dialog"
        @event="onEvent"
      />
    </a-modal>
  </div>
</template>

<script setup name="GoodDetails" lang="tsx">
  import CrossBorderInfo from './components/crossBorderInfo.vue'
  import UserNotice from './components/UserNotice.vue'
  import DialogSoldOut from '@/views/shop/goods/list/components/DialogSoldOut.vue'
  import CommentAndQA from './components/CommentAndQA.vue'
  import LabelTag from './components/LabelTag.vue'
  import { reactive, ref, nextTick, watch, createVNode, onMounted, computed, onBeforeUnmount } from 'vue'
  import Supernatant from './components/Supernatants.vue'
  import datas from './src/datas'
  import skuFun from './src/skuFun'
  import { DataDiff, addClass } from './src/index'
  import {
    setProductInfo,
    examineRequest,
    getProductExamineInfo,
    shopAreaInfo,
    storeProductQrcode,
    examine_unlock,
    examine_lock,
    product_check_image,
    update_examine_audit_admin
  } from './index.api'
  import { authList } from '@/api/common'
  import { useRoute, useRouter } from 'vue-router'
  import moment from 'moment'
  import { usePageDialog, useApp } from '@/hooks'
  const { getColumns, initEditTableData, setType } = skuFun()
  import GoodReviewsLabel from '../list/components/GoodReviewsLabel.vue'
  import { Modal, message, Image } from 'ant-design-vue'
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { cloneDeep, isObject } from 'lodash-es'
  import error from '@/assets/images/error.png'
  import { requireImg } from '@/utils'
  import dayjs from 'dayjs'
  const router = useRouter()
  const route = useRoute()
  const { useInfo } = useApp()
  const { pathName, backQuery, title, setTitle } = usePageDialog()
  console.log(useInfo, 'useInfo')

  // 销量
  const salesVolumeEnum = (type, number) => {
    let text = ''
    switch (type) {
      case 1:
        text = '累计真实销量'
        break
      case 2:
        text = '不展示销量'
        break
      case 3:
        text = `全渠道累计真实销量(${number})`
        break
    }
    return text
  }
  const cardInfo = {
    1: '食品经营许可证',
    2: '图书经营许可证',
    3: '医疗器械许可证'
  }
  let state = reactive({
    loading: false,
    // 预览
    preview: false,
    previewLoading: false,
    dialog: {
      visible: false
    },
    showOldData: false,
    oldQrCode: '',
    newQrCode: '',
    err_image_ids: [],
    err_detail_imgs: [],
    confirm_images: {},
    shop_confirm_images: {},
    image_tips: '', //主图违禁词校验提示
    detail_image_tips: '', //商品详情图违禁词校验提示
    examine_loading: true,
    all_passed_images: [],
    follower: undefined as undefined | number // 标记为跟审人
  })

  // 数据值
  let msgData = ref({})

  // 数据值
  let msgOldData = ref({})
  // erp
  let form = ref({})
  // 判断商品属性是否变化
  const addDiffClass = computed(() => {
    return (field: any) => addClass(field, msgData.value.status)
  })
  // 判断图片是否还有未确认的
  let hasFalse: any = computed(() => {
    if (state.err_image_ids.length && !state.err_detail_imgs.length) {
      return state.err_image_ids.some((value) => state.confirm_images[value] == false)
    } else if (state.err_image_ids.length && state.err_detail_imgs.length) {
      return (
        state.err_image_ids.some((value) => state.confirm_images[value] == false) ||
        state.err_detail_imgs.some((value) => state.confirm_images[value] == false)
      )
    } else if (!state.err_image_ids.length && state.err_detail_imgs.length) {
      return state.err_detail_imgs.some((value) => state.confirm_images[value] == false)
    } else {
      return false
    }
  })

  //限购差异变红
  const compareData = computed(() => {
    if (
      msgData.value.limit_total_num == msgOldData.value.limit_total_num &&
      msgData.value.limit_once_max_num == msgOldData.value.limit_once_max_num &&
      msgData.value.limit_once_min_num == msgOldData.value.limit_once_min_num
    ) {
      return ''
    } else {
      return 'label_red'
    }
  })
  // 导入服务保障
  const { guaranteeData, detableTableColumns } = datas()

  // 价格设置表格
  const tableData = reactive({
    list: [],
    columns: detableTableColumns,
    total: 0
  })
  // 价格设置表格
  const oldTableData = reactive({
    list: [],
    columns: detableTableColumns,
    total: 0
  })
  // 商品属性表格
  const attrTableData = reactive({
    list: [],
    columns: [
      {
        dataIndex: 'name',
        title: '商品属性',
        width: 160
      },
      {
        dataIndex: 'attr_val',
        title: '属性值',
        width: 260
      }
    ],
    total: 0
  })
  // 商品属性表格
  const oldAttrTableData = reactive({
    list: [],
    columns: [
      {
        dataIndex: 'name',
        title: '商品属性',
        width: 160
      },
      {
        dataIndex: 'attr_val',
        title: '属性值',
        width: 260
      }
    ],
    total: 0
  })

  // 操作日志表格
  const logTableData = reactive({
    list: [],
    columns: [
      {
        dataIndex: 'created_time',
        title: '操作时间',
        width: 220
      },
      {
        dataIndex: 'author_name',
        title: '操作人',
        width: 200
      },
      {
        dataIndex: 'remark',
        title: '操作内容'
      }
    ],
    total: 0
  })
  const setTag = (record, keys) => {
    let titleArr = record.title?.split('$&$') || record.title?.split('-')
    let specKeyArr = record.spec_key?.split(',') || record.spec_ids?.split(',')
    let index = titleArr.findIndex((item) => item === record[keys])
    let tag = record.spec_tag[specKeyArr[index]]
    return tag || undefined
  }
  // 发货时间 Filter
  const sendTimeFilter = (key) => {
    const obj = {
      1: 24,
      2: 48,
      4: 12,
      3: 72
    }
    return obj[key] || 'X'
  }

  const validObjectEmpty = (data) => {
    if (isObject(data)) {
      const fieldsArr = Object.keys(data)
      if (fieldsArr?.length) {
        return data
      } else {
        return null
      }
    } else {
      return null
    }
  }
  const calcHeight = (oldlist = [], newlist = [], type) => {
    if (msgOldData.value.on_sale != 0 && msgData.value.status == 1) {
      const rowNum = Math.max(oldlist?.length, newlist?.length)
      switch (type) {
        case 'mainPic':
          if (rowNum < 5) return 120
          if (rowNum < 9) return 120 * 2
          if (rowNum < 10) return 120 * 3
          return 115 * 3
        case 'cardPic':
          if (rowNum < 5) return 140
          if (rowNum < 9) return 140 * 2
          if (rowNum < 10) return 140 * 3
          return 140 * 3
        case 'detailPic':
          const multiplier = Math.ceil(rowNum / 5)
          return 132 * multiplier
        case 'attribute':
          return rowNum < 4 ? 230 : rowNum * 56 + 56
        case 'sku':
          return rowNum < 2 ? 260 : rowNum * 90 + 90
        case 'rebate_config':
          return rowNum < 1 ? 40 : rowNum * 24 + 60
        default:
          return 0 // 或者是一个默认值，如果type不匹配任何已知类型
      }
    } else {
      return {}
    }
  }
  // 获取小店商品详情二维码
  const requestQrcode = async (type, store_id, product_id) => {
    try {
      const { data } = await storeProductQrcode({ store_id, product_id })
      if (type === 'old') {
        state.oldQrCode = data.link
      } else {
        state.newQrCode = data.link
      }
    } catch (error) {
      console.error(error)
    }
  }
  // 获取售后地址详情
  const getShopAddress = async (id: any, type: string) => {
    try {
      const { data } = await shopAreaInfo({ id })
      const target = type === 'old' ? msgOldData.value : msgData.value
      target.shopAddressInfo = data
    } catch (error) {
      const target = type === 'old' ? msgOldData.value : msgData.value
      target.shopAddressInfo = null
      console.error(error)
    }
  }
  //获取寄件地址
  const getSenderAddress = async (id: any, type: string) => {
    try {
      const { data } = await shopAreaInfo({ id })
      const target = type === 'old' ? msgOldData.value : msgData.value
      target.senderAddressInfo = data
    } catch (error) {
      const target = type === 'old' ? msgOldData.value : msgData.value
      target.senderAddressInfo = null
      console.error(error)
    }
  }

  function compareAndMark(list1, list2) {
    function markDifferences(sourceList, compareList) {
      return sourceList.map((item) => {
        const foundItem = compareList.find((compareItem) => {
          const { id, pic, ...restItem } = item
          const { id: compareId, pic: comparepic, ...restCompareItem } = compareItem
          return JSON.stringify(restCompareItem) === JSON.stringify(restItem)
        })
        if (!foundItem) {
          return { ...item, _isDifferent: true }
        }
        return item
      })
    }

    const markedList1 = markDifferences(list1, list2)
    const markedList2 = markDifferences(list2, list1)

    return [markedList1, markedList2]
  }

  //已删除弹窗
  const modalWarning = () => {
    Modal.warning({
      title: '提示',
      content: '该商品已删除！',
      onOk() {
        // router.back() // 返回上一页
        router.push({ name: 'List' }) // 跳转到目标页面
      }
    })
  }
  // 获取商品详情
  const getProductInfo = async () => {
    try {
      state.loading = true
      let [{ data = {} }, { data: examineData }] = await Promise.all([
        setProductInfo({ id: route.query.id }),
        getProductExamineInfo({ id: route.query.id, is_audit: route.query.status == '1' ? 1 : null })
      ])

      DataDiff.init(cloneDeep(data), cloneDeep(examineData || data), 'label_red')
      if (examineData.status == 1) {
        let info = data.audit_num == 0 ? '商品信息首次审核' : '商品信息修改审核'
        let infoColor = data.audit_num == 0 ? '#5E85BB' : '#BC8855'
        const styleObj = {
          color: infoColor,
          fontSize: '18px',
          marginLeft: '5px',
          fontWeight: '400'
        }
        const lineObj = {
          width: '2px',
          height: '16px',
          background: '#D8D8D8',
          display: 'inline-block',
          marginLeft: '5px'
        }
        let content = createVNode('div', { class: 'flex-items-center flex' }, [
          title.value,
          createVNode('span', { style: lineObj }),
          createVNode('a', { style: styleObj, class: 'line-item' }, info)
        ])
        setTitle(content)
      }
      // 设置跟审人默认值
      if (data?.audit_admin_id && useInfo.value?.id === data.audit_admin_id) {
        state.follower = 2
      } else {
        state.follower = 1
      }
      // if (data.status === 1) {
      //   data = validObjectEmpty(examineData) || validObjectEmpty(data) || {}
      // }
      data = validObjectEmpty(data)
      const newData = validObjectEmpty(examineData)
      let _product_video = (data.product_video && JSON.parse(data.product_video)) || {}
      let _examineData_product_video = (examineData.product_video && JSON.parse(examineData.product_video)) || {}
      msgOldData.value = {
        ...data,
        sales_initial: salesVolumeEnum(data.sales_type, data.sales_initial),
        image_ids: (data.image_ids.trim() ? data.image_ids.split(',') : []).map((item) => {
          return {
            url: item
          }
        }),
        detail_image: data.detail_image ? JSON.parse(data.detail_image) : [],
        is_async_gdt: data.is_async_gdt == 1 ? true : false,
        gdt_aptitude_img: data.gdt_aptitude_img ? JSON.parse(data.gdt_aptitude_img) : [],
        product_video: _product_video.old_url || '',
        product_video_id: data.product_video_id || null,
        // rolling: data.rolling

        certificate_img_list: data.certificate_img_list
          ? JSON.parse(data.certificate_img_list)?.map((it: any) => {
              it.expire_at = it.expire_at ? dayjs(it.expire_at).format('YYYY-MM-DD') : ''
              return it
            })
          : [],
        shop_certificate_product_img_list: data.shop_certificate_product_img_list
          ? JSON.parse(data.shop_certificate_product_img_list)
          : [],
        rebate_config: data.rebate_config ? JSON.parse(data.rebate_config) : '',
        auth_phone: data?.auth_phone
      }
      msgData.value = {
        ...newData,
        auth_phone: newData?.auth_phone,
        sales_initial: salesVolumeEnum(newData.sales_type, newData.sales_initial),
        image_ids: (newData.image_ids.trim() ? newData.image_ids.split(',') : []).map((item) => {
          return {
            url: item
          }
        }),
        detail_image: newData.detail_image ? JSON.parse(newData.detail_image) : [],
        is_async_gdt: newData.is_async_gdt == 1 ? true : false,
        gdt_aptitude_img: newData.gdt_aptitude_img ? JSON.parse(newData.gdt_aptitude_img) : [],
        product_video: _examineData_product_video.old_url || '',
        product_video_id: newData.product_video_id || null,
        // rolling: data.rolling

        certificate_img_list: newData.certificate_img_list
          ? JSON.parse(newData.certificate_img_list)?.map((it: any) => {
              it.expire_at = it.expire_at ? dayjs(it.expire_at).format('YYYY-MM-DD') : ''
              return it
            })
          : [],
        shop_certificate_product_img_list: newData.shop_certificate_product_img_list
          ? JSON.parse(newData.shop_certificate_product_img_list)
          : [],
        rebate_config: newData.rebate_config ? JSON.parse(newData.rebate_config) : ''
      }
      // 处理商户端确认无误回显
      if (newData.image_ignores) {
        const { shop_confirm_images, passed_images } = newData.image_ignores.reduce(
          (acc: any, item: any) => {
            // 生成商户勾选状态映射对象
            acc.shop_confirm_images[item.images] = item.shop_status === 1
            // 收集两端通过的图片
            if (item.shop_status === 1 && item.boss_status === 1) {
              acc.passed_images.push(item.images)
            }
            return acc
          },
          { shop_confirm_images: {}, passed_images: [] }
        )
        state.shop_confirm_images = shop_confirm_images
        state.all_passed_images = passed_images
      }
      // 图片违禁词验证
      let detail_image = JSON.parse(newData.detail_image).map((item) => {
        return item.src
      })
      //
      let res = await product_check_image({
        images: newData.image_ids.split(','),
        scene: 2
      })
      let detailData = await product_check_image({
        images: detail_image,
        scene: 2
      })
      getViolationReasons(res.data.result)
      getDetailViolationReasons(detailData.data.result)
      // 操作日志
      logTableData.list = (data.records_list || []).map((v) => {
        return {
          ...v,
          created_time: moment(v.created_time * 1000).format('YYYY-MM-DD HH:mm:ss'),
          images: (v.images || []).map((v) => v.image)
        }
      })

      // 处理商品规格值

      if (data.sku_list || data.attribute) {
        let arr = (data.spec_list || []).map((item, index) => {
          item.key = 'sp' + (Number(index) + 1)
          return item
        })

        setType(msgOldData.value.type)
        oldTableData.list =
          initEditTableData(data.sku_list, arr)?.map((item) => {
            return {
              ...item,
              rookie_bond: `${item.rookie_account_name}-${item.bonded_product_name}-${item.warehouse_name}` || '--'
            }
          }) || []
        oldAttrTableData.list = data.attribute || []

        oldTableData.columns = getColumns(arr)
          .filter((v) => !['handle'].includes(v.dataIndex))
          .map((v) => {
            v.width = null
            // if (v.dataIndex == 'price') {
            //   v.label = '销售价/划线价'
            // }
            return v
          })
        oldTableData.columns.unshift({
          width: 70,
          dataIndex: 'idx',
          title: '序号',
          algin: 'left',
          fixed: 'left'
        })
      }
      if (examineData.sku_list || examineData.attribute) {
        // 处理商品规格值
        let arr = (examineData.spec_list || []).map((item, index) => {
          item.key = 'sp' + (Number(index) + 1)
          return item
        })

        setType(msgData.value.type)
        tableData.list =
          initEditTableData(examineData.sku_list, arr)?.map((item) => {
            return {
              ...item,
              rookie_bond: `${item.rookie_account_name}-${item.bonded_product_name}-${item.warehouse_name}` || '--'
            }
          }) || []
        attrTableData.list = examineData.attribute || []

        tableData.columns = getColumns(arr)
          .filter((v) => !['handle'].includes(v.dataIndex))
          .map((v) => {
            v.width = null
            // if (v.dataIndex == 'price') {
            //   v.label = '销售价/划线价'
            // }
            return v
          })
        tableData.columns.unshift({
          width: 70,
          dataIndex: 'idx',
          title: '序号',
          algin: 'left',
          fixed: 'left'
        })
      }

      function updateListsUsingCompareAndMark(dataPairs) {
        dataPairs.forEach(([oldData, newData, key]) => {
          const [markedOldList, markedNewList] = compareAndMark(oldData[key] || [], newData[key] || [])
          oldData[key] = markedOldList
          newData[key] = markedNewList
        })
      }

      // 定义需要比较和更新的数据对
      const dataPairs = [
        [oldTableData, tableData, 'list'],
        [oldAttrTableData, attrTableData, 'list'],
        [msgOldData.value, msgData.value, 'image_ids'],
        [msgOldData.value, msgData.value, 'detail_image'],
        [msgOldData.value, msgData.value, 'tag_list']
      ]

      // 使用封装的函数进行更新
      updateListsUsingCompareAndMark(dataPairs)
      // 价格设置表格
      // tableData.list = (data.sku_list || []).map((item, index) => {
      //   const names = item.title.split('-')
      //   item.rowKey = item.title.split('-').join(',')
      //   for (let i in names) {
      //     item['sp' + (Number(i) + 1)] = names[i]
      //   }
      //   item.pic = item.image_id && [{ id: item.image_id, url: item.image_url }]
      //   return item
      // })

      // // 获取非配送区域城市列表
      // if (!data.undeliver_id) return
      // setDeliverInfo({ id: data.undeliver_id }).then(({ data = {} }) => {
      //   msgData.value.citys = (data || {}).areas
      // })
      console.log(data, 'datatatatata')

      // 获取售后地址详情
      if (data?.shop_address_id) {
        getShopAddress(data.shop_address_id, 'old')
      }
      if (examineData?.shop_address_id) {
        getShopAddress(examineData.shop_address_id, 'new')
      }
      //寄件地址
      if (data?.send_address_id) {
        getSenderAddress(data.send_address_id, 'old')
      }
      if (examineData?.send_address_id) {
        getSenderAddress(examineData.send_address_id, 'new')
      }
    } catch (error) {
      // modalWarning()

      console.error(error)
      state.follower = 1
      setTimeout(() => {
        router.push({ name: 'List' }) // 跳转到目标页面
      }, 600)
    } finally {
      state.loading = false
    }
  }
  //主图违禁词处理
  const getViolationReasons = (data: any) => {
    if (!Array.isArray(data)) {
      return ''
    }
    const invalidItems = data.filter((item) => !item.valid)

    state.err_image_ids = invalidItems.map((item) => item.url)

    const images = data.reduce((obj, item) => {
      obj[item.url] = false
      return obj
    }, {})

    state.confirm_images = { ...images, ...state.confirm_images }
    let image_reason: any = invalidItems.map((item) => item.image_reason).filter((reason) => reason)
    let text_reason: any = invalidItems.map((item) => item.text_reason).filter((reason) => reason)

    image_reason = image_reason.length > 0 ? image_reason.join('、') : ''
    text_reason = text_reason.length > 0 ? text_reason.join('、') : ''
    let tips
    if (image_reason && text_reason) {
      tips = `图片涉及到”${image_reason}“相关，图片中”${text_reason}“涉及到违禁词”`
    } else if (image_reason) {
      tips = `图片涉及到”${image_reason}“相关”`
    } else if (text_reason) {
      tips = `图片中”${text_reason}“涉及到违禁词`
    }
    state.image_tips = tips
  }
  // 商品详情图违禁词处理
  const getDetailViolationReasons = (data: any) => {
    console.log(data, '-------------datadata')

    if (!Array.isArray(data)) {
      return ''
    }
    const invalidItems = data.filter((item) => !item.valid)

    state.err_detail_imgs = invalidItems.map((item) => item.url)

    const images = data.reduce((obj, item) => {
      obj[item.url] = false
      return obj
    }, {})

    state.confirm_images = { ...images, ...state.confirm_images }
    let image_reason: any = invalidItems.map((item) => item.image_reason).filter((reason) => reason)
    let text_reason: any = invalidItems.map((item) => item.text_reason).filter((reason) => reason)

    image_reason = image_reason.length > 0 ? image_reason.join('、') : ''
    text_reason = text_reason.length > 0 ? text_reason.join('、') : ''
    let tips
    if (image_reason && text_reason) {
      tips = `图片涉及到”${image_reason}“相关，图片中”${text_reason}“涉及到违禁词”`
    } else if (image_reason) {
      tips = `图片涉及到”${image_reason}“相关”`
    } else if (text_reason) {
      tips = `图片中”${text_reason}“涉及到违禁词`
    }
    state.detail_image_tips = tips
  }
  // 含违禁词图片点击勾选框
  const confirmImages = (event: any, element: string, data) => {
    data.forEach((item) => {
      let url = item.url || item.src
      if (element == url) {
        state.confirm_images[url] = event.target.checked // 替换图片确认勾选的值
      }
    })
  }
  const getAuthList = async () => {
    try {
      let params = {
        page: 1,
        page_size: 1000,
        id: msgData.value.erp_id
      }
      if (useInfo.value.is_private_platform) return
      let res = await authList(params)
      let _list = cloneDeep(res.data?.list) || []
      const _default = _list.filter((item) => item.id == msgData.value.erp_id)
      if (_default.length > 0) {
        form.value = {
          ..._default[0]
        }
      } else {
        form.value = {}
      }
    } catch (error) {}
  }

  // 返回
  const onBack = async () => {
    try {
      // 解开审核标注
      // let res = await examine_unlock({ id: route.query.id }) // 2025年06月03日16:06:48 后端让取消调用
      router.push({ name: pathName.value, query: backQuery.value })
    } catch (error) {
      console.log(error)
    }
  }
  // watch(
  //   () => route.query.id,
  //   async (newVal) => {
  //     if (newVal) {
  //       await getProductInfo()
  //       await getAuthList()
  //     }
  //   },
  //   {
  //     immediate: true
  //   }
  // )
  // watch(
  //   () => hasFalse.value,
  //   async (newVal) => {
  //     if (newVal) {
  //       state.examine_loading = false
  //     }
  //   },
  //   {
  //     immediate: true
  //   }
  // )
  const timer = ref<any>(null)
  // 进入商品详情后每5分钟请求一次审核加锁
  const lockFetch = async () => {
    try {
      let res = await examine_lock({ id: route.query.id })
      console.log('examine_lock++++++++++')
    } catch (error) {}
  }
  const lockHandle = () => {
    lockFetch()
    timer.value = setInterval(lockFetch, 5 * 60 * 1000) // 每5分钟请求一次接口
  }
  onMounted(async () => {
    await getProductInfo()
    await getAuthList()
    if (msgData.value.status == 1) {
      // lockHandle() // 2025年06月03日16:06:48 后端让取消加锁
    }
  })
  onBeforeUnmount(() => {
    clearInterval(timer.value)
  })
  // 打开二维码弹框
  const openCodeDialog = async () => {
    try {
      state.previewLoading = true
      let { data = {} } = await miniGoodsCode({ id: msgData.value.id })
      msgData.value.qrCode = data.image
      await nextTick()
      state.preview = true
    } catch (error) {
      console.error(error)
    } finally {
      state.previewLoading = false
    }
  }
  const imagesHandle = () => {
    if (msgData.value.image_ignores) {
      msgData.value.image_ignores.forEach((e: any) => {
        // 当前可能更改的状态
        let newStatus = state.confirm_images[e.images] ? 1 : 0
        if (state.err_image_ids.includes(e.images) || state.err_detail_imgs.includes(e.images)) {
          //更改违禁词校验不通过的状态
          e.boss_status = newStatus
          e.shop_status = newStatus
        }
      })
    }
  }

  // 打开弹框
  const onShowDialog = (type, item = {}) => {
    let _item = {
      ...item,
      tip: item.type == 1 ? '请填写违规下架原因' : '请填写审核不通过原因'
    }
    imagesHandle()
    _item.image_ignores = msgData.value.image_ignores
    switch (type) {
      case 'soldOut':
        state.dialog = {
          visible: true,
          title: '违规下架',
          width: 522,
          type: 'soldOut',
          item: _item,
          ids: String(msgData.value.id)
        }
        break
      case 'audit':
        state.dialog = {
          visible: true,
          title: '商品审核',
          width: 522,
          type: 'soldOut',
          item: _item,
          ids: String(msgData.value.id)
        }
        break
      case 'approve':
        // if (msgData.value.type === 4 && msgData.value.wechat_product_status !== 2) {
        //   approved()
        //   return
        // }
        // 点击审核通过  必须不含违禁词/违禁词确认无误
        if (!hasFalse.value) {
          state.dialog.item = _item
          approvedConfirm()
        }

        break
      case 'comment':
        state.dialog = {
          visible: true,
          title: '评论组详情',
          width: '80%',
          type: 'comment',
          item: _item
        }
        break
      case 'qa':
        state.dialog = {
          visible: true,
          title: '问答组详情',
          width: '80%',
          type: 'qa',
          item: _item
        }
        break
    }
  }
  const approved = async () => {
    // 二维码弹窗
    Modal.confirm({
      title: '上架商品',
      closable: true,
      icon: null,
      width: 460,
      cancelButtonProps: { style: { display: 'none' } },
      content: createVNode(
        'div',
        {
          style: {
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center'
          }
        },
        [
          createVNode('p', { style: { color: '#FF4D4F' } }, '请使用达人视频号进行扫码,上架橱窗;操作后请点击上架成功'),
          createVNode(Image, {
            src: msgData.value.wechat_product_qrcode,
            width: '134px',
            height: '134px'
          })
        ]
      ),
      okText: '上架成功',
      onOk() {
        // 用户点击“完成”后，显示审核确认弹窗
        approvedConfirm()
      },
      onCancel() {
        // 用户点击“取消”时，不进行任何操作
        message.success('已取消操作')
      }
    })
  }
  const approvedConfirm = () => {
    // 审核通过
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleOutlined),
      content: '确认进行此操作吗?',
      async onOk() {
        try {
          let res = await examineRequest({
            id: msgData.value.id,
            follower: state.follower,
            result: 2,
            image_ignores:
              state.err_image_ids.length || state.err_detail_imgs.length ? state.dialog.item.image_ignores : undefined
          })
          message.success(res.msg)
          state.selectItem = []
          getProductInfo()
          onBack()
        } catch (error) {
          console.error('审核：', error)
        }
      }
    })
  }
  const onEvent = (data) => {
    if (data.cmd == 'close') {
      state.dialog.visible = false
    } else if (data.cmd == 'editSuccess') {
      getProductInfo()
      onBack()
    }
  }
  const followerChange = async (v) => {
    try {
      console.log('c', v)
      await update_examine_audit_admin({ id: Number(route.query.id), follower: v.target.value })
    } catch (e) {
      console.log(e)
    }
  }
  defineExpose({
    onBack
  })
</script>
<!-- <style lang="less" scoped>
// 元素吸底父级不能设置超出隐藏
:deep(.layout_content_page) {
  overflow: visible !important;
  padding-bottom: 0 !important;
}
</style> -->
<style lang="scss" scoped>
  :deep(.comp_supernatant .help_box .flot) {
    margin-bottom: 0px;
  }

  .error {
    color: #999;
    font-size: 12px;
    text-align: center;
    line-height: 50px;
  }

  .page_goods_details {
    .table_box {
      // overflow: hidden;
      // max-width: 1100px;
      // min-width: 1000px;
      // display: inline-block;

      :deep(.table_content .header_row .col) {
        padding-top: 10px;
        padding-bottom: 10px;
      }
    }

    .image_list {
      display: flex;
      flex-wrap: wrap;

      .img_box {
        width: 100px;
        height: 100px;
        position: relative;
        border-radius: 6px;
        margin-right: 20px;
        margin-bottom: 32px;
        :deep {
          .ant-image {
            width: 96px;
            height: 96px;
            border-radius: 6px;
            background: #f1f5fc;
            .ant-image-img {
              object-fit: contain;
              height: 100%;
            }
          }
        }

        .img {
          width: 100%;
          height: 100%;
        }

        .title {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 30px;
          background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
          padding: 0 15px;
          line-height: 30px;
          font-size: 14px;
          color: #ffffff;
          border-radius: 0px 0px 6px 6px;
        }
      }
      .hight {
        border: 1px dashed red;
        :deep {
          .ant-image {
            .ant-image-img {
              padding: 6px;
            }
          }
        }
      }
    }

    :deep(.el-checkbox-group.guarantees) {
      width: 100%;
      padding: 0px 5px 0;

      .el-checkbox {
        width: 100%;
        align-items: flex-start;
        height: auto;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .desc {
        margin-top: 7px;
        color: #404040;
        font-weight: 400;
        font-size: 12px;
      }

      .label {
        color: #404040;
        line-height: 16px;
        font-weight: 500;
      }
    }
    :deep {
      .ant-checkbox-wrapper {
        width: 100%;
        .ant-checkbox {
          align-self: flex-start;
          margin-top: 2px;
        }
        .desc {
          margin-top: 7px;
          color: #404040;
          font-weight: 400;
          font-size: 12px;
        }

        .label {
          color: #404040;
          line-height: 16px;
          font-weight: 500;
        }
      }
    }

    .deliver_radio {
      padding: 7px 0 1px;
      margin-left: -22px;

      :deep(.el-radio__label) {
        color: var(--kk-des-text-color);
      }

      .desc2 {
        color: var(--kk-des-text-color);
        margin-left: 20px;
      }
    }

    :deep(.descriptions) {
      margin-bottom: 24px;
      .sku-item {
        .ant-descriptions-item-content {
          flex: 1;
          overflow: auto;
          // color: var(--kk-des-text-color);
        }
      }
      .ant-descriptions-item-container {
        display: flex;

        .ant-descriptions-item-label {
          flex-shrink: 0;
          min-width: 86px;
          font-weight: 500;
          // color: var(--kk-des-text-color);
          text-align: right;
        }
        .ant-descriptions-item-content {
          flex: 1;

          // color: var(--kk-des-text-color);
        }
      }

      .des_lable_hide {
        font-size: 0px;
        width: 0px !important;
        margin-right: 0px !important;
      }
    }

    // 折叠面板
    .wrapper {
      width: 510px;
      display: flex;
      overflow: hidden;

      .text {
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: justify;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        position: relative;
      }

      .text::before {
        content: '';
        height: calc(100% - 20px);
        float: right;
      }

      .text::after {
        content: '';
        width: 100%;
        height: 100%;
        position: absolute;
        background-color: #fff;
      }

      .btn {
        float: right;
        clear: both;
        color: #118bce;
        cursor: pointer;
        margin-left: 5px;
        margin-top: -4px;
      }

      .btn::before {
        content: '展开';
      }

      .exp {
        display: none;
      }

      .exp:checked + .text {
        -webkit-line-clamp: 999;
      }

      .exp:checked + .text .btn::before {
        content: '收起';
      }

      .exp:checked + .text::after {
        visibility: hidden;
      }
    }

    // 吸底
    .bottom {
      position: sticky;
      bottom: -10px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 0;
      width: 100%;
      box-sizing: border-box;
      box-shadow: 0px -2px 5px 0px rgba(229, 229, 229, 0.27);
      z-index: 999;
      background-color: #ffffff;
    }
  }

  .log_image {
    width: 41px;
    height: 41px;
    border-radius: 6px;
    overflow: hidden;
    margin-left: 10px;
  }
  :deep {
    .ant-descriptions {
      .label_red {
        .ant-descriptions-item-label {
          color: var(--error-color);
        }
        .ant-descriptions-item-content {
          color: var(--error-color);
        }
      }
    }
  }

  .btn-compare {
    width: 122px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #eaf3ff;
    border-radius: 2px;
    font-size: 14px;
    color: rgba(22, 119, 255, 0.88);
    line-height: 14px;
    cursor: pointer;
  }
  :deep {
    .hight-row {
      color: red;
      border: 1px solid red;
    }
  }
  .item,
  .upload {
    position: relative;
    width: 101px;
    height: 101px;
    background: #f1f5fc;
    border-radius: 4px;
    cursor: pointer;
    overflow: hidden;
  }

  .item {
    // margin-right: 21px;
    .imgg {
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }

    :deep(.el-image) {
      width: 100%;
      height: 100%;
    }

    .name {
      height: 30px;
      background: linear-gradient(0deg, #000000, rgba(0, 0, 0, 0.01));
      border-radius: 0px 0px 4px 4px;
      color: #fff;
      position: absolute;
      bottom: 0;
      width: 100%;
      text-align: center;
      box-sizing: border-box;
      padding: 0 5px;
    }

    .del_img {
      position: absolute;
      top: -10px;
      right: -7px;
      display: none;
    }

    &:hover {
      .del_img {
        display: inline-block;
      }
    }
  }
  .item-mark {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 100;
    flex-direction: column;
    opacity: 0.58;
    left: 1px;
    top: 1px;
  }
  .label_red {
    color: var(--error-color);
  }
  .follower-wrapper {
    position: absolute;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
  }
</style>
