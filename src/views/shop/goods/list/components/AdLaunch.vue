<template>
  <div>
    <SearchBaseLayout
      :data="searchFilter.schemas"
      :actions="searchFilter.formConfig"
      @changeValue="submitForm"
      class="mb-24px"
    />
    <a-table
      :dataSource="tableData.list"
      :columns="columns"
      style="width: 100%"
      :header-cell-style="{ background: '#F3F6FC', color: '#080F1E', fontWeight: 500 }"
      height="364"
      :scroll="{ x: 932, y: 364 }"
      @change="onTableChange"
      :pagination="{
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: tableData.total,
        pageSize: tableData.pageSize,
        current: tableData.pageNo,
        size: 'small',
        showTotal: (total) => `共${tableData.total}条数据`
      }"
    >
      <template #headerCell="{ record, column }">
        <template v-if="column.dataIndex === 'ad_num'">
          <div class="flex_align_center">
            <span>计划ID</span>
            <!-- <a-tooltip popper-class="toolt" placement="bottom" effect="light"> -->
            <a-popover>
              <QuestionCircleOutlined style="font-size: 10px; margin-left: 3px; color: #080f1e" />
              <template #content>
                <div class="shop_ad_popover">
                  <div v-for="item in data.adNumber" :key="item.app_id" class="divider">
                    <div>
                      <a-space flex middle>
                        <div>原始 ID&nbsp;:&nbsp;{{ item.original_id }}</div>
                        <CopyOutlined class="copy_icon" @click="copy(item.original_id)" />
                      </a-space>
                    </div>
                    <div>
                      <a-space flex middle>
                        <div>APP ID&nbsp;:&nbsp;{{ item.app_id }}</div>
                        <CopyOutlined class="copy_icon" @click="copy(item.app_id)" />
                      </a-space>
                    </div>
                  </div>
                </div>
              </template>
            </a-popover>
            <!-- </a-tooltip> -->
          </div>
        </template>
        <template v-if="column.dataIndex === 'conver_num'">
          <div class="flex_align_center">
            <span>转化数</span>
            <a-tooltip popper-class="toolt" placement="top" effect="light">
              <template #title>用户通过广告下单的订单数量</template>
              <QuestionCircleOutlined style="font-size: 10px; color: #080f1e" />
              <!-- <img style="margin-left: 5px;" src="@/assets/icon/yiwen.png" alt=""> -->
            </a-tooltip>
          </div>
        </template>
        <template v-if="column.dataIndex === 'address'">
          <span>回传比例</span>
          <a-tooltip popper-class="toolt" placement="top" effect="light">
            <template #title
              >回流券成交订单和广告成交订单将回传至广告侧，支持设置回传比例（实时回传中可能存在较小的误差）当设置多个券时，券后订单回传比例显示最大值</template
            >
            <!-- <img style="margin-left: 5px;" src="@/assets/icon/yiwen.png" alt=""> -->
            <QuestionCircleOutlined style="font-size: 10px; color: #080f1e" />
          </a-tooltip>
        </template>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'ad_url'">
          <div>
            <a-tooltip>
              <template #title>{{ record.name }}</template>
              <span class="text_overflow block">广告名称：{{ record.name }}</span>
            </a-tooltip>
          </div>
          <div>
            <span class="text_overflow block">计划ID：{{ record.ad_num }}</span>
          </div>
          <div v-if="record.ad_account_id">
            <a-space :size="[6, 0]">
              <span>账户ID:</span>
              <span class="text_overflow">{{ record.ad_account_id || '--' }}</span>
            </a-space>
          </div>
          <div class="flex">
            <div>
              {{ record.wechat_app_name || '--' }}
            </div>
            <div class="flex_column">
              <div>
                <span style="margin-left: 8px">APPID: {{ record.wechat_app_id }}</span>
                <CopyOutlined class="copy_icon" @click="copy(record.wechat_app_id)" />
              </div>
              <div>
                <span style="margin-left: 8px">原始ID: {{ record.original_id }}</span>
                <CopyOutlined class="copy_icon" @click="copy(record.original_id)" />
              </div>
            </div>
          </div>
          <div class="flex_center" v-if="record.mini_page">
            <a-tooltip>
              <template #title>{{ record.mini_page }}</template>
              <span class="text_overflow block"
                >{{ record.platform_id === 5 ? '小程序路径 : ' : '' }} {{ record.mini_page }}</span
              >
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.mini_page)" />
          </div>
          <div class="flex" v-if="record.landing_page_url">
            <a-tooltip>
              <template #title>{{ record.landing_page_url }}</template>
              <span class="text_overflow block max-w-360px">
                落地页地址： <span>{{ record.landing_page_url }}</span>
              </span>
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.landing_page_url)" />
          </div>
          <div class="flex" v-if="record.direct_url">
            <a-tooltip>
              <template #title>{{ record.direct_url }}</template>
              <span class="text_overflow block max-w-360px">
                直达链接： <span>{{ record.direct_url }}</span>
              </span>
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.direct_url)" />
          </div>
          <div class="flex" v-if="record.ad_url">
            <a-tooltip>
              <template #title>{{ record.ad_url }}</template>
              <span class="text_overflow block w-360px">
                {{ [6, 8].includes(record.platform_id) ? 'H5二跳链接: ' : '小程序链接：' }}
                <span>{{ record.ad_url }}</span>
              </span>
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.ad_url)" />
          </div>
          <div class="flex" v-if="record.report_url">
            <a-tooltip>
              <template #title> {{ record.report_url }}</template>
              <span class="text_overflow block"> 报备链接：{{ record.report_url }}</span>
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.report_url)" />
          </div>
          <div class="flex_center" v-if="record.detection_url">
            <a-tooltip>
              <template #title>{{ record.detection_url }}</template>
              <span class="text_overflow block">
                {{ record.platform_id == 8 ? '接口地址：' : '监测链接：' }} {{ record.detection_url }}</span
              >
            </a-tooltip>
            <CopyOutlined class="copy_icon" @click="copy(record.detection_url)" />
          </div>

          <div class="flex_align_center">
            <img
              :src="
                [0, 3, 4, 5].includes(record.jump_type)
                  ? requireImg('goods/icon_app.png')
                  : requireImg('goods/icon_h5.png')
              "
              alt=""
              style="margin-right: 10px"
            />
            <span class="item-tag item-blue">{{ record.wechat_landing_type == 1 ? '图文落地页' : '视频落地页' }}</span>
            <template v-if="record.platform_id == 1">
              <span class="item-tag item-green"> {{ platform_text[record.platform_id] }}</span>
              <span class="item-tag item-green">微信支付</span>
            </template>
            <template v-else-if="record.platform_id === 6">
              <span class="item-tag item-ocean">巨量引擎</span>
              <span class="item-tag item-green">微信支付</span>
            </template>
            <template v-else-if="record.platform_id === 8">
              <span class="item-tag item-hc"> 超级汇川</span>
              <span class="item-tag item-hc">{{ record.jump_type == 3 ? '二跳小程序' : '锦帆建站' }}</span>
            </template>
            <template v-else>
              <span class="item-tag item-cili"> 快手广告</span>
              <span class="item-tag item-cili">{{ record.jump_type == 3 ? '二跳小程序' : '快手磁力建站' }}</span>
            </template>

            <span v-if="record.h5_landing_page_audit" class="item-tag item-green"
              >{{ record.h5_landing_page_audit }} {{ record.h5_landing_page_version }}</span
            >
          </div>
        </template>
        <template v-if="column.dataIndex === 'conver_num'">
          <a-button :underline="false" type="success">{{ record.conver_num }}</a-button>
        </template>
        <template v-if="column.dataIndex === 'callback_ratio'">
          <div v-if="[1].includes(record.platform_id) && (record.shop_limit_callback === 0 || goodsDetail.type === 4)">
            <div>回传{{ !record.shop_limit_callback && goodsDetail.type !== 4 ? '100' : record.callback_ratio }}%</div>
          </div>
          <template v-else>
            <div v-if="!record.callback_type && record.shop_limit_callback">
              <div>回传{{ record.callback_ratio }}%</div>
              <div>整单金额</div>
            </div>
            <div v-else>
              <div v-if="record.callback_type === 1">回传{{ record.callback_ratio }}%</div>
              <div v-else>按金额区间回传</div>
              <div>
                {{
                  {
                    1: '整单金额',
                    2: '单件金额',
                    3: '固定金额',
                    4: '固定比例',
                    5: '金额区间'
                  }[record.callback_amount_type]
                }}回传
              </div>
            </div>
          </template>
        </template>
        <template v-if="column.dataIndex === 'style_type'">
          {{ styleType2Name(record.style_type) || '--' }}
        </template>
        <template v-if="column.dataIndex === 'updated_at'">
          {{ record.updated_at ? formatDate(record.updated_at * 1000) : '' }}
        </template>
        <template v-if="column.dataIndex === 'handle'">
          <div class="handle_btns">
            <div class="flex-col">
              <span @click="onShowDialog('link', record)">广告链接</span>
              <div v-click-outside="() => (record.popViseble = false)">
                <a-popover
                  placement="top"
                  :width="200"
                  v-model:open="record.popViseble"
                  :popper-style="{ padding: '15px', fontSize: '16px' }"
                  trigger="click"
                >
                  <template #content>
                    <div @click.stop="record.popViseble = true">
                      <div class="flex_ju_sp pop_title">
                        <span>落地页预览</span>
                        <img
                          class="close"
                          src="@/assets/icon/close.png"
                          alt=""
                          @click.stop="record.popViseble = false"
                        />
                      </div>
                      <div class="text_align_center">
                        <img
                          :src="data.preview_url"
                          style="width: 160px; height: 160px"
                          alt=""
                          v-if="data.preview_url"
                        />
                        <img
                          src="@/assets/images/empty/no_content.png"
                          style="width: 160px; height: 160px"
                          alt=""
                          v-else
                        />
                        <p>{{ data.preview_url ? '手机微信扫码预览' : '二维码加载中' }}</p>
                      </div>
                    </div>
                  </template>
                  <span @click="onPreview(record)">预览</span>
                </a-popover>
              </div>
            </div>
          </div>
        </template>
      </template>
    </a-table>
  </div>
  <a-modal :title="data.dialog.title" v-model:open="data.dialog.visible" :width="data.dialog.width" :footer="null">
    <AdLink v-if="data.dialog.type == 'link' && data.dialog.visible" :ad_url="data.item.ad_url" @event="onEvent" />
  </a-modal>
</template>
<script setup lang="ts">
  import { watch, reactive } from 'vue'
  import { requireImg, formatDate, copy } from '@/utils'
  import { setAdList, miniCode, getAdNumberApi } from '../index.api'
  import AdLink from './AdLink.vue'
  import datas from './src/datas'
  const emit = defineEmits(['close'])
  import { QuestionCircleOutlined, CopyOutlined } from '@ant-design/icons-vue'

  const columns = reactive([
    { dataIndex: 'ad_url', key: 'ad_url', title: '广告投放链接' },
    // { dataIndex: 'platform_id', key: 'platform_id', title: '投放渠道' },
    // { dataIndex: 'ad_num', key: 'ad_num', title: '计划ID' },
    {
      title: '落地页版本',
      dataIndex: 'style_type',
      key: 'style_type',
      width: 110
    },
    { dataIndex: 'conver_num', key: 'conver_num', title: '转化数', width: 90 },
    { dataIndex: 'callback_ratio', key: 'callback_ratio', title: '回传比例', width: 140 },
    { dataIndex: 'updated_at', key: 'updated_at', title: '更新时间', width: 180 },
    { dataIndex: 'handle', key: 'handle', title: '操作', width: 100 }
  ])
  const styleType2Name = (type) => {
    let obj = {
      1: '大众版',
      2: '老年版',
      3: '淘宝版',
      4: '拼多多版'
    }
    return obj[type]
  }
  // 导入广告平台、回传行为
  const { platformType } = datas()

  const props = defineProps({
    goodsDetail: {
      type: Object,
      default: () => {}
    }
  })

  const tableData = reactive({
    list: [],
    pageNo: 1,
    pageSize: 10,
    total: 1,
    loading: false
  })

  const data = reactive({
    popViseble: false,
    dialog: {
      id: '',
      type: '',
      width: '',
      visible: false,
      titie: ''
    },
    item: {},
    adNumber: [],
    goods_details: {},
    preview_url: null,
    query: {
      jump_type: -1,
      page: tableData.pageNo || 1,
      page_size: tableData.pageSize || 10,
      product_id: props.goodsDetail.id
    } as any
  })
  const searchFilter = reactive({
    schemas: [
      {
        field: 'name',
        type: 'input.text',
        value: '',
        span: 6,
        props: {
          placeholder: '请输入广告名称'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        field: 'exist_coupon_type',
        type: 'select',
        value: undefined,
        label: '优惠券',
        span: 6,
        props: {
          options: [
            {
              label: '新客优惠券',
              value: 1
            },
            {
              label: '回流优惠券',
              value: 2
            },
            {
              label: '支付失败优惠券',
              value: 3
            }
          ],
          mode: 'multiple',
          maxTagCount: 'responsive',
          placeholder: '请选择优惠券'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        field: 'exist_active_type',
        type: 'select',
        value: undefined,
        label: '营销活动',
        span: 6,
        props: {
          options: [
            {
              value: 1,
              label: '顺手买一件'
            },
            {
              value: 2,
              label: '周边推荐'
            }
          ],
          placeholder: '请选择活动类型'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        field: 'exist_kefu_group',
        type: 'select',
        value: undefined,
        label: '客服组加粉',
        span: 6,
        props: {
          options: [
            {
              label: '开启',
              value: 1
            },
            {
              label: '关闭',
              value: -1
            }
          ],
          placeholder: '请选择客服组加粉'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        field: 'style_type',
        type: 'select',
        value: undefined,
        label: '请选择落地页版本',
        span: 6,
        props: {
          options: [
            {
              label: '大众版',
              value: 1
            },
            {
              label: '老年版',
              value: 2
            },
            {
              label: '淘宝版',
              value: 3
            },
            {
              label: '拼多多版',
              value: 4
            }
          ],
          mode: 'multiple',
          maxTagCount: 'responsive',
          showArrow: true,
          placeholder: '请选择落地页版本'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      },
      {
        field: 'wechat_landing_type',
        type: 'select',
        value: undefined,
        label: '请选择落地页类型',
        span: 6,
        props: {
          options: [
            {
              label: '图文落地页',
              value: 1
            },
            {
              label: '视频落地页',
              value: 2
            }
          ],
          placeholder: '请选择落地页类型'
        },
        layout: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 6
        }
      }
    ],
    formConfig: {
      foldNum: 0
    }
  })
  // 搜素和重置
  const submitForm = (v) => {
    if (v.status) {
      data.query = {
        ...data.query,
        ...v.formData
      }
      getAdList()
    } else {
      tableData.pageNo = 1
      data.query = {
        page: 1,
        page_size: tableData.pageSize || 10,
        product_id: props.goodsDetail.id,
        jump_type: -1
      }
      getAdList()
    }
  }
  const onTableChange = (page) => {
    data.query.page = page.current
    data.query.page_size = page.pageSize
    tableData.pageNo = page.current
    tableData.pageSize = page.pageSize
    getAdList()
  }
  // 获取广告计划ID
  async function getAdNumber() {
    try {
      let res = await getAdNumberApi()
      console.log(res)
      data.adNumber = res.data || []
    } catch (error) {
      console.error(error)
    }
  }
  getAdNumber()

  const platform_text = {
    1: '腾讯广告',
    4: '磁力引擎',
    5: 'Bilibili'
  }
  const getAdTitle = (item) => {
    if (item.platform_id == 1) {
      return item.jump_type == 0 ? '小程序链接' : 'H5链接'
    } else {
      return item.jump_type == 4 ? '小程序链接' : '二跳链接'
    }
  }

  // 广告投放列表
  const getAdList = async () => {
    try {
      tableData.loading = true
      const params = new URLSearchParams()
      Object.keys(data.query).forEach((key) => {
        if (data.query[key]) {
          if (key === 'exist_coupon_type' && data.query.exist_coupon_type?.length) {
            console.log('data.query.exist_coupon_type', data.query.exist_coupon_type)
            data.query.exist_coupon_type.forEach((val: any) => {
              params.append('exist_coupon_type', val)
            })
          } else {
            params.append(key, data.query[key])
          }
        }
      })
      const resp = await setAdList(params.toString())
      tableData.list = (resp.data?.list || []).map((v) => {
        return {
          ...v,
          popViseble: false
        }
      })
      tableData.total = resp.data?.total_num || 0
      tableData.pageSize = resp.data?.size || 10
      tableData.pageNo = resp.data?.page || 1
      tableData.loading = false
    } catch (error) {
      // console.log('======>', state.dialog)
      emit('close', false)
      console.error(error)
      tableData.loading = false
    }
  }

  // 预览
  const onPreview = async (item) => {
    item.popViseble = true
    try {
      data.preview_url = null
      const resp = await miniCode({ id: item.id })
      console.log(resp, 'ddd')
      data.preview_url = resp.data.image
    } catch (error) {
      console.error(error)
    }
  }

  function platformFunc(type) {
    const obj = platformType?.find((v) => v.value == type) || {}
    return obj.label || null
  }

  watch(
    () => props.goodsDetail,
    (val, old) => {
      data.goods_details = { ...val }
      data.query.product_id = data.goods_details.id
      getAdList()
    },
    {
      deep: true,
      immediate: true
    }
  )

  const onShowDialog = (type, item) => {
    data.item = { ...item }
    switch (type) {
      case 'link':
        data.dialog = { id: null, title: '广告链接', width: 742, visible: true, type: 'link' }
        break
    }
  }

  const onEvent = (values) => {
    switch (values.cmd) {
      case 'close':
        data.dialog.visible = false
        break
      case 'edit':
        data.dialog.visible = false
        getAdList()
        break
    }
  }
</script>
<style lang="scss" scoped>
  .pop_title {
    margin-bottom: 10px;

    .close {
      cursor: pointer;
    }
  }

  .space_between {
    justify-content: space-between;
  }

  .text_align_center {
    text-align: center;
  }

  .handle_btns {
    user-select: none;

    span {
      color: var(--primary-color);
      cursor: pointer;
      margin-right: 10px;
    }
  }
</style>
<style lang="scss">
  .shop_ad_popover {
    .copy_icon {
      margin-left: 10px;
      cursor: pointer;
      color: var(--primary-color);
    }

    .divider {
      margin-top: 5px;
      padding-bottom: 5px;
      border-bottom: 1px solid #e5e6eb;

      &:nth-last-of-type(1) {
        border-bottom: none;
      }
    }
  }
  .item-blue {
    border: 1px solid #3f9fff;
    color: #3f9fff;
  }
  .item-green {
    border: 1px solid #70b606;
    color: #70b606;
  }
  .item-cili {
    border: 1px solid #fe4a08;
    color: #fe4a08;
  }
  .item-tag {
    border-radius: 5px;
    font-size: 12px;
    padding: 0 6px;
    margin-right: 16px;
  }
  .item-hc {
    border: 1px solid #ff7800;
    color: #ff7800;
  }
  .item-ocean {
    border: 1px solid #2a55e5;
    color: #2a55e5;
  }
</style>
