<template>
  <div class="page_main common_page_warp common_card_wrapper">
    <DesTablePage>
      <template #title>
        <div class="flex flex-justify-between w100%">
          <span>商品列表 </span>
          <div>
            <a-button
              v-if="hasRoute(['AuditStatistics']) && !route.query.checkStatus"
              type="primary"
              @click="
                router.push({
                  name: 'AuditStatistics',
                  query: {
                    type: 1
                  }
                })
              "
              >商品审核统计</a-button
            >
            <a-button type="primary" @click="onExport">导出</a-button>
          </div>
        </div>
      </template>
      <template #tableWarp>
        <SearchBaseLayout
          ref="searchRef"
          :data="schemas"
          :actions="formConfig"
          @changeValue="searchForm"
          :key="state.searchRefresh"
        />
        <ButtonRadioGroup class="btn_list" :data="btnOptions" @changeValue="tabClick" />
        <div class="page_table_toolbar flex justify-between" v-show="![5].includes(state.statusName)">
          <a-button
            v-auth="['shopGoodsBatchOff']"
            :disabled="!state.selectionItem.length"
            @click="onShowDialog('soldOut', state.selectionItem)"
          >
            批量违规下架
          </a-button>
        </div>
        <div class="page_table mt-16px">
          <TableZebraCrossing
            :data="{
              bordered: true,
              loading: tableData.loading,
              rowKey: 'id',
              scroll: {
                scrollToFirstRowOnChange: false,
                x: 1500
              },
              dataSource: tableData.list,
              columns: columns,
              pagination: false
            }"
            @change="pageChange"
            :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
          >
            <template #headerCell="{ scope: { column } }">
              <template v-if="column.dataIndex === 'sales_actual'">
                <a-space>
                  <span
                    @click="
                      salesActualSort({
                        prop: 'sales_actual',
                        order: null
                      })
                    "
                  >
                    累计销量
                  </span>
                  <a-popover
                    trigger="hover"
                    placement="bottom"
                    :overlayInnerStyle="{ boxShadow: 'rgba(0, 0, 0, 0.06) 0px 0px 10px 5px', width: '200px' }"
                  >
                    <span class="ant-table-column-sorter ant-table-column-sorter-full">
                      <span class="ant-table-column-sorter-inner">
                        <span
                          role="presentation"
                          aria-label="caret-up"
                          class="anticon anticon-caret-up ant-table-column-sorter-up"
                        >
                          <svg
                            focusable="false"
                            data-icon="caret-up"
                            width="1em"
                            height="1em"
                            fill="currentColor"
                            aria-hidden="true"
                            viewBox="0 0 1024 1024"
                          >
                            <path
                              d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"
                            ></path>
                          </svg>
                        </span>
                        <span
                          role="presentation"
                          aria-label="caret-down"
                          class="anticon anticon-caret-down ant-table-column-sorter-down"
                        >
                          <svg
                            focusable="false"
                            data-icon="caret-down"
                            width="1em"
                            height="1em"
                            fill="currentColor"
                            aria-hidden="true"
                            viewBox="0 0 1024 1024"
                          >
                            <path
                              d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"
                            ></path>
                          </svg>
                        </span>
                      </span>
                    </span>
                    <template #title>
                      <div class="sales_popover">
                        <div class="title">排序</div>
                        <div class="title_assist">销量仅统计支付订单</div>
                        <div class="border_line"></div>
                        <div class="title_assist">排序方式</div>
                        <a-radio-group
                          v-model:value="state.query.sales_actual_sort_type"
                          @change="
                            salesActualSort({
                              prop: 'sales_actual',
                              order:
                                state.query.saleSort == '1' ? 'ascend' : state.query.saleSort == '2' ? 'descend' : null
                            })
                          "
                        >
                          <a-radio :value="1">按累计销量</a-radio>
                          <a-radio :value="2">按广告引流销量</a-radio>
                          <a-radio :value="3">按自然流量销量</a-radio>
                          <!-- <a-radio :value="4">按回流-未回流销量</a-radio> -->
                          <!-- <a-radio :value="5">按回流-已回传销量</a-radio> -->
                        </a-radio-group>
                        <div class="border_line"></div>
                        <div class="title_assist">排序规则</div>
                        <a-radio-group
                          v-model:value="state.query.saleSort"
                          @change="
                            salesActualSort({
                              prop: 'sales_actual',
                              order:
                                state.query.saleSort == '1' ? 'ascend' : state.query.saleSort == '2' ? 'descend' : null
                            })
                          "
                        >
                          <a-radio value="2">从高到底</a-radio>
                          <a-radio value="1">从低到高</a-radio>
                        </a-radio-group>
                      </div>
                    </template>
                  </a-popover>
                </a-space>
              </template>
              <template v-if="column.dataIndex === 'is_brand_shop'">
                <div>
                  <span>店铺主页展示</span>
                  <a-tooltip>
                    <template #title>仅支持售卖中商品同步，开启后小程序店铺内将展示该商品</template>
                    <QuestionCircleFilled style="color: #939599" class="m-l-8px" />
                  </a-tooltip>
                </div>
              </template>
            </template>
            <template #bodyCell="{ scope: { record, column } }">
              <template v-if="column.dataIndex === 'title'">
                <div class="flex goods_info">
                  <div class="img">
                    <a-image
                      style="width: 60px; height: 60px; border-radius: 6px"
                      :src="record.image"
                      fit="fill"
                      :fallback="errorImg"
                    />
                  </div>
                  <div class="goods_info_data">
                    <a-tooltip popper-class="toolt" placement="topLeft">
                      <template #title>{{ record.title }}</template>
                      <p class="goods_info_data_name text_overflow_row2" @click="goGoodsDetail(record)">
                        {{ record.title }}
                      </p>
                    </a-tooltip>
                    <span class="goods_info_data_number c-#7A869F">ID：{{ record.code }}</span>
                  </div>
                </div>
              </template>
              <template v-if="column.dataIndex === 'product_type'">
                <div class="flex items-center">
                  <img class="w-14px h-14px" :src="imgObj[record.type].img" alt="" />
                  <span class="ml4px"> {{ imgObj[record.type].name }}</span>
                </div>
                <div v-if="record.type == 4">{{ record.wechat_shop_name }}</div>
              </template>
              <template v-if="column.dataIndex === 'category_names'">
                <span>{{ record.category_names || '-' }}</span>
              </template>
              <template v-if="column.dataIndex === 'shop_name'">
                <p class="goods_info_data_name text_overflow_row2" @click="goShopDetails(record, 'shop')">
                  {{ record.shop_name }}
                </p>
                <div class="goods_info_data_name text_overflow_row2" @click="goShopDetails(record, 'company')">
                  {{ record.company_name }}
                </div>
              </template>
              <template v-if="column.dataIndex === 'boss_remark'">
                <div class="flex-y-center flex-wrap">
                  <a-tooltip placement="topLeft">
                    <template #title>{{ record.boss_remark }}</template>
                    <span class="text_overflow_row2">{{ record.boss_remark }}</span>
                  </a-tooltip>
                  <EditOutlined @click="onShowDialog('boss_remark', record)" />
                </div>
              </template>
              <template v-if="column.dataIndex === 'price'">
                <span>{{ record.price.toFixed(2) }}</span>
              </template>
              <template v-if="column.dataIndex === 'protections'">
                <div v-for="(item, index) in record.protections" :key="index">{{ item }}</div>
                <!-- 下架春节停发-暂时注释 -->
                <div v-if="record.year_stop_send === 1 && false" class="number-time">
                  {{ dayjs.unix(record?.stop_start_time).format('YYYY-MM-DD') }}至{{
                    dayjs.unix(record?.stop_end_time).format('YYYY-MM-DD')
                  }}暂停发货
                </div>
              </template>
              <template v-if="column.dataIndex === 'sales_actual'">
                <a-space :size="[8, 0]">
                  <span>{{ record.sales_actual }}</span>
                  <a-popover
                    placement="bottom"
                    trigger="hover"
                    :overlayInnerStyle="{ boxShadow: 'rgba(0, 0, 0, 0.06) 0px 0px 10px 5px', width: '200px' }"
                  >
                    <InfoCircleOutlined class="icons icons_item" @click="record.salesvisible = true" />
                    <template #title>
                      <div style="line-height: 1.8; font-size: 12px">
                        <div>累计销量：{{ record.sales_actual || 0 }}</div>
                        <div>广告引流销量：{{ record.ad_sales || 0 }}</div>
                        <div>自然流量销量：{{ record.natural_sales || 0 }}</div>
                        <div>顺手买一件：{{ record.just_buy_sales || 0 }}</div>
                        <div>周边推荐：{{ record.noun_sales || 0 }}</div>
                        <!-- <div>回流-未回传销量：{{ record.not_callback_sales || 0 }}</div> -->
                        <!-- <div>回流-已回传销量：{{ record.callback_sales || 0 }}</div> -->
                      </div>
                    </template>
                  </a-popover>
                </a-space>
              </template>
              <template v-if="column.dataIndex === 'status'">
                <a-space :size="[6, 0]">
                  <span class="round" :style="{ background: colorType(record.on_sales_txt) }"></span>
                  <span>{{ record.on_sales_txt }}</span>
                  <a-tooltip v-if="record.on_sale === 3 && record.violation_msg" placement="top">
                    <template #title>{{ record.violation_msg }}</template>
                    <InfoCircleOutlined class="warning" />
                  </a-tooltip>
                </a-space>
              </template>
              <template v-if="column.dataIndex === 'status_txt'">
                <span class="flex_align_center" :style="{ color: statusTxt(record) }">
                  <span class="m-r-5px">{{ statusWord(record) }}</span>
                  <span>
                    <a-tooltip v-if="record.status === 1" placement="top">
                      <template #title>{{
                        record.audit_num ? '审核通过后小程序端展示修改后的商品信息' : '审核通过商品将自动上架'
                      }}</template>
                      <InfoCircleOutlined class="warning" />
                    </a-tooltip>
                    <a-tooltip v-if="record.status === 3 && record.fail_msg" placement="top" class="ml-5px">
                      <template #title>{{ record.fail_msg }}</template>
                      <InfoCircleOutlined class="warning" />
                    </a-tooltip>
                    <!-- 违规下架 -->
                    <a-tooltip v-if="record.status === 6 && record.fail_msg" placement="top" class="ml-5px">
                      <template #title>{{ record.fail_msg }}</template>
                      <InfoCircleOutlined class="warning" />
                    </a-tooltip>
                  </span>
                </span>
              </template>
              <template v-if="column.dataIndex === 'sort'">
                <span>{{ record.sort }}</span>
              </template>
              <template v-if="column.dataIndex === 'is_brand_shop'">
                <a-switch
                  v-model:checked="record.is_brand_shop"
                  :checkedValue="1"
                  :disabled="record.on_sale != 1"
                  :unCheckedValue="0"
                  @change="onShopSwitch($event, record)"
                />
              </template>
              <template v-if="column.dataIndex === 'admin_name'">
                <span>{{ record.admin_name || record.creator || '--' }}</span>
              </template>
              <template v-if="column.dataIndex === 'up_examine_time'">
                <span>{{ record.up_examine_time || '--' }}</span>
              </template>
              <template v-if="column.dataIndex === 'shelf_time'">
                <span>{{ record.shelf_time ? formatDate(record.shelf_time * 1000) || '--' : '--' }}</span>
              </template>
              <template v-if="column.dataIndex === 'handle'">
                <a-space :size="[0, 0]" direction="vertical" class="handle_btns">
                  <!-- v-auth:GoodsList="['review']" -->
                  <template v-if="record.status == 1">
                    <span v-auth="['shopGoodsCheck']" @click="goGoodsDetail(record)">审核</span>
                  </template>
                  <!-- v-auth:GoodsList="['takeOff']" -->
                  <template v-if="record.on_sale == 1 && record.status != 1">
                    <span v-auth="['shopGoodsOff']" @click="onShowDialog('soldOut', [record])">违规下架</span>
                  </template>
                  <template v-if="record.on_sale == 3 && record.status != 1">
                    <a-popconfirm title="确定取消违规下架?" ok-text="确定" cancel-text="取消" @confirm="cancel(record)">
                      <span v-auth="['shopGoodsOff']">取消违规下架</span>
                    </a-popconfirm>
                  </template>
                  <!-- v-auth:GoodsList="['adList']" -->
                  <span v-auth="['shopGoodsAdLink']" @click="onShowDialog('ad', record)">广告链接</span>

                  <a-popconfirm
                    title="请确认是否删除当前商品?"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm="deleteGood(record)"
                  >
                    <span v-auth="['deleteShopGood']">删除</span>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </TableZebraCrossing>
          <Pagination
            v-model:page="state.query.page"
            v-model:pageSize="state.query.size"
            :total="tableData.total"
            @change="getProductList"
          ></Pagination>
        </div>
      </template>
    </DesTablePage>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
    >
      <AdLaunch v-if="state.dialog.type === 'ad'" :goodsDetail="state.item" @event="onEvent" @close="handleClose" />
      <DialogSoldOut
        v-if="state.dialog.type === 'soldOut'"
        @event="onEvent"
        :item="state.dialog.item"
        :ids="state.dialog.ids"
      ></DialogSoldOut>
      <BossRemarkModel v-if="state.dialog.type === 'boss_remark'" @onEvent="onEvent" :item="state.dialog.item" />
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
  defineOptions({ name: 'List' })
  import { cloneDeep, isArray } from 'lodash'
  import { useRouter, useRoute } from 'vue-router'
  import datas from './src/datas'
  import { exportCreate } from '@/api/common'
  import DialogSoldOut from './components/DialogSoldOut.vue'
  import BossRemarkModel from './components/BossRemarkModel.vue'
  import Pagination from '@/components/ui/common/PaginationComponent/index.vue'
  import { formatDate, requireImg } from '@/utils'
  import { useDownloadCenter, useLogin } from '@/hooks'
  import { reactive, ref, watch, createVNode, h, nextTick, onMounted } from 'vue'
  import {
    setProductList,
    setCategoryList,
    getProductExamineList,
    productListCount,
    setBrandShopApi,
    batchSoldout,
    bonded_manager_product,
    deleteProduct,
    examine_lock
  } from './index.api'
  import { getShopCascade } from '@/views/shop/order/list/index.api'
  import AdLaunch from './components/AdLaunch.vue'
  import {
    InfoCircleOutlined,
    QuestionCircleFilled,
    ExclamationCircleFilled,
    EditOutlined
  } from '@ant-design/icons-vue'
  import errorImg from '@/assets/images/error.png'
  import { message, Modal } from 'ant-design-vue'
  import dayjs from 'dayjs'
  const router = useRouter()
  const route = useRoute()
  const { hasRoute } = useLogin()
  const { goCenter } = useDownloadCenter()
  const { schemas, formConfig, columns, btnOptions, statusTxt, colorType, statusWord } = datas()
  // const kkTableRef = ref(null)
  const tableData = reactive({
    list: [],
    total: 1,
    loading: false
  })
  const searchRef = ref(null)
  const state = reactive({
    searchRefresh: Date.now(),
    isExamine: 0,
    apiFun: setProductList, // 请求接口
    statusName: 1,
    showPhone: false,
    forzeStatus: true,
    btnActvie: -1,
    item: {},
    query: {
      page: 1,
      size: 10,
      status: undefined,
      up_price: undefined,
      down_price: undefined,
      up_sales: undefined,
      down_sales: undefined,
      sales_actual_sort_type: undefined,
      price_sort: '', // 价格排序
      sales_actual_sort: '', // 累计销量排序
      sort: '' // 排序 排序
    },
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: '',
      ids: []
    } as {
      visible: boolean
      title: string
      width: number
      type: string
      ids?: any[]
      item?: any
    },
    cateGoryList: [],
    selectionItem: [], // 表格选择的Item
    selectedRowKeys: []
  })
  const imgObj: Record<number, { name: string; img: string }> = {
    1: {
      name: '普通商品',
      img: requireImg('goods/normal.png')
    },
    2: {
      name: '跨境商品',
      img: requireImg('goods/cross_border.png')
    },
    4: {
      name: '微信小店',
      img: requireImg('goods/weixinShop.png')
    }
  }
  // 是否店铺主页展示
  const onShopSwitch = async (v: any, row: any) => {
    row.is_brand_shop = v == 1 ? 0 : 1
    let text = v == 1 ? '开启后小程序店铺内将展示该商品' : '关闭后小程序店铺内将不再展示该商品'
    Modal.confirm({
      title: '提示',
      icon: createVNode(ExclamationCircleFilled),
      content: text,
      async onOk() {
        try {
          setBrandShop(row.id)
        } catch (error) {
          console.error(error)
        }
      }
    })
  }

  // 店铺主页展示开关接口
  const setBrandShop = async (id) => {
    try {
      let res = await setBrandShopApi({ product_id: id })
      message.success(res.msg)
      getProductList()
    } catch (error) {}
  }
  /**
   * table表格排序 返回规则
   */
  function tablePropSort(row: { order: any }) {
    let text = ''
    switch (row.order) {
      case 'ascend':
        text = 'asc'
        break
      case 'descend':
        text = 'desc'
        break
      default:
        text = ''
        break
    }
    return text
  }

  const onEvent = (data) => {
    if (data.cmd == 'close') {
      state.dialog.visible = false
    } else if (data.cmd == 'editSuccess') {
      state.selectionItem = []
      state.selectedRowKeys = []
      getProductList()
    } else if (data.cmd === 'submit') {
      state.dialog.visible = false
      getProductList()
    }
  }
  // 广告链接关闭弹窗
  const handleClose = (value) => {
    setTimeout(() => {
      state.dialog.visible = value
      getProductList()
    }, 700)
  }
  // 打开弹框
  const onShowDialog = (type, item) => {
    state.item = { ...item }
    switch (type) {
      case 'ad':
        state.dialog = { visible: true, title: '广告链接', width: 1010, type: 'ad' }
        break
      case 'soldOut':
        state.dialog = {
          visible: true,
          title: '违规下架',
          width: 522,
          type: 'soldOut',
          item: { type: 1, tip: '请填写违规下架原因' },
          ids: item.map((v) => v.id).join(',')
        }
        break
      case 'boss_remark':
        state.dialog = { visible: true, title: '平台备注', width: 530, type: 'boss_remark', item }
        break
    }
  }
  // 删除商品
  const deleteGood = async (row) => {
    try {
      let res = await deleteProduct({ id: row.id })
      message.success(res.msg)
    } catch (error) {
      console.error('error:', error)
    } finally {
      getProductList()
    }
  }
  // 取消违规下架
  const cancel = async (row) => {
    try {
      let res = await batchSoldout({ type: 2, id: row.id, ids: row.id + '' })
      message.success(res.msg)
    } catch (error) {
      console.error('error:', error)
    } finally {
      getProductList() // 确保总是调用
    }
  }

  const initGetShopList = async () => {
    const result = await getShopCascade({ page_size: 1000 })
    if (result.code === 0) {
      if (result?.data?.length) {
        ;(schemas.value || []).forEach((item) => {
          if (item.field === 'shop_id') {
            item.props.options = result.data || []
          }
        })
      }
    }
  }
  initGetShopList()

  watch(
    () => route.fullPath,
    () => {
      nextTick(() => {
        getProductList() // 监听路由变化并刷新列表
      })
    }
  )

  // 商品列表
  const getProductList = async () => {
    try {
      tableData.loading = true
      state.selectedRowKeys = []
      state.selectionItem = []
      // console.log('111111', state.query)

      let query = cloneDeep({
        ...state.query,
        shop_id: state.query.shop_id || 0
      })
      console.log('222222', query)
      // 处理上下架筛选状态
      switch (state.query.hign_low_limit) {
        case 1:
          query = { ...query, on_sale: 1, is_temp: 0 }
          break
        case 2:
          query = { ...query, on_sale: 2, is_temp: 0 }
          break
        case 3:
          query = { ...query, is_temp: 1 }
          break
      }
      state.apiFun = query.status == 1 ? getProductExamineList : setProductList
      console.log('333333', query)
      const resp = await state.apiFun(query)
      tableData.list = (resp.data?.list || []).map((v) => {
        return {
          ...v,
          isEditSort: false,
          salesvisible: false
        }
      })
      tableData.total = resp.data?.total || 0
      tableData.loading = false
      // if (state.forzeStatus) getHeadData(query)
      getHeadData(query)
    } catch (error) {
      console.error(error)
    } finally {
      tableData.loading = false
    }
  }

  watch(
    () => route.query,
    async (newQuery) => {
      await nextTick() // 等待 DOM 更新完成
      if (searchRef.value) {
        if (newQuery.product_type === '4') {
          formConfig.foldNum = 0
          searchRef.value.formData.product_type = 4
          state.query.product_type = '4'
        }

        if (newQuery.checkStatus) {
          searchRef.value.formData.status = newQuery.checkStatus === 'success' ? 2 : 3
          state.query.status = newQuery.checkStatus === 'success' ? 2 : 3
        }
      }

      getProductList()
    },
    { immediate: true, deep: true }
  )
  // 获取头部数据
  const getHeadData = async (query) => {
    try {
      if (btnOptions.value.value == 5) {
        delete query.status
      }

      let res = await productListCount(query)
      console.log(res, 'res')
      const list = res.data?.count || {}
      const keys = {
        1: 'all',
        2: 'on_sale',
        3: 'off_sale',
        4: 'temp',
        5: 'wait_status',
        6: 'boss_off_sale',
        51: 'wait_status_one',
        52: 'wait_status_two'
      }
      btnOptions.value.list.forEach((v) => {
        v.total = 0
        if (v.value) {
          for (let key in list) {
            if (key == (keys as any)[v.value]) v.total = list[key]
          }
        }
      })
    } catch (error) {
      console.log(error)
    }
  }
  // 点击审核，增加标注==》跳转商品详情审核
  const goGoodsDetail = async (record) => {
    try {
      // if (record.status == 1) {
      //   let res = await examine_lock({ id: record.id })
      //   if (res.code == 0) {
      //     router.push({ path: '/shop/goods/goods-detail', query: { id: record.id } })
      //   }
      // } else {
      //   router.push({ path: '/shop/goods/goods-detail', query: { id: record.id } })
      // }
      router.push({ path: '/shop/goods/goods-detail', query: { id: record.id, status: record.status } })
    } catch (error) {}
    //
  }

  const goShopDetails = (record, type) => {
    const href =
      type == 'shop'
        ? router.resolve({
            path: '/shop/customer/shop-detail',
            query: { id: record.shop_id }
          })
        : router.resolve({
            path: '/shop/customer/company-detail',
            query: { id: record.company_id }
          })
    window.open(href.href, '_blank')
  }
  // 获取筛选条件ids
  const getCategorIds = (data: string[]) => {
    if (!isArray(data)) return ''
    return data.join(',')
  }
  // 搜索按钮
  const category_ids = ref([])
  const searchForm = (v) => {
    console.log('--- v ---', v)
    if (!v?.status) {
      schemas.value.forEach((f) => {
        if (f.field === 'status') f.value = undefined
        if (f.field === 'on_sale') f.value = undefined
        f.value = undefined
      })
      state.searchRefresh = Date.now()
      delete state.query.on_sale
      delete v.formData.on_sale
      v.formData.status = undefined
      v.formData.shelf_time = undefined
      state.query.audit_type = null
    }
    let shopids = []
    if (v.formData?.shop_id?.length > 0) {
      v.formData?.shop_id.forEach((item) => {
        if (item.length == 1) {
          shopids = [0]
        } else {
          // if (companyids.indexOf(item[0]) == -1) {
          //   companyids.push(item[0])
          // }
          if (shopids.indexOf(item[1]) == -1) {
            shopids.push(item[1])
          }
        }
      })
    }

    state.forzeStatus = true
    state.query = {
      ...state.query,
      ...v.formData,
      shop_ids: shopids.length ? shopids.join(',') : '',
      category_id: v.formData.category_id ? v.formData.category_id[v.formData.category_id.length - 1] : undefined,
      // category_ids: getCategorIds(v.formData.category_ids)
      bonded_id: (v.formData.bonded_id || []).join(','),
      product_type: v.formData.product_type ? v.formData.product_type + '' : undefined
    }
    delete state.query.shop_id
    if (Array.isArray(v.formData.admin_ids)) {
      state.query.admin_ids = v.formData.admin_ids.join(',')
    }
    category_ids.value = v.formData.category_id ? v.formData.category_id : undefined
    // if (isArray(v.formData.shelf_time)) {
    //   if (moment(v.formData.shelf_time[1]).diff(moment(v.formData.shelf_time[0]), 'days') > 90) {
    //     return message.warning('时间区间最大为90天')
    //   }
    // }
    state.query.shelf_time =
      !v.formData?.shelf_time?.length || v.formData?.shelf_time == null
        ? ''
        : `${v.formData.shelf_time[0]}_${v.formData.shelf_time[1]}`
    if (isArray(v.formData.price)) {
      state.query.up_price = v.formData.price[0]
      state.query.down_price = v.formData.price[1]
    } else {
      delete state.query.up_price
      delete state.query.down_price
    }
    if (isArray(v.formData.sales_actual)) {
      state.query.up_sales = v.formData.sales_actual[0]
      state.query.down_sales = v.formData.sales_actual[1]
      state.query.sales_actual = []
    } else {
      delete state.query.up_sales
      delete state.query.down_sales
    }
    console.log('------', state.query)

    if ((v.formData.status != 1 && v.formData.on_sale != 3) || (v.formData.status === 1 && v.formData.on_sale === 3)) {
      btnOptions.value.value = 1
    }
    if (!v.formData.status && v.formData.on_sale === 3) {
      btnOptions.value.value = 6
    } else {
      btnOptions.value.value = 1
    }
    if (v.formData.protection) {
      state.query.protection = v.formData.protection.join(',')
    }
    console.log('++++++', state.query)
    getProductList()
  }
  // 排序改变
  function sortChange(row) {
    switch (row.prop) {
      case 'price':
        state.query.price_sort = tablePropSort(row)
        state.query.sort = null
        break
      case 'sort':
        state.query.sort = tablePropSort(row)
        state.query.price_sort = null
        break
    }
    state.query.sales_actual_sort_type = null
    state.query.saleSort = 0
    state.query.sales_actual_sort = null
    state.query.page = 1
    getProductList()
    return row
  }
  // 累计销量排序
  function salesActualSort(row) {
    // kkTableRef.value.clearSort()
    if (!row.order) state.query.saleSort = 0
    state.query.sales_actual_sort = tablePropSort(row)
    state.query.price_sort = null
    state.query.sort = null
    getProductList()
  }
  // 获取商品类目列表
  const getCategorlList = async () => {
    try {
      let [res, result] = await Promise.all([setCategoryList(), bonded_manager_product()])

      schemas.value.forEach((v) => {
        if (v.field == 'category_id') {
          v.props.options = res.data
        } else if (v.field == 'bonded_id') {
          v.props.options =
            result.data?.list?.map((it: any) => {
              return {
                ...it,
                label: it.account_name,
                value: it.id,
                children: it.bonded_product?.map((item: any) => {
                  return (
                    {
                      ...item,
                      label: item.name,
                      value: item.id,
                      children:
                        item.bonded_manage?.map((k: any) => {
                          return {
                            ...k,
                            label: k.warehouse_name,
                            value: k.id
                          }
                        }) || []
                    } || []
                  )
                })
              }
            }) || []
        }
      })
    } catch (error) {}
  }
  getCategorlList()
  // getShopList(schemas)

  // tab切换
  const tabClick = (data: { value: any }) => {
    console.log('sww223', data, schemas.value)
    btnOptions.value.value = Number(data.value)
    state.forzeStatus = false
    switch (data.value) {
      // 全部
      case 1:
        state.isExamine = 0
        // const obj = schemas.value.find((v) => v.field === 'status') || {}
        // if (obj.value) state.query.status = obj.value
        // else delete state.query.status
        state.query.on_sale = 0
        schemas.value.forEach((f) => {
          f.value = f.field == 'category_id' ? category_ids : state.query[f.field]
          if (f.field === 'status') f.value = undefined
          if (f.field === 'on_sale') f.value = undefined
          // if (f.field === 'shelf_time_arr' && state.shelf_time_arr?.length > 0) f.value = state.shelf_time_arr
        })
        delete state.query.audit_type
        delete state.query.status
        // state.apiFun = setProductList
        // if (state.query.status >= 0) delete state.query.status
        break
      case 51:
      case 52:
        // 商品首次审核
        // 商品修改审核
        state.query.status = 1
        state.query.on_sale = 0
        state.query.audit_type = (data.value + '').replace(/5/g, '') * 1 // audit_type取1或2
        state.isExamine = 1
        schemas.value.forEach((f) => {
          f.value = f.field == 'category_id' ? category_ids : state.query[f.field]
          if (f.field === 'status') f.value = 1
          if (f.field === 'on_sale') f.value = undefined
          // if (f.field === 'shelf_time_arr' && state.shelf_time_arr?.length > 0) f.value = state.shelf_time_arr
        })
        console.log('================================================================', schemas.value)
        break
      case 6:
        state.query.on_sale = 0
        state.isExamine = 0
        schemas.value.forEach((f) => {
          f.value = f.field == 'category_id' ? category_ids : state.query[f.field]
          if (f.field === 'on_sale') f.value = 3
          if (f.field === 'status') f.value = undefined
          // if (f.field === 'shelf_time_arr' && state.shelf_time_arr?.length > 0) f.value = state.shelf_time_arr
        })
        state.query.on_sale = 3
        delete state.query.audit_type
        delete state.query.status
        break
    }
    // state.searchRefresh = Date.now()
    state.query.page = 1
    getProductList()
    state.selectedRowKeys = []
    state.selectionItem = []
  }

  const pageChange = (pagination: any, _filters: any, sorter: any) => {
    console.log('pagination', pagination)
    console.log('sorter', sorter)

    // state.query.page = pagination.current
    // state.query.size = pagination.pageSize
    // state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    if (sorter && Object.keys(sorter).length) {
      switch (sorter.field) {
        case 'price':
          state.query.price_sort = tablePropSort(sorter)
          state.query.sort = ''
          break
        case 'sort':
          state.query.sort = tablePropSort(sorter)
          state.query.price_sort = ''
          break
      }
      state.query.sales_actual_sort_type = null
      state.query.saleSort = ''
      state.query.sales_actual_sort = ''
      state.query.page = 1
      // state.tableConfigOptions.pagination.current = 1
    }
    getProductList()
  }

  const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
    state.selectedRowKeys = selectedRowKeys
    state.selectionItem = selectedRows
  }
  // 导出
  const onExport = async () => {
    try {
      let params = {
        ...state.query,
        is_examine: state.isExamine
      }
      await exportCreate({
        type: 'product_list',
        params: JSON.stringify(params)
      })
      goCenter('download', 'download')
    } catch (error) {
      console.log(error)
    }
  }
</script>
<style lang="scss" scoped>
  .goods_info {
    img,
    .img_item {
      width: 60px;
      height: 60px;
      background: #bec6d6;
      border-radius: 6px;
    }

    .goods_info_data {
      flex: 1;
    }

    p {
      margin: 0;
    }

    &_data {
      margin-left: 10px;
      font-family: PingFang SC;
      font-weight: 400;

      &_name {
        overflow: hidden; //多出的隐藏
        text-overflow: ellipsis; //多出部分用...代替
        display: -webkit-box; //定义为盒子模型显示
        -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
        -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
        font-size: 14px;
        color: var(--primary-color);
        cursor: pointer;
      }

      &_number {
        font-size: 12px;
        color: #999999;
      }
    }
  }

  // 按钮
  .icons {
    &_item {
      cursor: pointer;
      &:hover {
        color: var(--primary-color);
      }
    }
  }
  .icons_none {
    display: none;
  }
  :deep {
    .page_table_toolbar {
      margin-top: 16px;
    }
    .page_table .ant-table-wrapper .ant-table-row:hover .ant-table-cell {
      .icons_none {
        display: block;
      }
    }
  }

  // 累计销量排序弹框
  .sales_popover {
    .title {
      font-size: 14px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #080f1e;
      margin-bottom: 8px;
    }
    .title_assist {
      font-size: 12px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #404040;
    }
    .border_line {
      width: 100%;
      height: 1px;
      background-color: #eef0f3;
      margin: 10px 0;
    }
    :deep {
      .ant-radio-group {
        //
      }
      .ant-radio-wrapper {
        width: 100%;
        height: 14px;
        margin-top: 15px;
        display: flex;
        align-items: center;
        span {
          font-size: 12px;
        }
      }
    }
  }
  .round {
    width: 8px;
    height: 8px;
    background: #404040;
    border-radius: 50%;
    display: inline-block;
  }

  .handle_btns {
    user-select: none;

    .icons {
      margin-bottom: 10px;
    }

    span {
      color: var(--primary-color);
      cursor: pointer;
    }
  }
  .number-time {
    color: rgba(36, 47, 87, 0.6) !important;
    font-size: var(--font-size-mini) !important;
  }
</style>
