<template>
  <div>
    <div class="model_block">
      <a-breadcrumb separator="/">
        <a-breadcrumb-item>
          <router-link :to="{ name: 'FreightTemp' }">运费模版</router-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>详情</a-breadcrumb-item>
      </a-breadcrumb>
    </div>
    <div class="model_block bor_6" style="padding: 20px 30px 5px 30px">
      <a-descriptions class="descriptions" title="基本信息" :column="1">
        <a-descriptions-item label="模版名称">{{ state.addForm.temp_name }}</a-descriptions-item>
        <a-descriptions-item label="模版描述">{{ state.addForm.desc }}</a-descriptions-item>
        <a-descriptions-item label="默认运费">{{ state.addForm.freight_desc }}</a-descriptions-item>
        <a-descriptions-item label="区域邮费">
          <!-- <AreaList :list="state.addForm.freight_temp" :isHandle="false" /> -->
          <a-table
            :dataSource="state.areaList"
            bordered
            emptyText="空空如也 没有内容"
            :pagination="false"
            style="width: 100%"
          >
            <a-table-column data-index="areas" title="配送区域"></a-table-column>
            <a-table-column data-index="freight_desc" title="区域运费"></a-table-column>
          </a-table>
        </a-descriptions-item>
        <a-descriptions-item label="不配送区域">
          <a-table
            :dataSource="state.noAreaList"
            bordered
            emptyText="空空如也 没有内容"
            :pagination="false"
            style="width: 100%"
          >
            <a-table-column data-index="areas" title="不配送区域"></a-table-column>
          </a-table>
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </div>
</template>

<script setup>
  import { onActivated, onMounted, reactive, ref } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  // import AreaList from './components/AreaList.vue'
  // import NoAreaList from './components/NoAreaList.vue'
  import { setDeliverInfo } from './index.api'

  const router = useRouter()
  const route = useRoute()

  const state = reactive({
    loading: false,
    addForm: {
      freight_type: 1,
      freight_temp: []
    },
    noAreaList: [],
    areaList: []
  })

  const temp_id = route.query.id

  onMounted(() => {
    if (temp_id) getDeliverInfo(temp_id)
  })

  async function getDeliverInfo(id) {
    try {
      const resp = await setDeliverInfo({ id })
      const result = resp.data || {}

      if (result.freight_type == 2) {
        const freight = result.freight ? result.freight / 100 : 0
        const add_freight = result.add_freight ? result.add_freight / 100 : 0
        result.freight_desc = `${result.num}件内${freight}元，每增加${result.add_num}件，增加运费${add_freight}元。`
      } else {
        result.freight_desc = ' ¥' + (result.default_freight ? result.default_freight / 100 : 0)
      }
      result.freight_temp = result.freight_temp || []
      result.freight_temp.forEach((v) => {
        v.default_freight = (v.default_freight || 0) / 100
        v.freight = (v.freight || 0) / 100
        v.add_freight = (v.add_freight || 0) / 100
        v.freight_desc = v.freight_desc + (v.freight_type == 1 ? '元' : '')
      })
      result.areas = result.un_deliver_info.areas
      state.addForm = result || {}
      state.noAreaList = result.areas ? [{ id: 1, areas: result.areas }] : []
      state.areaList = result.freight_temp || []
    } catch (error) {}
  }
</script>

<style lang="scss" scoped>
  .model_block {
    background-color: #fff;
    padding: 24px;
    margin-bottom: 10px;
    border-radius: var(--border-radius-huge);
    .title {
      line-height: 16px;
      font-size: 16px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #080f1e;
      margin: 20px 0;
    }
  }
  :deep(.ant-descriptions-item-label) {
    width: 85px;
  }
</style>
