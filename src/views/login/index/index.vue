<template>
  <div class="page_login">
    <div class="warpper">
      <div class="left">
        <div class="middle">
          <div class="logo_box">
            <div class="title">
              <img class="login_img" src="@/assets/images/login-hyd/logohyd.png" alt="" />
            </div>
            <a-form label-width="0" ref="ruleForm" class="login_form" :style="{ width: isMobile ? '100%' : '' }">
              <div>
                <div v-show="form.loginType !== 1">
                  <a-form-item label="" v-bind="validateInfos.username">
                    <a-input v-model:value.trim="form.username" maxlength="16" placeholder="请输入登录账号" />
                  </a-form-item>
                  <a-form-item label="" v-bind="validateInfos.password">
                    <a-input-password
                      v-model:value.trim="form.password"
                      type="password"
                      maxlength="16"
                      placeholder="请输入密码"
                      @pressEnter="submitForm(ruleForm)"
                    >
                      <template #iconRender="v">
                        <img
                          src="@/assets/largeImages/login/eyeOpen.png"
                          style="width: 16px; height: 11px; cursor: pointer"
                          alt=""
                          v-if="v"
                        />
                        <img
                          src="@/assets/largeImages/login/eye.png"
                          style="width: 16px; height: 8px; cursor: pointer"
                          alt=""
                          v-else
                        />
                      </template>
                    </a-input-password>
                  </a-form-item>
                  <a-form-item>
                    <a-input
                      v-model:value="form.google_code"
                      :maxlength="6"
                      placeholder="请输入六位动态验证码"
                    ></a-input>
                  </a-form-item>
                  <div class="item">
                    <div class="item_txt" @click="passEnt({ name: 'ForgotPwd' })">忘记密码</div>
                  </div>

                  <!-- <div class="flex flex-center">
                    <div
                      :style="{
                        marginTop: '10px',
                        position: 'relative',
                        width: 'auto',
                        height: '32px',
                        overflow: 'hidden'
                      }"
                      id="slidingId"
                    ></div>
                  </div> -->
                </div>
              </div>
              <div>
                <a-button
                  id="submitLogin"
                  class="loginbtn"
                  :style="{ marginTop: form.loginType === 1 ? '20px' : '20px' }"
                  type="primary"
                  :disabled="!allowLogin"
                  @click="handleSubmit"
                >
                  {{ state.loading ? '登录中...' : '立即登录' }}
                </a-button>
              </div>
            </a-form>
          </div>
          <!-- <div class="login-right">
          <div class="desc1">覆盖全链路经营场景</div>
          <div class="desc2">全渠道的精准用户触达</div>
          <div class="desc2">每天服务千万级活跃商家</div>
          <div class="desc2">助力企业实现全流程营销数字化</div>
        </div> -->
        </div>
      </div>
      <div class="right">
        <div class="right_img">
          <img src="@/assets/images/login-hyd/text.png" alt="" />
        </div>
        <div class="desc">{{ getConfig('PROPERTY_RIGHTS') }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  interface State {
    loading: boolean
    text: string
    timer: number | null
    time: number
    status: boolean
    smsDisabled: boolean
    year: string
  }
  import { getConfig, getSystemFile, validateMobile, localStg } from '@/utils'
  import rules from './src/rules'
  import code from './src/code'
  import { loginByPassword, setCaptcha } from './index.api'
  import { computed, onMounted, reactive, ref } from 'vue'
  import { useForm } from 'ant-design-vue/es/form'
  import moment from 'moment'
  import { useLogin, useRouter, useVerify } from '@/hooks'
  import { isMobileDevice } from '@/components/ui/common/QaComments/src/generation'
  import { message } from 'ant-design-vue'
  import { debounce } from 'lodash-es'

  const { nvc, delay: loginDelay, isNvcPass, isSider } = useVerify('submitLogin', 'slidingId', handleSubmit)

  const { ruleLoginData } = rules()

  defineOptions({ name: 'Login' })

  const { routerToBase, routerPush } = useRouter()

  // 表单数据
  const form = reactive({
    loginType: 2,
    phone: '',
    captcha: '',
    username: '',
    password: '',
    validate: 'adhsajkdhaks',
    platform_type: 2,
    google_code: '' //谷歌动态码
  })
  const state = reactive({
    loading: false,
    text: '获取验证码',
    timer: null,
    time: 60,
    status: false,
    smsDisabled: true,
    year: moment().format('YYYY')
  })
  const { validateInfos, validate } = useForm(form, {
    username: ruleLoginData.username,
    password: ruleLoginData.password
  })
  const passEnt = (params: any) => {
    routerPush(params)
  }
  const { getCode, codeData } = code(login, getCatcha, state, form)
  // 是否满足登录规则
  const allowLogin = computed(() => {
    if (form.loginType === 1) {
      return validateMobile(form.phone) && /^\d{6}$/.test(form.captcha) && !state.loading && !isSider.value
    } else {
      return !!form.username && form.password !== '' && !state.loading && !isSider.value
    }
  })
  // 验证码按钮验证
  const smsStatus = computed(() => {
    return validateMobile(form.phone) && !state.status
  })
  const isMobile = computed(() => {
    return isMobileDevice()
  })
  onMounted(() => {
    localStg.clear()
  })
  async function handleSubmit() {
    await validate()
    await loginDelay()
    login()
  }
  // 登录按钮添加防抖
  const debounceSubmit = debounce(handleSubmit, 500)

  // 登录
  const submitForm = async () => {
    try {
      await validate()
      getCode()
    } catch (error) {
      state.loading = false
      codeData.data = {}
      console.error('登录页面报错：', error)
    }
  }
  async function login() {
    state.loading = true
    // 账号登录
    try {
      const { login: hookLogin, isLogin } = useLogin()
      const result = await loginByPassword({
        username: form.loginType == 1 ? form.phone : form.username,
        password: form.password,
        loginType: form.loginType,
        captcha: form.captcha,
        platform_type: form.platform_type,
        google_code: form.google_code,
        ...codeData.data,
        nvc: nvc.value
      })
      state.loading = false
      if (isNvcPass && !isNvcPass(result)) {
        return
      } else {
        await hookLogin({ userInfo: result.data.user_info, token: result.data.token_info.access_token })
        if (isLogin.value) {
          localStg.set('account', result.data.user_info.account)
          // !isMobile ? routerPush({ name: 'MobileData' }) : routerToBase()
          routerToBase()
          message.success('登录成功')
        } else {
          console.log('登录失败：')
        }
      }
    } catch (error) {
      state.loading = false
      codeData.data = {}
      console.error(error)
    } finally {
      state.loading = false
    }
  }
  // 获取验证码
  async function getCatcha() {
    if (!codeData?.data?.randstr) {
      getCode()
      return
    }
    try {
      let res = await setCaptcha({
        phone: form.phone,
        tp: 'login',
        ...codeData.data
      })
      state.status = true
      state.text = '获取验证码'
      state.time = 60
      state.timer = setInterval(() => {
        state.time--
        if (state.time == 0) {
          clearInterval(state.timer)
          state.text = '重新获取'
          state.status = false
          codeData.data = {}
        }
      }, 1000)
    } catch (error) {
      codeData.data = {}
      console.error(error)
    }
  }
</script>
<style lang="scss" scoped>
  .page_login {
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    display: flex;
    .warpper {
      width: 100%;
      display: flex;
      justify-content: space-between;
      position: relative;
    }
    .login_logo {
      width: 88px;
      height: 32px;
      position: absolute;
      top: 39px;
      left: 39px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    :deep(.ant-form-item input) {
      // width: 400px;
      height: 40px;
      line-height: 40px;
      background: #f7f9fc;
      border-radius: 8px;
      border: 1px solid #e6e6e6;
    }
    :deep(.ant-input-affix-wrapper) {
      // width: 400px;
      height: 40px;
      line-height: 40px;
      background: #f7f9fc;
      border-radius: 8px;
      border: 1px solid #e6e6e6;
    }
    :deep(.ant-input-affix-wrapper input) {
      height: auto;
      border: none;
    }
    .left {
      width: 35%;
      // background-image: url('@/assets/images/login-hyd/left.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      position: relative;
    }
    .right {
      flex: 1;
      background: url('@/assets/images/login-hyd/right_2.png') no-repeat center/ cover;
      background-repeat: no-repeat;
      background-position: center;
      position: relative;
      .desc {
        position: absolute;
        left: 50%;
        bottom: 40px;
        transform: translateX(-50%);
        text-align: center;
        font-size: 14px;
        color: #647799;
      }
      .right_img {
        position: absolute;
        top: 10%;
        left: 6.25%;
        width: 300px;
        height: 100px;
        img {
          object-fit: contain;
        }
      }
    }
    .middle {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      .logo_box {
        width: 300px;

        background: #ffffff;
        // box-shadow: 0px 2px 18px 0px rgba(143, 179, 250, 0.62);
        border-radius: 16px;
        // padding: 40px 40px 24px 40px;
      }
      .title {
        height: 45px;
        font-size: 32px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        color: #222222;
        line-height: 45px;
        text-align: center;
        margin-bottom: 8px;
        .login_img {
          width: 138px;
          height: 39px;
        }
      }
      .login_form {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        .item {
          position: relative;
          width: 100%;
          display: flex;
          justify-content: flex-end;
          user-select: none;
          .item_txt {
            margin-top: 16px;
            color: var(--primary-color);
            font-size: 14px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            &.color {
              color: var(--primary-color);
            }
            &:hover {
              color: var(--primary-color);
            }
          }
          .item_btn {
            color: #323233;
            position: absolute;
            top: 3px;
            right: 16px;
          }
        }
        .loginbtn {
          background: var(--primary-color);
          color: #fff;
          width: 100%;
          height: 40px;
          flex-shrink: 0;
        }
        .ant-btn-primary:disabled {
          opacity: 0.5;
        }
        .login_types {
          display: inline-block;
          margin-bottom: 24px;
        }
      }
      :deep(.comp_radio .font_color) {
        color: #969799;
      }
      :deep(.comp_radio .active .font_color) {
        font-size: 24px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        color: #242f57;
        line-height: 33px;
        padding-bottom: 0;
      }
      :deep(.comp_radio .active::after) {
        width: 0 !important;
        height: 0 !important;
      }

      .login-right {
        margin-left: 32px;
        .desc1 {
          font-size: 50px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 500;
          color: #ec7403;
          line-height: 70px;
          letter-spacing: 4px;
          margin-top: 161px;
          margin-bottom: 61px;
          white-space: nowrap;
        }
        .desc2 {
          font-size: 26px;
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          color: #4d678b;
          line-height: 37px;
          letter-spacing: 2px;
        }
      }
    }
    .color-text {
      font-size: 14px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      color: #3b3b3b;
      line-height: 20px;
    }
  }
  .ant-btn-link:disabled {
    color: #323233;
  }
  .ant-btn-link {
    color: #323233;
  }
  .ant-input-affix-wrapper {
    height: 50px;
    line-height: 50px;
  }
  .ant-form-item {
    margin-bottom: 0px;
    margin-top: 16px;
  }
  @media screen and (max-width: 1280px) {
    .page_login {
      .left {
        width: 35%;
      }

      .right {
      }
      .middle {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        .logo_box {
          width: 300px;
        }
        .login-right {
          .desc1 {
            letter-spacing: 0;
          }
          .desc2 {
            letter-spacing: 0;
          }
        }
      }
    }
  }
  @media screen and (max-width: 879px) {
    .page_login {
      width: 100%;
      box-sizing: border-box;
      padding: 70px 0;
      display: flex;
      .left {
        width: 100%;
        position: relative;
        .middle {
          position: relative;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
      .login_logo,
      .right,
      .login-right {
        display: none;
      }
      .warpper {
        justify-content: center;
        position: relative;
      }
      .middle {
        position: static;
        width: 375px;
        padding: 0 20px;
        .title {
          margin-bottom: 24px;
        }
        .logo_box {
          width: 375px !important;
          flex: 1;
        }
        .loginbtn {
          flex: 1;
        }
      }
    }
  }
</style>
