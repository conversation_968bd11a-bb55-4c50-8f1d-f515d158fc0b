import { reactive } from 'vue'

export default function datas() {
  const tableConfigOptions = {
    bordered: true,
    loading: false,
    rowKey: 'id',
    // size: 'small',
    scroll: {
      scrollToFirstRowOnChange: false,
      x: 1000
    },
    dataSource: [],
    columns: [
      {
        title: '导出类型',
        dataIndex: 'type',
        key: 'type',
        slot: true,
        width: 200
      },
      {
        title: '文件名称',
        dataIndex: 'file_path',
        key: 'file_path',
        slot: true,
        width: 280
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        slot: true,
        width: 160
      },
      {
        title: '导出时间',
        dataIndex: 'created_at',
        key: 'created_at',
        slot: true,
        width: 200
      },
      {
        title: '操作人',
        dataIndex: 'user_name',
        key: 'user_name',
        width: 160
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 150,
        fixed: 'right',
        slot: true
        // fixed: 'right'
      }
    ],
    pagination: {
      hideOnSinglePage: false,
      showQuickJumper: true,
      showSizeChanger: true,
      total: 0,
      current: 1,
      pageSize: 10,
      size: 'small',
      showTotal: (total: String | Number) => `共${total}条数据`
    }
  }

  const typeStatus = (type: any) => {
    let status = {
      order: '订单数据',
      refund: '退款数据',
      shop: '店铺数据',
      company: '公司数据',
      member: '会员数据',
      invest_report_ad_latitude: '广告维度数据',
      invest_report_account_latitude: '账号维度数据',
      invest_report_product_latitude: '商品维度数据',
      ad_transform_info: '转化明细数据',
      refund_log: '售后对账明细数据',
      order_bill: '支付对账明细数据',
      order_transaction_info: '支付流水数据',
      after_order: '售后数据',
      boss_order: '订单数据',
      shop_member: '会员数据',
      kefu_list: '客服数据',
      complaints_date: '每日投诉',
      complaints_product: '商品投诉',
      complaints_shop: '店铺投诉',
      complaints_wechat: '小程序投诉',
      export_complaint_label_report: '标签投诉',
      invest_report_ad_latitude_v2: '计划报表_汇总导出',
      invest_report_ad_latitude_day_v2: '计划报表_分日导出',
      invest_report_account_latitude_v2: '账户报表_汇总导出',
      invest_report_account_latitude_day_v2: '账户报表_分日导出',
      invest_report_product_latitude_v2: '商品报表_汇总导出',
      invest_report_product_latitude_day_v2: '商品报表_分日导出',
      invest_report_user_latitude_v2: '用户报表',
      channel_data: '商户拓展渠道数据',
      tbao_order_list: '淘宝订单列表',
      pdd_order_list: '拼多多订单列表',
      tb_order_list: '淘宝订单列表',
      pdd_promotion_settle: '佣金对账',
      invest_report_company_latitude_v2: '公司报表',
      export_category_list: '类目报表',
      ad_transform_info_v2: '广告转化明细',
      jd_order_list: '京东订单数据',
      complaints_boss_wechat_list: '微信支付投诉数据',
      complaints_boss_mini_list: '小程序投诉数据',
      complaints_boss_platform_list: '平台投诉数据',
      product_list: '商品列表数据',
      order_boss_split_return: '分账_出账明细数据',
      order_split_info: '分账_入账明细数据',
      order_shop_split_sum: '分账_汇总数据',
      mini_day_comment_latitude: '每日评价统计',
      mini_app_comment_latitude: '小程序评价统计',
      member_blacklist: '黑名单列表',
      product_comment_latitude: '商品评价统计',
      complaints_special_shop: '店铺投诉',
      complaints_special_product: '商品投诉',
      product_examine_statistics: '商品审核统计',
      export_comment_list: '评价管理',

      shop_expense_discount: '技术服务减免费-列表明细',
      shop_expense_deduction: '额外技术服务费-列表明细',
      shop_split_expense_discount: '技术服务费减免明细',
      shop_split_expense_deduction_shop_list: '额外技术服务费明细扣费记录',
      shop_split_expense_deduction: '额外技术服务费明细',
      shop_security_money_export: '保证金扣除明细',
      welfare_statistic_ad_slot: '广告统计-广告类型统计',
      welfare_statistic_date: '广告统计-每日统计',
      welfare_statistic_app: '广告统计-小程序统计'

    }
    return (status as any)[type]
  }

  const orderStatus = (type: any) => {
    let status = {
      1: {
        color: '#118BCE',
        text: '导出中'
      },
      2: {
        color: '#E77316',
        text: '导出中'
      },
      3: {
        color: '#404040',
        text: '导出成功'
      },
      4: {
        color: '#60A13B',
        text: '已下载'
      },
      5: {
        color: '#E63030',
        text: '导出失败'
      }
    }
    return (status as any)[type]
  }

  return { tableConfigOptions, typeStatus, orderStatus }
}
