import { ref, reactive } from 'vue'
import { validateMobile, generateRandomString } from '@/utils'
import { debounce, includes } from 'lodash-es'
import { getShopListApi } from '@/api/common'
import { getEvent, getRoom, getMember, testNotify, addNotify, changeNotify } from '../../index.api'
import { message } from 'ant-design-vue'
const scopeOptions = [
  {
    label: '商家运营',
    value: 1,
    notify_eventList: [
      {
        label: '投诉汇总',
        value: '1'
      },
      {
        label: '投诉率',
        value: '2'
      },
      {
        label: '发货超时',
        value: '3'
      }
    ]
  },
  {
    label: '平台运营',
    value: 2,
    notify_eventList: [
      {
        label: '商品审核',
        value: '1'
      },
      {
        label: '多多授权',
        value: '2'
      },
      {
        label: '支付授权',
        value: '3'
      },
      {
        label: '小程序授权',
        value: '4'
      },
      {
        label: '微信支付投诉',
        value: '5'
      },
      {
        label: '待支付订单预警',
        value: '6'
      },
      {
        label: '支付配置错误',
        value: '7'
      },
      {
        label: '云函数异常预警',
        value: '8'
      }
    ]
  },
  {
    label: '客户',
    value: 3,
    notify_eventList: [
      {
        label: '商品审核',
        value: '1'
      },
      {
        label: '小程序投诉',
        value: '2'
      },
      {
        label: '微信支付投诉',
        value: '4'
      },
      {
        label: '平台投诉',
        value: '5'
      },
      {
        label: '商品下架',
        value: '6'
      },
      {
        label: '接管投诉退款',
        value: '7'
      },
      {
        label: '商品监测异常',
        value: '8'
      }
    ]
  }
]
const user_idOptions = [
  {
    label: 'BOSS',
    value: 1,
    notify_eventList: [
      {
        label: '商品审核',
        value: 1
      },
      {
        label: '云函数预警',
        value: 2
      },
      {
        label: '投诉预警',
        value: 3
      }
    ]
  },
  {
    label: '店铺',
    value: 2,
    notify_eventList: [
      {
        label: '商品审核',
        value: 1
      },
      {
        label: '投诉预警',
        value: 2
      },
      {
        label: 'ROI预警',
        value: 3
      }
    ]
  }
]
const event_signOptions = [
  {
    label: '1',
    value: 1
  }
]
const notifyObjectOptionsFun = (value) => {
  const obj = {
    1: [
      {
        label: '个人微信群通知',
        value: 5
      }
    ],
    2: [
      {
        label: '钉钉群通知',
        value: 2
      },
      {
        label: '飞书群通知',
        value: 3
      },
      {
        label: '企业微信群通知',
        value: 4
      }
    ],
    3: [
      {
        label: '钉钉群通知',
        value: 2
      },
      {
        label: '飞书群通知',
        value: 3
      },
      {
        label: '企业微信群通知',
        value: 4
      }
    ]
  }
  return obj[value]
}
const notify_objectOptions = [
  {
    label: '短信通知',
    value: 1
  },
  {
    label: ' 钉钉群通知 ',
    value: 2
  },
  {
    label: '飞书群通知',
    value: 3
  },
  {
    label: '企业微信群通知',
    value: 4
  },
  {
    label: '个人微信群通知',
    value: 5
  }
]
const notify_typeOptions = [
  {
    label: '1',
    value: 1
  }
]
const notify_minute = [5, 10, 15, 20, 25, 30].map((minute) => ({
  label: String(minute),
  value: minute
}))

const notify_hour = Array.from({ length: 12 }, (_, i) => ({
  label: i + 1,
  value: i + 1
}))
const notify_day = [
  {
    label: '1',
    value: 1
  }
]
export default function useData(emit, props) {
  const ruleForm = ref(null)
  const textareaCache = { start: '', end: '' }
  const text_content = ref(null)
  const rules = reactive({
    scope: [{ required: true, message: '请选择事件范围', trigger: ['change', 'blur'] }],
    user_id: [{ required: true, message: '请选择所有者', trigger: ['change', 'blur'] }],
    // ad_account_id: [{ required: true, message: '请输入广告账户编号', trigger: ['change', 'blur'] }],
    event_sign: [{ required: true, message: '请选择通知事件', trigger: ['change', 'blur'] }],
    notify_object: [{ required: true, message: '请选择通知对象', trigger: ['change', 'blur'] }],
    notify_type: [{ required: true, message: '请选择通知类型', trigger: ['change', 'blur'] }],
    // doneTimeStatus: [{ required: true, message: '请选择执行时间', trigger: ['change', 'blur'] }],
    content: [{ required: true, message: '请输入通知内容', trigger: ['change', 'blur'] }],
    remark: [{ required: true, message: '请输入通知名称', trigger: ['change', 'blur'] }],
    shop_id: [{ required: true, message: '请选择所有者', trigger: ['change', 'blur'] }],
    notifyTime: [
      {
        required: true,
        validator: (_rule, value, callback) => {
          if (!state.form.notifyDay || !state.form.notifyTime?.length) {
            callback(new Error('请选择通知时间'))
          }
          callback()
        },
        trigger: ['change', 'blur']
      }
    ],
    doneTimeStatus: [
      {
        required: true,
        validator: (_rule, value, callback) => {
          if (!state.form.event_cron) {
            callback(new Error('请选择执行时间'))
          }
          callback()
        },
        trigger: ['change', 'blur']
      }
    ]
  })
  const validPhone = (rule: any, value: string) => {
    if (!validateMobile(value)) {
      return Promise.reject('请输入正确的联系人手机号')
    } else {
      return Promise.resolve()
    }
  }

  let ownerOptions = ref([])
  const state = reactive<any>({
    form: {
      scope: undefined,
      user_id: undefined,
      event_sign: undefined,
      notify_object: undefined,
      notify_type: undefined,
      doneTimeStatus: 1,
      hours: undefined,
      aite: undefined,
      remark: undefined,
      content: undefined,
      notify_addr: undefined,
      notify_secret: undefined,
      notifyDay: undefined,
      notifyTime: [],
      notifyObjectList: [{ item: 18888888888, token: undefined }],
      event_cron: '* * * * *',
      shop_id: undefined,
      company_id: undefined,
      system: 1, // 默认全天制
      systemList: [
        {
          key1: generateRandomString(),
          key2: generateRandomString(),
          key3: generateRandomString(),
          timeRanger: undefined,
          aite: undefined,
          frequency: undefined,
          frequencyType: 1
        }
      ],
      doneTimeList: [
        {
          key: generateRandomString(),
          timeRanger: undefined
        }
      ],
      event_loop: null,
    },
    loading: false,
    originEventSignList: [],
    wxRoomList: [],
    wxMemberList: [],
    btnList: [],
    eventInfo: undefined,
    tutoriaLink: 'https://doc.weixin.qq.com/doc/w3_Aa4A9QbJALMH09zYTWhQWKlqUchTM?scode=ANcAJQf6ADwCMbxenR'
  })
  // 事件对象
  const onAddEventObject = () => {
    state.form.notifyObjectList.push({ item: undefined })
  }
  const onDeleteEventObject = (item: number | string, index: number) => {
    if (state.form.notifyObjectList.indexOf(item) > -1) {
      state.form.notifyObjectList.splice(state.form.notifyObjectList.indexOf(item), 1)
    }
  }
  const onTestEventObject = (item) => { }
  const getList = async () => {
    try {
      let params = {
        page: 1,
        page_size: 100
      }
      const res = await getEvent(params)
      state.originEventSignList = (res.data.list || []).map((item) => {
        return {
          ...item,
          label: item.event_name,
          value: item.event_sign
        }
      })
      // console.log(res, 'res')
    } catch (error) { }
  }
  const editecho = async () => {
    await getList()

    // state.btnList = option.tags || []
    // state.eventInfo = option || undefined

    console.log(state.originEventSignList, 'state.originEventSignList')

    if (props.item) {
      // console.log('props.item', props.item)
      state.eventSignList = state.originEventSignList.filter((it) => it.scope === props.item.scope)
      // 处理执行时间
      let dataTime = []
      if (props.item.scope == 1) {
        dataTime = props.item.event_cron.map((cron) => parseCronValues(cron))
      }
      state.form = {
        event_sign: props.item.event_sign,
        notify_object: props.item.notify_object,
        notify_addr: props.item.notify_addr,
        notify_secret: props.item.notify_secret,
        notify_type: props.item.notify_type != 0 ? props.item.notify_type : undefined,
        content: props.item.content,
        scope: props.item.scope,
        remark: props.item.remark,

        shop_id: props.item.shop_id,
        company_id: props.item.company_id
      }
      state.form.event_loop = props.item.event_sign
      if (state.form.scope === 1) {
        state.form.doneTimeStatus = 1
      }
      if (props.item.notify_object == 5) {
        console.log(props.item.notify_mention, 'props.item.notify_mention')

        state.form.aite = props.item.notify_mention || []
        await getRoomList()
        await getMemberList()
      } else {
        state.form.aite = props.item.notify_mention.join(',')
      }
      let option = state.originEventSignList.find(
        (item: { event_sign: number }) => item.event_sign === props.item?.event_sign
      )

      state.btnList = option.tags || []
      state.eventInfo = option || undefined
      state.form.system = props.item.notify_mention?.length > 1 ? 2 : 1
      state.form.systemList =
        props.item.scope == 1
          ? props.item.event_cron.map((range, index) => {
            let times = range.split('-')
            return {
              aite: props.item.notify_mention[index] ? props.item.notify_mention[index] : undefined,
              timeRanger: dataTime[index].hour,
              frequency: dataTime[index].frequency,
              frequencyType: dataTime[index]?.type || 1
            }
          })
          : null
    }
  }
  editecho()
  //店铺名称
  async function getShopList() {
    try {
      let { data } = await getShopListApi({ page: 1, page_size: 9999 })
      ownerOptions.value = (data.list || []).map((item) => {
        return {
          ...item,
          value: item.shop_info.id,
          label: item.shop_info.name
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getShopList()
  function parseCronValues(cron) {
    const cronParts = cron.split(' ')
    let returnData = {
      minute: undefined,
      hour: undefined,
      day: undefined,
      month: undefined,
      week: undefined,
      type: undefined,
      frequency: undefined
    }
    // 确保有 5 个部分
    if (cronParts?.length !== 5) {
      throw new Error('Invalid Cron expression. It should have 5 parts.')
    }

    const [minute, hour, day, month, week] = cronParts
    console.log('this.log', minute, hour, day, month, week)
    if (minute.includes('/')) {
      returnData.type = 1
      returnData.minute = minute.split('/')[1]
      returnData.frequency = minute.split('/')[1]
      if (hour.includes('-')) {
        returnData.hour = hour.split('-')
      } else {
        returnData.hour = hour
      }
    } else if (hour.includes('/')) {
      returnData.type = 2
      returnData.frequency = hour.split('/')[1]
      returnData.hour = hour.split('/')[0]
      if (returnData.hour.includes('-')) {
        returnData.hour = returnData.hour.split('-')
      } else {
        returnData.hour = [returnData.hour]
      }
    } else if (day.includes('/')) {
      returnData.type = 3
      returnData.frequency = day.split('/')[1]
      returnData.hour = hour
    } else {
      returnData.type = 2
      returnData.frequency = 1
      returnData.hour = hour.split('-')
    }

    return returnData
  }
  //下拉框-事件-对象-类型-微信群成员-艾特人
  const change = (e, option, type) => {
    console.log(e, option, type)
    try {
      switch (type) {
        case 'event_sign':
          state.form.event_loop = option.event_loop
          state.btnList = option.tags || []
          state.eventInfo = option || undefined
          if (state.form.notify_type && state.form.notify_object) contentView(state.form.notify_type)
          break
        case 'notify_object':
          state.form.notify_addr = undefined
          state.form.notify_secret = undefined
          state.form.notify_type = undefined
          state.form.aite = undefined
          state.form.content = undefined

          if (e === 5) getRoomList()
          break
        case 'notify_type':
          contentView(e)
          break
        case 'notify_addr':
          state.form.aite = undefined
          getMemberList()
          break
        case 'aite':
          console.log(e, 'aite')
          if (/\ball\b/.test(e.trim())) {
            if (e.split(',').length > 1) message.warning('已通知所有人，不可单个通知')
            state.form.aite = 'all'
          }

          break
        case 'scope':
          state.eventSignList = state.originEventSignList.filter((it) => it.scope === e)
          state.form.event_sign = undefined
          state.btnList = []
          state.eventInfo = undefined
          state.form.notify_type = undefined
          state.form.notify_object = undefined
          state.form.shop_id = undefined
          state.form.company_id = undefined
          break
        case 'shop_id':
          state.form.company_id = option.shop_info.company_id

          break

        default:
          break
      }
    } catch (error) { }
  }
  //根据对象和类型判断展示的事件内容
  const contentView = (e: number) => {
    if (e === 1) {
      state.form.content = state.eventInfo.content
    } else {
      const key = state.form.notify_object
      switch (key) {
        case 2:
          state.form.content = state.eventInfo.ding_markdown
          break
        case 3:
          state.form.content = state.eventInfo.fei_markdown
          break
        case 4:
          state.form.content = state.eventInfo.work_markdown
          break
        default:
          state.form.content = state.eventInfo.content
          break
      }
    }
    textareaCache.start = state.form.content
    textareaCache.end = ''
    ruleForm.value.validateFields(['content'])
  }
  //点击按钮添加标签
  const onAddeventSign = debounce((e, event) => {
    if (props.item && state.form.scope === 3) return
    if (e === 'stencil') {
      contentView(state.form.notify_type)
      textareaCache.start = state.form.content
      textareaCache.end = ''
    } else {
      if (!textareaCache.start && !textareaCache.end) {
        state.form.content = e
        textareaCache.start = e
      } else {
        state.form.content = textareaCache.start + e + textareaCache.end
        // textareaCache.start = state.form.content
        textareaCache.end = e + textareaCache.end
      }
    }
  }, 500)
  //点击内容文本框获取焦点
  const handleTextareaClick = () => {
    const textarea: any = document.getElementById('textarea')
    // const cursorPos = textarea.selectionStart

    let _content = state.form?.content || ''
    const startPos = textarea.selectionStart
    const endPos = textarea.selectionEnd

    // 插入标签名称到光标位置或文本末尾
    const textBeforeCursor = _content.substring(0, startPos)
    const textAfterCursor = _content.substring(endPos)

    console.log(textBeforeCursor, 'textBeforeCursor')
    console.log(textAfterCursor, ' textAfterCursor')
    textareaCache.start = textBeforeCursor
    textareaCache.end = textAfterCursor
  }
  //微信群列表
  const getRoomList = async () => {
    try {
      let res = await getRoom()
      state.wxRoomList = (res.data || []).map((item) => {
        return {
          ...item,
          value: item.gid,
          label: item.g_name
        }
      })
      console.log(res, 'res')
    } catch (error) {
      console.error(error)
    }
  }
  //微信群成员列表
  const getMemberList = async () => {
    try {
      let params = {
        gid: state.form.notify_addr
      }
      let res = await getMember(params)
      state.wxMemberList = (res.data || []).map((item) => {
        return {
          ...item,
          value: item.wx_id,
          label: item.nick_name
        }
      })
      console.log(res, 'res')
    } catch (error) {
      console.error(error)
    }
  }
  //测试发送
  const testSend = async () => {
    await ruleForm.value.validate()
    let _notify_mention = []
    let event_cron_list = []
    if (state.form.scope == 2) {
      if (state.form.aite && state.form.notify_object !== 5) {
        _notify_mention = state.form.aite.split(',')
      } else {
        _notify_mention = state.form.aite || []
      }
    }
    let params = {
      event_sign: state.form.event_sign,
      notify_object: state.form.notify_object,
      notify_addr: state.form.notify_addr,
      notify_secret: state.form.notify_secret,
      notify_type: state.form.notify_type,
      content: state.form.content,
      notify_mention: _notify_mention,

    }
    console.log(params, 'params')

    const res = await testNotify(params)

    if (res.code == 10000) {
      message.success('测试发送成功')
    } else {
      message.warning(res.msg)
    }
  }
  //保存
  const submitForm = async () => {
    try {
      await ruleForm.value.validate()
      let _notify_mention = []
      let event_cron_list = []
      if (state.form.scope == 2) {
        if (state.form.aite && state.form.notify_object !== 5) {
          _notify_mention = [state.form.aite.split(',')]
        } else {
          _notify_mention = state.form.aite || [[]]
        }
      } else {
        if (state.form.systemList.length != 0) {
          _notify_mention = state.form.systemList.map((item) => {
            return item.aite
          })
          event_cron_list = state.form.systemList.map((item) => {
            let tiemData = ''
            if (item.frequencyType == 1) {
              const timeRange = item.timeRanger && item.timeRanger.join('-')
              tiemData = `*/${item.frequency} ${timeRange} * * *`
            } else if (item.frequencyType == 2) {
              const timeRange = item.timeRanger && item.timeRanger.join('-')
              tiemData = `0 ${timeRange}/${item.frequency} * * *`
            } else if (item.frequencyType == 3) {
              const timeRange = item.timeRanger
              // tiemData = `0 ${timeRange.split('-')[0]} */${item.frequency} * *`
              tiemData = `0 ${timeRange} */${item.frequency} * *`
            }
            return tiemData // 拼接成完整的 cron 表达式
          })
        }
      }

      let params = {
        event_sign: state.form.event_sign,
        notify_object: state.form.notify_object,
        notify_addr: state.form.notify_addr,
        notify_secret: state.form.notify_secret,
        notify_type: state.form.notify_type,
        content: state.form.content,
        notify_mention: _notify_mention,
        remark: state.form.remark,
        scope: state.form.scope,
        event_cron: event_cron_list,
        shop_id: state.form.shop_id
      }
      if (state.form.shop_id) {
        params.company_id = state.form.company_id
      }
      let res
      if (props.item) {
        params.id = props.item.id
        res = await changeNotify(params)
      } else {
        res = await addNotify(params)
      }
      if (res.code == 10000) {
        message.success('保存成功')
      } else {
        message.warning(res.msg || res.data)
        return
      }

      emit('event', { cmd: 'submit', status: true })
    } catch (err) {
      console.log('err', err)
    }
  }
  //取消
  const close = () => {
    emit('event', { cmd: 'close', status: true })
  }
  // 所有者搜索
  const handleSearchOption = (input: string, option: any) => {
    console.log('handleSearchOption===', input, option)
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }
  const doneTimeHandler = (type: string, item: any) => {
    if (type === 'del') {
      state.form.doneTimeList = state.form.doneTimeList.filter((i) => i.key !== item.key)
    } else if (type === 'add') {
      if (!state.form.doneTimeList.every((it) => it.timeRanger?.length)) {
        message.warning('请先补充完整信息')
        return false
      }
      state.form.doneTimeList.push({
        key: generateRandomString(),
        timeRanger: undefined
      })
    } else if (type === 'change') {
      if (item.target?.value === 1) {
        state.form.doneTimeList = [
          {
            key: generateRandomString(),
            timeRanger: undefined
          }
        ]
      }
    }
  }
  // 提及人相关
  const sysHandler = (type: string, item: any, index: number) => {
    if (type === 'del') {
      state.form.systemList.splice(index, 1)
    } else if (type === 'add') {
      if (
        !state.form.systemList.every(
          (it) => it.timeRanger?.length && it.frequency && (state.form.system !== 2 || (it.aite && it.aite.length > 0))
        )
      ) {
        message.warning('请先补充完整信息')
        return false
      }
      state.form.systemList.push({
        key1: generateRandomString(),
        key2: generateRandomString(),
        timeRanger: undefined,
        aite: undefined,
        frequency: undefined,
        frequencyType: 1
      })
    } else if (type === 'change') {
      state.form.systemList = [
        {
          key1: generateRandomString(),
          key2: generateRandomString(),
          timeRanger: undefined,
          aite: undefined,
          frequency: undefined,
          frequencyType: 1
        }
      ]

    }
  }
  const onFiterOption = (value: any, option: any) => {
    if (option.label.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }
  return {
    state,
    rules,
    scopeOptions,
    user_idOptions,
    onAddeventSign,
    event_signOptions,
    notify_objectOptions,
    notify_typeOptions,
    submitForm,
    close,
    text_content,
    onAddEventObject,
    onDeleteEventObject,
    onTestEventObject,
    change,
    handleTextareaClick,
    validPhone,
    testSend,
    ruleForm,
    notifyObjectOptionsFun,
    ownerOptions,
    handleSearchOption,
    sysHandler,
    onFiterOption,
    doneTimeHandler,
    notify_minute,
    notify_hour,
    notify_day
  }
}
