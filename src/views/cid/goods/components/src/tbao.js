
import { reactive, ref ,onMounted } from 'vue'
import { useRoute } from 'vue-router'
// fetchShopList,
import { fetchTbProductList,  delTbGoods } from '../../index.api'
  import { getShopListApi } from '@/api/common'
import { message } from 'ant-design-vue'
export default function datas() {


  const searchData = ref([
    {
      type: 'select',
      field: 'prod_type',
      value: undefined,
      props: {
        placeholder: '请选择商品类型',
        options: [
          {
            label: 'UDSmart',
            value: 1
          },
         
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'input.text',
      field: 'name',
      value: undefined,
      props: {
        placeholder: '请输入商品名称'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
     {
      type: 'input.text',
      field: 'item_id',
      value: undefined,
      props: {
        placeholder: '请输入商品ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'select',
      field: 'shop_id',
      props: {
        placeholder: '请选择店铺名称',
        options: []
      },
      value: undefined,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    // {
    //   type: 'select',
    //   field: 'pdd_shop_id',
    //   value: undefined,
    //   props: {
    //     placeholder: '请选择店铺(淘宝)',
    //     options: []
    //   },
    //   layout: {
    //     xs: 24,
    //     sm: 12,
    //     md: 8,
    //     lg: 8,
    //     xl: 8,
    //     xxl: 6
    //   }
    // },
    
    {
      type: 'input.text',
      field: 'remark',
      value: undefined,
      props: {
        placeholder: '请输入备注'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: undefined,
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  ])
  const route = useRoute()
  const actions = {
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 6
    }
  }
  const state = reactive({
    activeName: 'pdd',
    initParams: {
      page: 1,
      size: 10,
      product_name: '',
      prod_type: 1,
      created_at: null,
      ad_link: null
    },
    tableConfigOptions: {
      bordered: true,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 'max-content'
      },
      dataSource: [],
      columns: [
         {
        title: '店铺名称',
        dataIndex: 'shop_name',
        key: 'shop_name',
        width: 140
      },
         {
          title: '商品类型',
          dataIndex: 'type',
          key: 'type',
          width: 100
        },
        {
          title: '商品信息',
          dataIndex: 'title',
          key: 'title',
          // width: 
        },
        {
          title: '备注',
          dataIndex: 'remark',
          key: 'remark',
          width: 360
        },
        {
          title: '创建时间',
          dataIndex: 'updated_at',
          key: 'updated_at',
          width: 240
        },
         {
          title: '操作',
          dataIndex: 'action',
          key: 'action',
          fixed: 'right',
          width: 100
        }
       
      ],
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        current: 1,
        pageSize: 10,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    },
    item: {},
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: ''
    },
    cateGoryList: [],
    selectionItem: [] // 表格选择的Item
  })

  const pageChange = (pagination) => {
    state.initParams.page = pagination.current
    state.initParams.size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getProductList()
  }
    //店铺名称
  async function getShopList() {
    try {
      let { data } = await getShopListApi({ page: 1, page_size: 9999 })
      searchData.value.forEach((v) => {
        if (v.field == 'shop_id') {
          v.props.options = (data.list || []).map((item) => {
            return {
              value: item.shop_info.id,
              label: item.shop_info.name
            }
          })
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  getShopList()
  // 搜索
  const changeValue = (data) => {
    console.log('search 参数', data)
    state.initParams = {
      ...state.initParams,
      ...data.formData,
      begin_time: data.formData.created_at ? data.formData.created_at[0] : '',
      end_time: data.formData.created_at ? data.formData.created_at[1] : '',
      page: 1
    }
    getProductList()
  }

  /**
   * 删除item
   * @param {string} id 商品id
   */
  const onDel = async (id) => {
    try {
      await delTbGoods({ id: id })
      message.success('删除当前商品成功')
      getProductList()
    } catch (error) {
      console.error(error)
    }
  }

  const onShowDialog = (type, item) => {
    state.item = { ...item }
    switch (type) {
      case 'ad':
        state.dialog = { visible: true, title: '广告投放', width: 870, type: 'ad' }
        break
      case 'goods':
        state.dialog = { visible: true, title: '添加商品', width: 600, type: 'goods' }
        break
      case 'config':
        state.dialog = { visible: true, title: '选择替换链接', width: 680, type: 'config' }
        break
      case 'remark':
        state.dialog = { visible: true, title: '编辑备注', width: 600, type }
        break
    
    }
  }

  const onEvent = (data) => {
    if (data.cmd == 'close') {
      state.dialog.visible = false
    }else if (data.cmd == 'list') {
      state.dialog.visible = false
     onShowDialog('record', state.item )
    }
     else {
      state.dialog.visible = false
      getProductList()
    }
  }


  // 商品列表
  const getProductList = async () => {
    try {
      state.tableConfigOptions.loading = true
      console.log('-------', state.initParams)
      const resp = await fetchTbProductList(state.initParams)
      state.tableConfigOptions.dataSource = resp.data?.list || []
      state.tableConfigOptions.pagination.total = resp.data?.total_num || 0
      state.tableConfigOptions.pagination.current = resp.data?.page || 1

      state.tableConfigOptions.loading = false
    } catch (error) {
      console.error(error)
      state.tableConfigOptions.dataSource = []
      state.tableConfigOptions.loading = false
    }
  }


  const getListData = () =>{
    onMounted(() => {
      if (route.query?.product_name || route.query?.id) {
        state.initParams.name = route.query?.product_name || route.query?.id
        searchData.value.find((item) => item.field == 'name').value = route.query?.product_name || route.query?.id
        getProductList()
      } else {
        getProductList()
      }
      getShopList()
    })
  }
  

  return {
    searchData,

    state,
    actions,
    getListData,
    getProductList,
    onShowDialog,

    changeValue,
    onDel,
    onEvent,
    pageChange
  }
}
