<template>
  <div>
    <a-space direction="vertical" class="w-full">
      <SearchBaseLayout :data="searchData" @changeValue="changeValue" :actions="actions" />
      <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange">
        <template #bodyCell="{ scope }">
          <template v-if="scope.column.key === 'type'">
            <div>{{ scope.record.prod_type == 1 ? 'UDSmart' : 'UDSmart' }}</div>
          </template>
          <template v-if="scope.column.key === 'title'">
            <div class="flex goods_info">
              <div class="img">
                <a-image
                  :width="60"
                  :preview="false"
                  style="width: 60px; height: 60px; border-radius: 6px"
                  :src="scope.record.url"
                />
              </div>
              <div class="goods_info_data">
                <a-tooltip placement="top">
                  <template #title>{{ scope.record.name }}</template>
                  <p class="goods_info_data_name">
                    {{ scope.record.name }}
                  </p>
                </a-tooltip>

                <span class="goods_info_data_number">商品ID：{{ scope.record.item_id }}</span>
              </div>
            </div>
          </template>
          <template v-if="scope.column.key === 'remark'">
            <a-tooltip placement="top">
              <template #title>{{ scope.record.remark }}</template>
              <a-space class="edit_remark">
                <div class="goods_info_data_name1">
                  {{ scope.record.remark || '-' }}
                </div>
                <!-- <EditOutlined class="mb15px remark_icon" @click="onShowDialog('remark', scope.record)" /> -->
              </a-space>
            </a-tooltip>
          </template>

          <template v-if="scope.column.key === 'updated_at'">
            <div class="flex-col">
              <!-- <span>{{ scope.record.updated_at ? formatDate(scope.record.updated_at * 1000) : '--' }}</span> -->
              <span>{{ scope.record.created_at || '--' }}</span>
            </div>
          </template>
          <template v-if="scope.column.key === 'action'">
            <a-button
              v-auth="['cidShopLink']"
              type="link"
              :disabled="scope.record.monitor_status == 2 && ![1, 2].includes(scope.record.tibao_status)"
              @click="onShowDialog('ad', scope.record)"
              class="item-btn pa-0 mr-5px"
            >
              推广链接
            </a-button>
            <a-popconfirm title="请确认是否删除当前商品？" placement="topRight" @confirm="onDel(scope.record.id)">
              <a-button v-auth="['delCidShop']" type="link" class="item-btn pa-0"> 删除 </a-button>
            </a-popconfirm>
          </template>
        </template>
      </TableZebraCrossing>
    </a-space>
    <!-- </a-card> -->
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
    >
      <!-- <AddGoods v-if="state.dialog.type === 'goods'" @event="onEvent" /> -->
      <AdLaunch v-if="state.dialog.type === 'ad'" :goodsDetail="state.item" @event="onEvent" />
      <!-- <EditRemark v-if="state.dialog.type === 'remark'" :item="state.item" @event="onEvent" type="tb" /> -->

      <!-- <ReplaceLink
        v-if="['link', 'number'].includes(state.dialog.type)"
        :type="state.dialog.type"
        :item="state.item"
        @event="onEvent"
      />
      <ReplaceRecord
        v-if="state.dialog.type === 'record'"
        :type="state.dialog.type"
        :item="state.item"
        @event="onEvent"
      /> -->
    </a-modal>
  </div>
</template>
<script setup lang="ts">
  import { formatDate } from '@/utils'

  import AdLaunch from './TbAdLaunch.vue'

  // import AddGoods from './TbAddGoods.vue'

  // import EditRemark from './EditRemark.vue'

  import { ExclamationCircleFilled, EditOutlined } from '@ant-design/icons-vue'
  import datas from './src/tbao'

  const { searchData, state, actions, getListData, onShowDialog, changeValue, onDel, onEvent, pageChange } = datas()
  getListData()
  defineExpose({
    onShowDialog
  })
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn';

  .round {
    width: 6px;
    height: 6px;
    margin-right: 4px;
    border-radius: 50%;
  }
  .item-monitor-tag {
    vertical-align: middle;
    display: inline-block;
    padding: 0 6px;
    font-size: 12px;
    line-height: 18px;
    border-radius: 2px;
    cursor: default;
  }
  .item-primary {
    color: #15c369;
    border: 1px solid #15c369;
  }
  .item-error {
    color: #f44545;
    border: 1px solid #f44545;
  }
  .already {
    background-color: #f44545;
    color: #fff;
  }
  .soon {
    background-color: #c1c1c1;
    color: #fff;
  }
  .goods_info_data_remak {
    overflow: hidden; //多出的隐藏
    text-overflow: ellipsis; //多出部分用...代替
    display: -webkit-box; //定义为盒子模型显示
    -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
    -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
    font-size: 14px;
  }
  .goods_info {
    img,
    .img_item {
      width: 60px;
      height: 60px;
      background: #bec6d6;
      border-radius: 6px;
    }

    &_data {
      margin-left: 10px;
      font-family: PingFang SC;
      font-weight: 400;

      &_name {
        overflow: hidden; //多出的隐藏
        text-overflow: ellipsis; //多出部分用...代替
        display: -webkit-box; //定义为盒子模型显示
        -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
        -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
        font-size: 14px;
        color: var(--primary-color);
      }

      &_number {
        font-size: 12px;
        color: #999999;
      }
    }
  }

  .price {
    span {
      margin-right: 9px;
      display: inline-block;
    }

    &_icon {
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }
  .time {
    color: #999999;
    font-size: 12px;
    margin-top: 4px;
    line-height: 1;
  }
  .ant-btn + .ant-btn {
    margin-left: 0;
  }
  .remark_icon {
    display: none;
  }
  .remark_icon:hover {
    color: #fe9d35;
  }
  .edit_remark:hover {
    .remark_icon {
      display: block;
    }
  }
</style>
