// 导出请求封装方法
import http from '@/utils/request'

/**
 * 淘宝推广链接列表
 */
export const fetchTbAdList = (data: any) => {
  return http('post', `/manage/tb/get_list`, data)
}
/**
 * 淘宝商品/列表
 */
export const fetchTbProductList = (data: any) => {
  return http('post', `/merchant/tb/get_product_list`, data)
}
/**
 * 获取淘宝商品详情
 */
export const getTbProductDetail = (data: any) => {
  return http('get', `/manage/tb/get_product_info`, data)
}
/**
 * 添加淘宝商品
 */
export const addTbProduct = (data: any) => {
  return http('post', `/manage/tb/add_product`, data)
}
/**
 * 淘宝商品/删除
 */
export const delTbGoods = (data: any) => {
  return http('get', `/manage/tb/del_product`, data)
}
