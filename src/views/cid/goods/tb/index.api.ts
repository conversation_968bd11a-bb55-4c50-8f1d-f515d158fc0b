// 导出请求封装方法
import http from '@/utils/request'

/**
 * 商品/列表
 */
export const fetchProductList = (data: any) => {
  return http('get', `/shop-admin/pdd/product/list`, data)
}
/**
 * 商品/删除
 */
export const delProduct = (data: any) => {
  return http('post', `/shop-admin/pdd/product/delete`, data)
}
/**
 * 获取拼多多商店列表
 */
export const fetchShopList = (data: any) => {
  return http('get', `/shop-admin/pdd/shop/list`, data)
}
/**
 * 获取广告列表
 */
export const getAddList = (data: any) => {
  return http('get', `/shop-admin/pdd/get_pdd_ad_list`, data)
}
/**
 * 添加广告链接
 */
export const addAdurl = (data: any) => {
  return http('post', `/shop-admin/pdd/add_pdd_ad_url`, data)
}
/**
 * 修改广告链接
 */
export const updateAdurl = (data: any) => {
  return http('post', `/shop-admin/pdd/update_pdd_ad_url`, data)
}
/**
 * 删除广告链接
 */
export const delAdurl = (data: any) => {
  return http('post', `/shop-admin/pdd/delete_pdd_ad`, data)
}
/**
 * 获取商品详情
 */
export const getProductDetail = (data: any) => {
  return http('get', `/shop-admin/pdd/product/info`, data)
}
/**
 * 添加商品
 */
export const createProduct = (data: any) => {
  return http('post', `/shop-admin/pdd/product/create`, data)
}
/**
 * 查询报备商品信息
 */
export const queryGoods = (data: any) => {
  return http('get', `/shop-admin/pdd/report/info`, data)
}
/**
 * 搜索商品是否报备
 */
export const searchGoods = (data: any) => {
  return http('post', `/pdd_audit/search_goods`, data)
}
/**
 * 上传视频初始化
 */
export const uploadVideoInit = (data: any) => {
  return http('post', `/shop-admin/pdd/report/video_part_init`, data)
}

/**
 * 上传视频分片
 */
export const uploadVideoPart = (data: any) => {
  return http('post', `/shop-admin/pdd/report/video_part_store`, data)
}
/**
 * 上传视频完成
 */
export const uploadVideoComplete = (data: any) => {
  return http(
    'post',
    `/shop-admin/pdd/report/video_part_complete
`,
    data
  )
}
/**
 * 商品报备
 */
export const saveTibaoSave = (data: any) => {
  return http('post', `/pdd_audit/audit_save`, data)
}
/**
 * 上传图片
 */
export const uploadStoreImage = (data: any) => {
  return http('post', `/pdd_audit/store_image`, data)
}
/**
 * 获取小程序码
 */
export const miniCode = (data: any) => {
  return http('get', `/shop-admin/ad/mini_code`, data)
}

/**
 * 获取商品
 */
export const miniGoodsCode = (data: any) => {
  return http('get', `/shop-admin/product/show`, data)
}

/**
 * 广告列表
 */
export const setAdList = (data: any) => {
  return http('get', `/shop-admin/ad/list`, data)
}

/**
 * 广告详情
 */
export const setAdInfo = (data: any) => {
  return http('get', `/shop-admin/ad/info`, data)
}

/**
 * 发布广告链接
 */
export const cratedAd = (data: any) => {
  return http('post', `/shop-admin/ad/add`, data)
}

/**
 * 编辑广告
 */
export const editAd = (data: any) => {
  return http('post', `/shop-admin/ad/edit`, data)
}

/**
 * 删除广告
 */
export const deleteAd = (data: any) => {
  return http('post', `/shop-admin/ad/delete`, data)
}

/**
 * 商品/复制
 */
export const copyProduct = (data: any) => {
  return http('post', `/shop-admin/product/copy`, data)
}

/**
 * 获取广告列表
 */
export const setAdDmpList = (data: any) => {
  return http('get', `/shop-admin/ad/dmp_list`, data)
}
/**
 * 获取广告列表 磁力
 */
export const magnetism_list = (data: any) => {
  return http('get', `/shop-admin/ad/magnetism_list`, data)
}

/**
 * 非配送区域详情
 * https://www.apifox.cn/web/project/2014698/apis/api-55985617
 */
export const setDeliverInfo = (data: any) => {
  return http('get', `/shop-admin/un_deliver/info`, data)
}

/**
 * 回传行为
 */
export const callbackList = (data: any) => {
  return http('get', `/shop-admin/ad/callbck_list`, data)
}

/**
 * 获取小程序id
 */
export const getAdAppidListApi = (data: any) => {
  return http('get', `/shop-admin/ad/get_appid_list`, data)
}
/**
 * 回传行为
 */
export const batchSetFreight = (data: any) => {
  return http('post', `/shop-admin/product/batch_update_freight`, data)
}
/**
 * 获取商品sku详情
 */
export const getProductSku = (id: any) => {
  return http('get', `/shop-admin/product/sku_price`, { id: id })
}
/**
 * 批量修改价格
 */
export const batchEditPrice = (data: any) => {
  return http('post', `/shop-admin/product/batch_sku_price`, data)
}
/**
 * 修改排序
 */
export const editProductSort = (data: any) => {
  return http('post', `/shop-admin/product/update_sort`, data)
}

/**
 * 列表角标统计
 */
export const productListCount = (data: any) => {
  return http('get', `/shop-admin/product/list_count`, data)
}

/**
 * 淘宝添加商品
 */
export const createTbProduct = (data: any) => {
  return http('post', `/shop-admin/tb/product/create`, data)
}
/**
 * 淘宝添加商品
 */
export const updateTbProduct = (data: any) => {
  return http('post', `/shop-admin/tb/product/update`, data)
}
/**
 * 淘宝商品/列表
 */
export const fetchTbProductList = (data: any) => {
  return http('get', `/shop-admin/tb/product/list`, data)
}
/**
 * 淘宝获取商品详情接口
 */
export const fetchTbProductInfo = (data: any) => {
  return http('get', `/shop-admin/tb/product/info`, data)
}
/**
 * 淘宝创建推广链接
 */
export const createTbAd = (data: any) => {
  return http('post', `/shop-admin/tb/ad/create`, data)
}
/**
 * 淘宝推广链接列表
 */
export const fetchTbAdList = (data: any) => {
  return http('get', `/shop-admin/tb/ad/list`, data)
}
/**
 * 淘宝修改推广链接
 */
export const editTbAd = (data: any) => {
  return http('post', `/shop-admin/tb/ad/edit`, data)
}
/**
 * 淘宝推广链接详情
 */
export const fetchTbAdInfo = (data: any) => {
  return http('get', `/shop-admin/tb/ad/info`, data)
}
/**
 * 淘宝删除推广链接
 */
export const delTbAd = (data: any) => {
  return http('post', `/shop-admin/tb/ad/delete`, data)
}

export const get_html_list = (data: any) => {
  return http('get', `/shop-admin/html/list`, data)
}

export const get_html_detail = (data: any) => {
  return http('get', `/shop-admin/html/detail`, data)
}

/**
 * 淘宝删除推广链接
 */
export const delTbProduct = (data: any) => {
  return http('post', `/shop-admin/tb/product/delete`, data)
}

/**
 * 以下是京东接口
 *
 *
 * 京东商品列表
 */
export const jdList = (data: any) => {
  return http('get', `/shop-admin/jd/list`, data)
}
/**
 *
 * 查询京东商品详情
 */
export const productInfo = (data: any) => {
  return http('get', `/shop-admin/jd/product_info`, data)
}
/**
 *
 * 添加修改京东商品
 */
export const addProduct = (data: any) => {
  return http('post', `/shop-admin/jd/add_product`, data)
}
/**
 *
 * 创建京东广告项目
 */
export const tkJdAdCreate = (data: any) => {
  return http('post', `/shop-admin/tkJdAd/create`, data)
}
/**
 *
 * 更新京东广告项目
 */
export const tkJdAdUpdateAd = (data: any) => {
  return http('post', `/shop-admin/tkJdAd/update_ad`, data)
}
/**
 *
 * 获取京东广告列表
 */
export const tkJdAdList = (data: any) => {
  return http('get', `/shop-admin/tkJdAd/list`, data)
}
/**
 *
 * 获取京东广告列表
 */
export const tkJdAdDelete = (data: any) => {
  return http('post', `/shop-admin/tkJdAd/delete`, data)
}
/**
 *
 * 删除京东商品列表
 */
export const jdDelProduct = (data: any) => {
  return http('post', `/shop-admin/jd/del_product`, data)
}

/**
 * 获取组织/广告主列表
 *
 */
export const getOrglist = (data) => {
  return http('get', `/shop-admin/ad/get_oceanengine_list`, data)
}
/**
 * 获取替换记录
 *
 */
export const getChangeLogList = (data) => {
  return http('get', `/shop-admin/pdd/change_ad_promotion_log`, data)
}
/**
 *
 * 替换链接
 */
export const changeAdLink = (data: any) => {
  return http('post', `/shop-admin/pdd/change_ad_promotion`, data)
}
/**
 *
 * 修改拼多多备注
 */
export const updateTbRemark = (data: any) => {
  return http('post', `/merchant/tb/edit_product_ramark`, data)
}
