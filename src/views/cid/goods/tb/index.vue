<template>
  <div>
    <DesTablePage class="shipping_templates">
      <template #title>
        <div>淘宝商品</div>
      </template>
      <template #extra>
        <a-button v-auth="['addCidShop']" type="primary" @click="onShowDialog()">添加商品</a-button>
      </template>
      <template #tableWarp>
        <Tb ref="tbRef" />
      </template>
    </DesTablePage>
  </div>
</template>
<script setup lang="ts">
  import Tb from '../components/TbList.vue'
  import { ref } from 'vue'
  const tbRef = ref(null)
  const onShowDialog = (type: any) => {
    tbRef.value.onShowDialog('goods')
  }
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn';
</style>
