<template>
  <DesTablePage class="shipping_templates">
    <template #title>
      <div>淘宝订单</div>
    </template>
    <template #extra>
      <a-button v-auth="['shopCidTbOrderExport']" type="primary" @click="download"> 导出 </a-button>
    </template>
    <template #tableWarp>
      <TbOrder ref="tbRef" />
    </template>
  </DesTablePage>
</template>
<script setup lang="ts">
  import TbOrder from '../components/TbOrder.vue'
  import { ref } from 'vue'
  const tbRef = ref(null)
  // 导出
  const download = async () => {
    tbRef.value.downloadTbao()
  }
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn';
</style>
