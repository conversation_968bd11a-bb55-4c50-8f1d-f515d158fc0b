// 导出请求封装方法
import http from '@/utils/request'

/**
 * 商品分类列表
 */
export const setCategoryList = (data: any) => {
  return http('get', `/shop-admin/category/list`, data)
}

/**
 * 订单/列表
 */
export const getOrderList = (data: any) => {
  return http('get', `/shop-admin/pdd/order_list`, data)
}

/**
 * 获取拼多多商店列表
 */
export const fetchShopList = (data: any) => {
  return http('get', `/shop-admin/pdd/shop/list`, data)
}

/**
 * 获取淘宝订单列表
 */
export const fetchTbaoOrderList = (data: any) => {
  return http('get', `/shop-admin/tb/order/list`, data)
}
/**
 * 获取jd订单列表
 */
export const getJdOrderList = (data: any) => {
  return http('get', `/shop-admin/jd_order/list`, data)
}
/**
 * 转化明细
 */
export const setTbaoList = (data) => {
  return http('get', `/shop-common/ad/transform_info_v2`, data)
}
