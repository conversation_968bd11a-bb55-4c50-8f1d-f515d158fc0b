<template>
  <a-space direction="vertical" class="w-full">
    <SearchBaseLayout :data="searchData" @changeValue="changeValue" :actions="formConfig" />
    <TableZebraCrossing :data="state.tableConfigOptions" @change="pageChange">
      <template #bodyCell="{ scope }">
        <template v-if="scope.column.key === 'type'">
          <div>{{ scope.record.prod_type == 1 ? 'UDSmart' : 'UDSmart' }}</div>
        </template>
        <template v-if="scope.column.key === 'title'">
          <div class="flex goods_info">
            <div class="img">
              <a-image
                :width="60"
                :preview="false"
                style="width: 60px; height: 60px; border-radius: 6px"
                :src="scope.record.ud_product_url"
              />
            </div>
            <div class="goods_info_data">
              <a-tooltip placement="top">
                <template #title>{{ scope.record.ud_product_name }}</template>

                <span class="goods_info_data_name">
                  <!-- <img
                    :src="scope.record.platform_id == 1 ? state.oceanIcon : state.gdtIcon"
                    class="w-14px h-14px inline-block"
                  /> -->
                  {{ scope.record.ud_product_name }}
                </span>
              </a-tooltip>
              <span class="goods_info_data_number grey-color">所属店铺：{{ scope.record.shop_name }}</span>
              <div class="goods_info_data_number grey-color">商品ID：{{ scope.record.item_id }}</div>
              <span class="goods_info_data_number grey-color">商品金额：{{ scope.record.pay_fee_str }}</span>
            </div>
          </div>
        </template>
        <template v-if="scope.column.key === 'tid'">
          <a-space direction="vertical">
            <span> 订单号：{{ scope.record.order_id }}</span>
          </a-space>
        </template>
        <template v-if="scope.column.key === 'status'">
          <a-space direction="vertical">
            <span>{{ getOrderStatus(scope.record.order_status) }}</span>
            <span :class="[scope.record.callback_status == 1 ? 'primary-color' : 'other-color']">{{
              scope.record.callback_status == 1 ? '已回传' : '未回传'
            }}</span>
          </a-space>
        </template>

        <template v-if="scope.column.key === 'ad_code'">
          <!-- <div class="flex_align_center grey-color">
            <span class=""> 账户ID：</span>

            <span>{{ scope.record.advertiser || '-' }}</span>
            <a-tooltip>
              <template #title
                >广告账户授权异常，为避免报表数据错误，请点击
                <span class="cursor-pointer c-#97DEFF" @click="getAuthUrl(scope.record)">去授权</span></template
              >
              <ExclamationCircleOutlined v-if="scope.record.is_authorized == 1" class="c-#e63030 ml-3px" />
            </a-tooltip>
          </div>
          <div class="flex_align_center grey-color">
            <span class=""> 项目ID：</span>

            <span>{{ scope.record.project || '-' }}</span>
          </div> -->
          <div class="flex_align_center grey-color">
            <a-tooltip placement="topLeft">
              <template #title>{{ channelType(scope.record.platform_id) }}</template>
              <img
                v-if="[1, 4, 5, 6].includes(scope.record.platform_id)"
                class="icon m-l-4px w-14px mr-4px"
                :src="iconType(scope.record.platform_id)"
                alt=""
              />
            </a-tooltip>
            <span class=""> 广告ID：</span>
            <span>{{ scope.record.adgroup_id || '-' }}</span>
          </div>
          <!-- <div class="flex_align_center grey-color">
            <span class=""> 访问日志ID：</span>
            <a-tooltip placement="topLeft">
              <template #title>{{ scope.record.unique_id }}</template>
              <span class="ad_url">
                {{ scope.record.unique_id || '-' }}
              </span>
            </a-tooltip> -->
          <!-- <a-button
              v-if="scope.record?.unique_id"
              class="p-0 ml-5px"
              type="link"
              @click="onShowDialog('detail', scope.record)"
              >查看全部</a-button
            > -->
          <!-- <CopyOutlined v-if="scope.record.unique_id" @click="copy(scope.record.unique_id)" /> -->
        </template>
        <template v-if="scope.column.key === 'source'">
          <div class="flex-col">
            <span>{{ scope.record.is_ad_order == 1 ? '广告流量' : '自然流量' }}</span>
          </div>
        </template>
        <template v-if="scope.column.key === 'admin'">
          <a-space direction="vertical">
            <span>{{ scope.record.admin_name }}</span>
            <!-- <div class="flex_align_center grey-color">
              <span class=""> 公司ID：</span>

              <span>{{ scope.record.company_id || '-' }}</span>
            </div>
            <div class="flex_align_center grey-color">
              <span class=""> 商户ID：</span>
              <span>{{ scope.record.shop_id || '-' }}</span>
            </div> -->
          </a-space>
        </template>
        <template v-if="scope.column.key === 'created_at'">
          <div class="flex-col">
            <span>{{ scope.record.order_create_time || '--' }}</span>
            <span class="">{{ scope.record.order_pay_time || '--' }}</span>
          </div>
        </template>
      </template>
    </TableZebraCrossing>
    <a-modal
      v-model:open="state.dialog.visible"
      :title="state.dialog.title"
      :width="state.dialog.width"
      :footer="null"
      destroyOnClose
    >
      <!-- <DetailList v-if="state.dialog.type === 'detail'" :item="state.item" /> -->
    </a-modal>
  </a-space>
</template>
<script setup lang="ts">
  import dayjs from 'dayjs'
  import { formatDate, copy, requireImg } from '@/utils'
  import { useRoute, useRouter } from 'vue-router'
  import { exportCreate, getShopCascade } from '@/api/common'
  // setOceanAuthUrl
  import { fetchTbaoOrderList, fetchTbShopList } from '../index/index.api'
  import { useTheme, useDownloadCenter } from '@/hooks'
  import { notification } from 'ant-design-vue'
  import { ref, reactive, onMounted, createVNode } from 'vue'
  import datas from './src/tbaoDatas'
  const { goCenter } = useDownloadCenter()
  const { themeVar } = useTheme()
  const { searchData, formConfig, columns } = datas()
  const router = useRouter()
  const route = useRoute()
  const startDate = dayjs().subtract(6, 'day').format('YYYY-MM-DD')
  const endDate = dayjs().format('YYYY-MM-DD')
  const state = reactive({
    initParams: {
      page: 1,
      page_size: 10,
      created_at: `${startDate}_${endDate}`
    },
    tableConfigOptions: {
      bordered: true,
      loading: false,
      rowKey: 'id',
      scroll: {
        scrollToFirstRowOnChange: false,
        x: 1600
      },
      dataSource: [],
      columns: columns,
      pagination: {
        hideOnSinglePage: false,
        showQuickJumper: true,
        showSizeChanger: true,
        total: 0,
        pageSize: 10,
        current: 1,
        size: 'small',
        showTotal: (total) => `共${total}条数据`
      }
    },
    oceanIcon: requireImg('cid/icon_ocean_check.png'),
    gdtIcon: requireImg('cid/icon_gd_check.png'),
    dialog: {
      visible: false,
      title: '',
      width: 0,
      type: ''
    },
    cateGoryList: [],
    selectionItem: [] // 表格选择的Item
  })
  const getOrderStatus = (type) => {
    const status = {
      0: '-',
      1: '拍下',
      2: '支付',
      3: '退款',
      4: '预售',
      5: '收货'
    }
    return status[type]
  }
  const channelType = (type: any) => {
    let status: any = {
      0: '自然流量',
      1: '广点通',
      2: '视频号广告',
      4: '磁力引擎',
      6: '巨量引擎'
    }
    return status[type]
  }
  const iconType = (type: number) => {
    let status: any = {
      // 1: requireImg('order/wx_icon2.png'),
      6: requireImg('order/o6.png'),
      4: requireImg('order/o1.png'),
      1: requireImg('order/o8.png'),
      5: requireImg('order/o5.png')
    }
    return status[type]
  }
  const pageChange = (pagination) => {
    state.initParams.page = pagination.current
    state.initParams.page_size = pagination.pageSize
    state.tableConfigOptions.pagination.pageSize = pagination.pageSize
    getList()
  }
  // 搜索
  const changeValue = (data) => {
    console.log('search 参数', data)
    let shopids: any = []
    let companyids: any = []
    if (data.formData?.shop_id?.length > 0) {
      data.formData?.shop_id.forEach((item) => {
        if (item.length == 1) {
          companyids.push(item[0])
        } else {
          if (companyids.indexOf(item[0]) == -1) {
            companyids.push(item[0])
          }
          if (shopids.indexOf(item[1]) == -1) {
            shopids.push(item[1])
          }
        }
      })
    }
    state.initParams = {
      ...state.initParams,
      ...data.formData,
      shop_ids: shopids.length ? shopids.join(',') : '',
      created_at: data.formData.created_at && data.formData.created_at[0] ? data.formData.created_at.join('_') : ``
    }

    delete state.initParams.shop_id
    if (!data.status) {
      state.initParams.created_at = `${dayjs().subtract(6, 'day').format('YYYY-MM-DD')}_${dayjs().format('YYYY-MM-DD')}`
      data.formData.created_at = [dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
    }
    getList()
  }

  // 订单列表
  const getList = async () => {
    try {
      state.tableConfigOptions.loading = true
      state.initParams.platform_id = state.initParams.platform_id || undefined
      const resp = await fetchTbaoOrderList(state.initParams)
      state.tableConfigOptions.dataSource = resp.data?.list || []
      state.tableConfigOptions.pagination.total = resp.data?.total_num || 0
      state.tableConfigOptions.pagination.current = resp.data?.page || 1
      state.tableConfigOptions.loading = false
    } catch (error) {
      console.error(error)
      state.tableConfigOptions.dataSource = []
      state.tableConfigOptions.loading = false
    }
  }

  // 导出
  const downloadTbao = async () => {
    let params = {
      ...state.initParams
    }
    for (let prop in params) {
      if (params[prop] === null) {
        delete params[prop]
      }
    }
    const result = await exportCreate({
      params: JSON.stringify(params),
      type: 'tb_order_list'
    })
    if (result.code === 0) {
      notification.success({
        message: '导出成功',
        description: '请前往系统->下载中心查看',
        style: {
          color: themeVar.value.primaryColor,
          cursor: 'pointer'
        },
        onClick: () => router.push({ path: '/system/setUp/download' })
      })
    }
  }

  // 店铺列表
  const getShopList = async () => {
    try {
      const res = await fetchTbShopList({})
      searchData.value.find((item) => item.field == 'shop_name').props.options = (res.data || []).map((v) => {
        return {
          value: v.ud_shop_id,
          label: v.ud_shop_name
        }
      })
    } catch (error) {
      console.error(error)
    }
  }
  const initGetShopList = async () => {
    const result = await getShopCascade({ page_size: 1000 })
    if (result.code === 0) {
      if (result?.data?.length) {
        ;(searchData.value || []).forEach((item) => {
          if (item.field === 'shop_id') {
            item.props.options = result.data || []
          }
        })
      }
    }
  }
  initGetShopList()
  getShopList()

  onMounted(() => {
    if (route.query?.order_num) {
      state.initParams.order_num = route.query?.order_num
      searchData.value.find((item) => item.field == 'order_num').value = route.query?.order_num
      getList()
    } else {
      console.log('----------)))')
      getList()
    }
  })
  defineExpose({
    downloadTbao
  })
</script>
<style lang="scss" scoped>
  @import './src/assets/css/mixin_scss_fn';

  .primary-color {
    color: v-bind('themeVar.primaryColor');
  }
  .other-color {
    color: #60a13b;
  }
  .grey-color {
    color: v-bind('themeVar.textColorGray');
  }
  .ad_url {
    overflow: hidden; //多出的隐藏
    text-overflow: ellipsis; //多出部分用...代替
    display: -webkit-box; //定义为盒子模型显示
    -webkit-line-clamp: 1; //用来限制在一个块元素显示的文本的行数
    -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
    font-size: 14px;
    width: 116px;
    cursor: pointer;
  }
  .round {
    width: 6px;
    height: 6px;
    margin-right: 4px;
    border-radius: 50%;
  }
  .item-error {
    color: #0080ff;
    border: 1px solid #0080ff;
  }
  .goods_info_data_remak {
    overflow: hidden; //多出的隐藏
    text-overflow: ellipsis; //多出部分用...代替
    display: -webkit-box; //定义为盒子模型显示
    -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
    -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
    font-size: 14px;
  }
  .goods_info {
    .img_item {
      width: 60px;
      height: 60px;
      background: #bec6d6;
      border-radius: 6px;
    }

    p {
      margin: 0;
    }

    .goods_info_data {
      margin-left: 10px;
      font-family: PingFang SC;
      font-weight: 400;
    }
  }
  .goods_info_data_name {
    overflow: hidden; //多出的隐藏
    text-overflow: ellipsis; //多出部分用...代替
    display: -webkit-box; //定义为盒子模型显示
    -webkit-line-clamp: 2; //用来限制在一个块元素显示的文本的行数
    -webkit-box-orient: vertical; //从上到下垂直排列子元素（设置伸缩盒子的子元素排列方式）
    color: var(--primary-color);
    cursor: pointer;
  }
</style>
