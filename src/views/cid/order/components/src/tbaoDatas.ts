import { reactive, ref } from 'vue'
import dayjs from 'dayjs'
import { Cascader } from 'ant-design-vue'
export default function datas() {
  const callbackStatus = reactive([
    {
      label: '已回传',
      value: 1
    },
    {
      label: '未回传',
      value: 2
    }
  ])

  const searchData = ref([
    {
      type: 'select',
      field: 'platform_id',
      value: null,
      props: {
        placeholder: '请选择广告渠道',
        options: [
          {
            label: '全部',
            value: 0
          },
          {
            label: '广点通',
            value: 1
          },
          // {
          //   label: '磁力',
          //   value: 4
          // },
          {
            label: '巨量',
            value: 6
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'input.text',
      field: 'product',
      value: undefined,
      props: {
        placeholder: '请输入商品名称/商品ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'input.text',
      field: 'order_num',
      value: undefined,
      props: {
        placeholder: '请输入订单编号'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'select',
      field: 'callback_status',
      value: null,
      props: {
        placeholder: '是否回传',
        options: callbackStatus
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'select',
      field: 'shop_name',
      value: undefined,
      props: {
        placeholder: '请选择淘宝店铺',//拼多多店铺
        options: []
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'cascader',
      field: 'shop_id',
      useConfig: true,
      props: {
        placeholder: '请选择店铺名称',
        options: [],
        multiple: true,
        showCheckedStrategy: Cascader.SHOW_CHILD,
        fieldNames: {
          label: 'name',
          value: 'id'
        },
      },
      value: [],
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'select',
      field: 'order_source',
      value: null,
      props: {
        placeholder: '请选择订单来源',
        options: [
          {
            label: '自然流量',
            value: 1
          },
          {
            label: '广告流量',
            value: 2
          }
        ]
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'input.text',
      field: 'ad_id',
      value: undefined,
      props: {
        placeholder: '请输入广告ID'
      },
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    },
    {
      type: 'date',
      field: 'created_at',
      value: [dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      layout: {
        xs: 24,
        sm: 12,
        md: 8,
        lg: 8,
        xl: 8,
        xxl: 6
      }
    }
  ])

  const formConfig = reactive({
    foldNum: 0,
    layout: {
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 8,
      xxl: 6
    }
  })
  const columns = reactive([
    {
      title: '商品类型',
      dataIndex: 'type',
      key: 'type',
      fixed: 'left',
      width: 100
    },
    {
      title: '店铺名称',
      dataIndex: 'main_shop_name',
      key: 'main_shop_name',
      fixed: 'left',
      width: 140
    },
    {
      title: '商品信息',
      dataIndex: 'title',
      key: 'title',
      fixed: 'left',
      width: 260
    },
    {
      title: '实付金额（元）',
      dataIndex: 'actual_pay_fee_str',
      key: 'actual_pay_fee_str',
      width: 140
    },
    {
      title: '订单号',
      dataIndex: 'tid',
      key: 'tid',
      width: 200
    },

    {
      title: '广告信息',
      dataIndex: 'ad_code',
      key: 'ad_code',
      width: 240
    },
    {
      title: '订单/回传状态',
      dataIndex: 'status',
      key: 'status',
      width: 180
    },
    {
      title: '订单来源',
      dataIndex: 'source',
      key: 'source',
      width: 100
    },
    {
      title: '负责人',
      dataIndex: 'admin',
      key: 'admin',
      width: 120
    },

    {
      title: '创建/支付时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160
    }
  ])

  return { searchData, formConfig, columns, callbackStatus }
}
