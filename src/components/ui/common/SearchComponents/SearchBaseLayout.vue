<template>
  <a-row :gutter="[16, 16]" class="search_base_layout">
    <template v-for="(item, index) in data" :key="item.field">
      <a-col v-if="setMore(index, item)" v-bind="item.layout" class="date_container">
        <slot v-if="item.type === 'input.text'" :name="item.field">
          <a-input
            v-model:value="formData[item.field]"
            @pressEnter="changeFormValue(true)"
            @blur="changeFormValue(true)"
            allowClear
            v-bind="item.props"
          />
        </slot>
        <slot v-if="item.type === 'input.phone'" :name="item.field">
          <a-input
            v-model:value.trim="formData[item.field]"
            @pressEnter="changeFormValue(true)"
            @blur="changeFormValue(true)"
            allowClear
            v-bind="item.props"
            :maxlength="20"
          />
        </slot>
        <slot v-else-if="item.type === 'select'" :name="item.field">
          <a-select
            v-model:value="formData[item.field]"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            @change="changeFormValue(true)"
            allowClear
            class="w-full"
            :show-search="true"
            :filterOption="onFiterOption"
            v-bind="item.props"
          ></a-select>
        </slot>
        <slot v-else-if="item.type === 'goods'" :name="item.field">
          <a-select
            v-model:value="formData[item.field]"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            @change="changeFormValue(true)"
            allowClear
            :options="goodsList"
            :fieldNames="{ value: 'unique_id', label: 'goods_name' }"
            class="w-full"
            :show-search="true"
            :filter-option="false"
            v-bind="item.props"
            @search="fetchList"
          >
            <template v-if="state.fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
          </a-select>
        </slot>
        <slot v-else-if="item.type === 'plan_goods'" :name="item.field">
          <a-select
            v-model:value="formData[item.field]"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            @change="changeFormValue(true)"
            allowClear
            :options="planGoodsList"
            :fieldNames="{ value: 'id', label: 'title' }"
            class="w-full"
            :show-search="true"
            :filter-option="false"
            v-bind="item.props"
            @search="fetchPlanGoodsList"
          >
            <template v-if="state.goods_fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
          </a-select>
        </slot>
        <slot v-else-if="item.type === 'account'" :name="item.field">
          <a-select
            v-model:value="formData[item.field]"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            @change="changeFormValue(true)"
            allowClear
            :options="adAccountList"
            :fieldNames="{ value: 'unique_id', label: 'ad_account_name' }"
            class="w-full"
            :show-search="true"
            :filter-option="false"
            v-bind="item.props"
            @search="fetchAccountList"
          >
            <template v-if="state.account_fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
          </a-select>
        </slot>
        <slot v-else-if="item.type === 'date'" :name="item.field">
          <a-range-picker
            allowClear
            v-model:value="formData[item.field]"
            valueFormat="YYYY-MM-DD"
            class="w-full"
            :presets="getDatePresetsOptions(item)"
            @calendarChange="onCalendarChange"
            @openChange="onOpenChange"
            @change="
              (_: any, value: any) => {
                changeFormValue(true)
                ;(formData as any)[item.field] = value
              }
            "
            v-bind="item.props"
          />
        </slot>
        <slot v-else-if="item.type === 'datetime'" :name="item.field">
          <a-range-picker
            :allowClear="showDateClear"
            v-model:value="formData[item.field]"
            valueFormat="YYYY-MM-DD HH:mm:ss"
            class="w-full"
            :presets="getDatePresetsOptions(item)"
            @calendarChange="onCalendarChange"
            @openChange="
              (_: any, value: any) => {
                changeFormValue(true)
                // 格式化为 'YYYY-MM-DD HH:mm:ss'
                ;(formData as any)[item.field] = value.map((date) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'))
              }
            "
            v-bind="item.props"
            :showTime="true"
          />
        </slot>
        <slot v-else-if="item.type === 'number.range'" :name="item.field">
          <RangeNumber v-model="formData[item.field]" v-bind="item.props" @change="changeFormValue(true)" />
        </slot>
        <slot v-else-if="item.type === 'cascader'" :name="item.field">
          <a-cascader
            popupClassName="edit_cascader_wrapper"
            :open="showCascader[item.field]"
            v-model:value="formData[item.field]"
            :allow-clear="true"
            class="w-full"
            maxTagCount="responsive"
            :show-search="true"
            :filterOption="onFiterOption"
            :expandTrigger="expandTriggerFilter(item.props)"
            @change="changeFormValue(true, item.props)"
            @dropdownVisibleChange="onDropdownVisibleChange($event, item)"
            v-bind="item.props"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
          />
        </slot>
        <slot v-else-if="item.type === 'select_id'" :name="item.field">
          <div class="joint_date">
            <a-space class="w-full">
              <a-space-compact block>
                <a-select
                  :style="{ width: '42%' }"
                  :getPopupContainer="(triggerNode: any) => triggerNode?.parentNode?.parentNode"
                  :options="item.props.options"
                  @change="(e) => onTypeChange(e, item.props.options)"
                  v-model:value="item.field"
                  :showArrow="true"
                >
                </a-select>
                <a-input
                  :style="{ width: '100%' }"
                  v-model:value="formData[item.field]"
                  @pressEnter="changeFormValue(true)"
                  @blur="changeFormValue(true)"
                  allowClear
                  v-bind="item.props"
                />
              </a-space-compact>
            </a-space>
          </div>
        </slot>
        <slot v-else-if="item.type === 'joint_date'" :name="item.field">
          <div class="joint_date">
            <a-space class="w-full">
              <a-space-compact block>
                <a-select
                  v-if="!item.props?.leftContent?.field"
                  :style="{ width: '40%' }"
                  :getPopupContainer="(triggerNode: any) => triggerNode?.parentNode?.parentNode"
                  allowClear
                  placeholder="时间类型"
                  :options="item.props.options"
                  @change="(e) => dateTypeChange(e)"
                  v-model:value="item.field"
                  :showArrow="true"
                >
                </a-select>
                <a-select
                  v-else
                  :style="{ width: '40%' }"
                  :getPopupContainer="(triggerNode: any) => triggerNode?.parentNode?.parentNode"
                  :allowClear="item.props.leftAllowClear"
                  placeholder="时间类型"
                  :options="item.props.options"
                  @change="(e) => dateTypeChange(e, item)"
                  v-model:value="item.props.leftContent.value"
                  :showArrow="true"
                >
                </a-select>
                <a-range-picker
                  :style="{ width: '100%' }"
                  :allowClear="showDateClear"
                  v-model:value="formData[item.field]"
                  valueFormat="YYYY-MM-DD"
                  :presets="getDatePresetsOptions(item)"
                  @calendarChange="onCalendarChange"
                  @openChange="onOpenChange"
                  @change="
                    (_: any, value: any) => {
                      changeFormValue(true)
                      // 格式化为 'YYYY-MM-DD HH:mm:ss'
                      ;(formData as any)[item.field] = value.map((date) =>
                        date ? dayjs(date).format('YYYY-MM-DD') : undefined
                      )
                    }
                  "
                  :showTime="true"
                  v-bind="item.props"
                />
              </a-space-compact>
            </a-space>
          </div>
        </slot>
        <slot v-else-if="item.type === 'admin'">
          <a-cascader
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            style="width: 100%"
            v-model:value="formData[item.field]"
            multiple
            :allowClear="true"
            maxTagCount="responsive"
            :options="userList"
            :show-search="true"
            :filterOption="onFiterAdminOption"
            :show-checked-strategy="Cascader.SHOW_CHILD"
            placeholder="请选择负责人"
            @change="changeFormValue(true)"
            :fieldNames="{ value: 'id', label: 'name', children: 'user_list' }"
            v-bind="item.props"
          >
          </a-cascader>
        </slot>

        <slot v-else-if="item.type === 'company'" :name="item.field">
          <a-select
            v-model:value="formData[item.field]"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            :fieldNames="{ value: 'id', label: 'name' }"
            @change="changeFormValue(true)"
            :options="userList"
            :allowClear="true"
            class="w-full"
            :show-search="true"
            :filterOption="onFiterAdminOption"
            v-bind="item.props"
          ></a-select>
        </slot>
        <slot v-else-if="item.type === 'shop'" :name="item.field">
          <a-select
            v-model:value="formData[item.field]"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            @change="changeFormValue(true)"
            :options="shopList"
            :allowClear="true"
            class="w-full"
            :show-search="true"
            :filterOption="onFiterOption"
            v-bind="item.props"
          ></a-select>
        </slot>

        <slot v-else-if="item.type === 'department'">
          <a-tree-select
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            v-model:value="formData[item.field]"
            @change="changeFormValue(true)"
            :tree-data="departmentList"
            tree-node-filter-prop="name"
            :popupClassName="'department'"
            allow-clear
            show-search
            style="width: 100%"
            :fieldNames="{ label: 'name', value: 'id' }"
            :show-checked-strategy="SHOW_PARENT"
            v-bind="item.props"
          />
        </slot>

        <slot v-else-if="item.type === 'channel_id'" :name="item.field">
          <a-cascader
            maxTagCount="responsive"
            :getPopupContainer="(triggerNode: any) => triggerNode.parentNode"
            v-model:value="formData[item.field]"
            :allow-clear="true"
            class="w-full"
            multiple
            placeholder="请选择商户拓展"
            :options="channelList"
            :fieldNames="{
              label: 'channel_name',
              value: 'id',
              children: 'children'
            }"
            :show-search="true"
            :show-checked-strategy="Cascader.SHOW_CHILD"
            :expandTrigger="expandTriggerFilter(item.props)"
            @change="changeFormValue(true, item.props)"
            @dropdownVisibleChange="onDropdownVisibleChange($event, props)"
            v-bind="item.props"
          />
        </slot>
      </a-col>
    </template>
    <a-col v-if="isBtn" v-bind="actions.layout">
      <a-space align="center" :size="[16, 0]">
        <a-button type="primary" @click="changeFormValue(true)">查询</a-button>
        <a-button @click="changeFormValue(false)">重置</a-button>
        <a-button type="link" class="pa-0" @click="changeFormValue(true, 'export')" v-if="btnNames.includes('export')"
          >导出</a-button
        >
        <a-button type="link" class="pa-0" @click="changeFormValue(true, 'refresh')" v-if="btnNames.includes('refresh')"
          >刷新</a-button
        >
        <div v-if="actions.foldNum !== 0 && actions.foldNum < data.length">
          <a-button class="p-0" type="link" @click="showConfigFold" v-if="props.useConfig">{{
            isShowConfig ? '收起' : '展开'
          }}</a-button>
          <a-button class="p-0" type="link" @click="showFold(foldNum <= actions.foldNum)" v-else>
            {{ foldNum <= actions.foldNum ? '展开' : '收起' }}
          </a-button>
          <RightOutlined style="color: #737ba6" />
        </div>
      </a-space>
    </a-col>
    <slot name="btns"></slot>
  </a-row>
</template>

<script setup lang="ts">
  import {
    getUserListApi,
    get_department_info,
    getChannelListApi,
    get_search_goods_list,
    get_search_advertiser_list,
    getShopListApi
  } from '@/api/common'
  import { setProductList } from '@/views/shop/goods/list/index.api'
  import { getDatePresetsOptions, checkMobilePermission } from '@/utils'
  import { RightOutlined } from '@ant-design/icons-vue'
  import { Cascader } from 'ant-design-vue'
  import { isObject, throttle, debounce } from 'lodash-es'
  import { defineEmits, onMounted, ref, nextTick, watch } from 'vue'
  import { Dayjs } from 'dayjs'
  import dayjs from 'dayjs'
  type RangeValue = [Dayjs, Dayjs]
  const dates = ref<RangeValue>()
  const formData = ref({})
  const props = defineProps({
    data: {
      type: Object,
      default: () => []
    },
    actions: {
      type: Object,
      default: {}
    },
    btnNames: {
      type: Array,
      default: []
    },
    isBtn: {
      type: Boolean,
      default: true
    },
    channel_ids: {
      type: String,
      default: ''
    },
    showDateClear: {
      type: Boolean,
      default: true
    },
    companyName: {
      type: String,
      default: ''
    },
    // 是否使用配置项控制筛选条件显隐
    useConfig: Boolean,
    checkPermission: {
      type: String,
      default: ''
    }
  })
  watch(
    () => props.companyName,
    (newVal) => {
      props.companyName = newVal
      formData.value.name = props.companyName
      console.log('最后', props.companyName, formData.value.name)
    },
    { deep: true, immediate: true }
  )
  const emits = defineEmits(['changeValue', 'selectedDate', 'onOpenChange'])
  const foldNum = ref(props.actions.foldNum)

  // watch(
  //   () => props.data,
  //   () => {
  //     const initFormData = {}
  //     props.data.forEach((item: { field: string | number; value: any }) => {
  //       ;(initFormData as any)[item.field] = item.value
  //     })
  //     formData.value = initFormData
  //   },
  //   {
  //     immediate: true,
  //     deep: true
  //   }
  // )
  onMounted(() => {
    const initFormData = {}
    props.data.forEach((item: { field: string | number; value: any }) => {
      ;(initFormData as any)[item.field] = item.value
    })

    formData.value = initFormData
    // 处理商户拓展页面跳转参数回显
    if (props.channel_ids) {
      formData.value.channel_ids = props.channel_ids
      formData.value.channel_ids = (formData.value?.channel_ids?.split(',') || []).map((v) => {
        return +v
      })
    }
    if (props.useConfig) {
      // 使用配置项控制显隐
      foldNum.value = props.data.length
    }
  })

  // 手动控制联机选择器的显隐
  const showCascader = ref<Record<string, boolean>>({})

  /**
   * 初始化显示隐藏级联菜单
   */
  const initShowCascader = () => {
    if (Array.isArray(props.data)) {
      props?.data?.forEach((item) => {
        if (item.type == 'cascader' || item.type == 'channel_id') {
          showCascader.value[item.field] = false
        }
      })
    }
  }
  initShowCascader()

  // 获取报表商品列表
  let goodsList = ref([])
  const state = ref({
    fetching: false,
    account_fetching: false,
    goods_fetching: false
  })
  const fetchList = debounce(async (value) => {
    try {
      state.value.fetching = true
      const params = {
        page: 1,
        page_size: 100,
        goods_name: value,
        source: formData.value.product_type || 0
      }
      if (!value.trim()) {
        goodsList.value = []
        state.value.fetching = false
        return
      }
      let res = await get_search_goods_list(params)
      goodsList.value = res?.data?.list || []
      state.value.fetching = false
    } catch (error) {
      state.value.fetching = false
    }
    console.log(formData.value, 'list')
  }, 300)

  // 获取报表账户列表
  let adAccountList = ref([])

  const fetchAccountList = debounce(async (value) => {
    try {
      state.value.account_fetching = true
      const params = {
        page: 1,
        page_size: 100,
        ad_account_name: value,
        media_type: formData.value.media_type || 0
      }
      if (!value.trim()) {
        adAccountList.value = []
        state.value.account_fetching = false
        return
      }
      let res = await get_search_advertiser_list(params)
      adAccountList.value = res?.data?.list || []
      state.value.account_fetching = false
    } catch (error) {
      state.value.account_fetching = false
    }
    console.log(formData.value, 'list')
  }, 300)

  // 获取商品列表
  let planGoodsList = ref([])

  const fetchPlanGoodsList = debounce(async (value) => {
    try {
      state.value.goods_fetching = true
      const params = {
        page: 1,
        page_size: 100,
        name_code: value
      }

      if (!value.trim()) {
        planGoodsList.value = []
        state.value.goods_fetching = false
        return
      }
      let res = await setProductList(params)
      planGoodsList.value = res?.data?.list || []
      state.value.goods_fetching = false
    } catch (error) {
      state.value.goods_fetching = false
    }
    console.log(formData.value, 'goodslist')
  }, 300)

  // select搜索
  const onFiterOption = (value: any, option: any) => {
    if (option.label.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }
  // 负责人搜索
  const onFiterAdminOption = (value: any, option: any) => {
    if (option.name.indexOf(value) != -1) {
      return true
    } else {
      return false
    }
  }

  // 控制是否配置项显隐
  const isShowConfig = ref(false)
  const showConfigFold = () => {
    isShowConfig.value = !isShowConfig.value
  }

  const showFold = (status: any) => {
    foldNum.value = status ? props.data.length : props.actions.foldNum
  }

  // 判断是否是多维数组
  function isMultiDimensionalArray(arr: any) {
    return Array.isArray(arr) && arr.some((item) => Array.isArray(item))
  }
  // 选择时间筛选类型
  const dateTypeChange = (e: any, item?: any) => {
    console.log('e', e)
    const initFormData = formData.value
    let time = ['created_at', 'deliver_time', 'success_at', 'pay_time', 'finish_time']
    props.data.forEach((item: { field: string | number; value: any; type: any; props?: any }) => {
      if (item.type == 'joint_date') {
        if (item?.props?.leftContent?.field) {
          initFormData[item.props.leftContent.field] = e
          // initFormData[item.field] = undefined
        } else {
          item.field = e
          let timeKey = ''
          time.forEach((i) => {
            if ((initFormData as any)[i] && (initFormData as any)[i].length > 0) {
              timeKey = i
            }
          })
          ;(initFormData as any)[item.field] = (initFormData as any)[timeKey]
          ;(initFormData as any)[timeKey] = []
        }
      }
    })
    formData.value = initFormData
    if (
      item?.props?.leftContent?.field &&
      formData.value[item.field]?.length &&
      formData.value[item.field]?.every((it) => it)
    )
      changeFormValue(true)
  }
  const onTypeChange = (e, options) => {
    console.log('onTypeChange', e, options)
    const initFormData = { ...formData.value }
    const optionsList = options.map((item) => item.value)
    optionsList.forEach((type) => {
      if (type === e) {
        initFormData[type] = initFormData ? initFormData[type] : undefined
      } else {
        initFormData[type] = undefined
      }
    })
    formData.value = initFormData
    console.log('formData', formData)
  }
  let admin_ids = ref(null)
  const changeFormValue = throttle(async (status: any, type = undefined) => {
    // 处理负责人传参
    if (formData.value?.admin_ids && isMultiDimensionalArray(formData.value?.admin_ids)) {
      admin_ids = formData.value?.admin_ids.map((v) => {
        return v[1]
      })
      admin_ids = admin_ids.join(',')
    } else {
      admin_ids = formData.value?.admin_ids ? formData.value?.admin_ids[1] : undefined
    }

    // 处理商户拓展传参
    let channel_ids = ref(null)
    if (formData.value?.channel_ids && isMultiDimensionalArray(formData.value?.channel_ids)) {
      channel_ids = formData.value?.channel_ids.map((v) => {
        return v
      })
      channel_ids = channel_ids.join(',')
    } else {
      channel_ids = formData.value?.channel_ids?.length > 0 ? formData.value?.channel_ids : undefined
    }

    if (type && isObject(type)) {
      if (type.changeOnSelect) showCascader.value[type.field] = false
    }

    if (status && type == undefined) {
      if (!formData.value.account_id) adAccountList.value = []
      if (!formData.value.product_id) goodsList.value = []

      emits('changeValue', { status, formData: { ...formData.value, admin_ids: admin_ids, channel_ids, page: 1 } })
    } else if (status && type === 'refresh') {
      emits('changeValue', { status, formData: { ...formData.value, admin_ids: admin_ids, channel_ids }, type })
    } else if (status && type === 'export') {
      if (props.checkPermission) {
        // 没有对应权限且点击了取消
        if ((await checkMobilePermission(props.checkPermission)) == 1) return
      }
      emits('changeValue', { status, formData: { ...formData.value, admin_ids: admin_ids, channel_ids }, type })
    } else {
      goodsList.value = []
      adAccountList.value = []
      const initFormData = {}
      props.data.forEach((item: { field: string | number; value: any }) => {
        if (status) {
          ;(initFormData as any)[item.field] = formData.value[item.field]
        } else {
          ;(initFormData as any)[item.field] = item.value
        }
      })
      formData.value = initFormData
      emits('changeValue', { status, formData: formData.value })
    }
  }, 1000)

  // 联机选择器的展开方式
  const expandTriggerFilter = (props: any) => {
    if (!props) return 'click'
    if (props.expandTrigger) return props.expandTrigger
    if (props.changeOnSelect) return 'hover'
    if (!props.changeOnSelect) return 'click'
    return 'click'
  }

  // 联机选择器的显示隐藏
  const onDropdownVisibleChange = (visible: boolean, item: any) => {
    console.log('onDropdownVisibleChange', visible)
    showCascader.value[item.field] = visible
  }

  const setMore = (index: any, item: any) => {
    if (props.useConfig) {
      if (!isShowConfig.value && item.useConfig) {
        return false
      }
      return true
    } else {
      if (props.actions && props.actions.foldNum) {
        return ++index <= foldNum.value
      } else {
        return true
      }
    }
  }

  const onCalendarChange = (val: RangeValue) => {
    dates.value = val
    emits('selectedDate', dates.value)
  }
  const onOpenChange = (open: boolean) => {
    emits('onOpenChange', open)
  }

  // 获取负责人列表
  let userList = ref([])
  let companyList = ref([])
  const getUserList = async () => {
    try {
      let res = await getUserListApi()
      userList.value = res?.data || []
    } catch (error) {}
  }

  // 渠道拓展数据
  const channelList = ref([])
  const getChannelList = async () => {
    try {
      let res = await getChannelListApi()
      channelList.value = res?.data || []
    } catch (error) {}
  }

  // 获取店铺列表
  const shopList = ref([])
  const getShopList = async () => {
    try {
      let { data } = await getShopListApi({ page: 1, page_size: 9999 })
      shopList.value =
        data?.list.map((item: any) => {
          return {
            value: item.shop_info.id,
            label: item.shop_info.name
          }
        }) || []
    } catch (error) {}
  }
  // 获取部门列表
  const departmentList = ref([])
  const getdepartmentList = async () => {
    try {
      let res = await get_department_info({})
      departmentList.value = res.data || []
    } catch (error) {}
  }

  onMounted(() => {
    // 如果数据里面有负责人的话请求负责人接口
    if ((props.data || []).some((v: any) => v.type == 'admin' || v.type == 'company')) {
      getUserList()
    }
    // 如果数据里面有部门数据，请求部门接口
    if ((props.data || []).some((v: any) => v.type == 'department')) {
      getdepartmentList()
    }
    // 如果数据里面有商户拓展，请求商户拓展数据接口
    if ((props.data || []).some((v: any) => v.type == 'channel_id')) {
      getChannelList()
    }
    // 如果数据里面有店铺，请求店铺接口
    if ((props.data || []).some((v: any) => v.type == 'shop')) {
      getShopList()
    }
    const initFormData = {}
    props.data.forEach((item: { field: string | number; value: any; type?: any; props?: any }) => {
      ;(initFormData as any)[item.field] = item.value
      if (item.type == 'joint_date' && item?.props?.leftContent?.field) {
        ;(initFormData as any)[item.props.leftContent.field] = item.props.leftContent.value
      }
    })
    formData.value = initFormData
  })

  // 向外暴露筛选数据
  const getFormData = () => {
    return {
      ...formData.value
    }
  }

  // 向外暴露方法
  defineExpose({ getFormData, changeFormValue, formData, getChannelList })
</script>

<style lang="scss">
  .ant-tree-select-dropdown .ant-select-tree .ant-select-tree-treenode {
    width: 100%;
  }
  .ant-tree-select-dropdown
    .ant-select-tree-list-holder-inner
    .ant-select-tree-treenode
    .ant-select-tree-node-content-wrapper {
    width: 100%;
  }
  .ant-btn-link {
    color: #647dff;
  }
  .ant-btn-link:hover {
    color: #8ca3ff !important;
  }
  .edit_cascader_wrapper {
    .ant-cascader-menu-item {
      max-width: 250px;
      .ant-cascader-menu-item-content {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        max-width: 240px;
        white-space: normal;
      }
    }
  }
  .joint_date {
    .ant-space-item {
      width: 100%;
      height: 34px;
      .ant-space-compact-block {
        height: 100%;
      }
      .ant-select {
        height: 100%;
        z-index: auto;
      }
      .ant-picker {
        height: 100%;
      }
    }
  }
</style>
