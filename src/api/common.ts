// 导出请求封装方法
import http from '@/utils/request'
import { APPLET } from './controller'

// 获取账户信息
export const getUserInfo = (data?: any) => {
  return http('get', `/public/user/info`, data)
}

// 获取账户权限信息
export const getUserAuth = (data?: any) => {
  return http('get', `/public/user/permission`, data)
}

// 获取cos临时密钥
export const getCosAuth = (data?: any) => {
  return http('get', `/public/file/credential`, data)
}
/**
 * 获取用户菜单
 */
export const getRoleMenuList = (params?: any) => {
  return http('get', `/public/role/role-menu`, params)
}

/**
 * 获取店铺列表
 * https://www.apifox.cn/web/project/2014698/apis/api-63590656
 */
export const getShopListApi = (data?: any) => {
  return http('get', `/manage/shop/list`, data)
}
// 获取素材库数据
export const getFileList = (data?: any) => {
  return http('get', `/public/file/list`, data)
}
/**
 * 编辑素材库文件名称
 */
export const updateNameFile = (data?: any) => {
  return http('post', `/public/file/update-name`, data)
}
/**
 * 删除素材库文件
 */
export const deleteFile = (data?: any) => {
  return http('post', `/public/file/delete`, data)
}

// 上传素材库图片
export const fileSave = (data?: any) => {
  return http('post', `/public/file/save`, data)
}
// 素材库文件/修改文件或分组名称
export const fileEdit = (data?: any) => {
  return http('post', `/public/file/edit`, data)
}

/**
 * 重置token
 * @param {String} shop_id 店铺id
 * @returns
 */
export const cutShopUpdateTokenApi = (shop_id: string) => {
  return http('post', `/public/token/reset`, {
    shop_id
  })
}

/**
 * 导出
 * @param {String} type :  order-> 订单导出  params： 搜索条件 转为json字符串
 * @returns
 */
export const exportCreate = (data?: any) => {
  return http('post', `/public/export-data/create`, data)
}

/**
 * 客服信息
 */
export const kefuInfo = (data?: any) => {
  return http('get', `/public/kefu/info`, data)
}

/**
 * 消息已读
 */
export const msgUpdateApi = (data?: any) => {
  return http('post', `/public/msg/update`, data)
}

/**
 * ocr识别
 * https://www.apifox.cn/link/project/2014698/apis/api-66580697
 */
export const setOcrInfo = (data) => {
  return http('get', `/public/ocr/info`, data)
}

/**
 * 投诉查询菜单数字展示
 * https://www.apifox.cn/link/project/2014698/apis/api-66580697
 */
export const pendingCount = () => {
  return http('get', `/manage/merchant-complaints/pending-count`)
}

/**
 * 负责人数据
 */
export const getUserListApi = () => {
  return http('get', `/manage/company/user`)
}
/**
 * erp列表
 */
export const authList = (data) => {
  return http('post', `/shop-erp/auth-list`, data)
}

/**
 * 部门列表数据
 */
export const get_department_info = (data) => {
  return http('get', `/public/department/info`, data)
}
export const exportdata_create = (data) => {
  return http('post', `/public/export-data/create`, data)
}

/**
 * 获取商户渠道数据
 */
export const getChannelListApi = () => {
  return http('get', `/manage/channel/company-channel`)
}

/**
 * 获取报表商品搜索列表
 */
export const get_search_goods_list = (data: any) => {
  return http('get', `/shop-boss/goods_log/search_list`, data)
}

/**
 * 获取报表商品搜索列表
 */
export const get_search_advertiser_list = (data: any) => {
  return http('get', `/shop-boss/advertiserLog/list`, data)
}
/**
 * 获取二维码
 */
export const getGoogleSecretApi = (data: any) => {
  return http('get', `/manage/user/get-google-secret`, data)
}

/**
 *绑定谷歌令牌
 */
export const bindGoogleSecretApi = (data: any) => {
  return http('post', `/manage/user/bind-google-auth`, data)
}

// 保存和编辑标题排序
export const save_report_column = (data: any) => {
  return http('post', `/manage/user-report-sort/add`, data)
}
// 获取标题排序列表
export const get_report_column_list = (data) => {
  return http('get', `/manage/user-report-sort/list`, data)
}
//查看手机号
export const change_phone = (data) => {
  return http('get', `/manage/order/get-phone`, data)
}

// 公司店铺级联列表
export const getShopCascade = (data?: any) => {
  return http('get', `/shop-boss/shop/cascade`, data)
}

/**
 * 商户号列表
 */
export const getSubMchListApi = (data: any) => {
  return http('get', `/manage/shop/sub-mch-list`, data)
}
// 黑名单手机号解密
export const get_phone = (data) => {
  return http('get', `/manage/member-blacklist/get_phone`, data)
}

//平台介入查看手机号
export const platformInterveneGetPhone = (data: any) => {
  return http('post', `/manage/after-sale-review/get-phone-info`, data)
}
/**
 * 获取主体列表
 */
export const getMiniProgramEntityList = (data?: any) => {
  return http('get', `/manage/mini-program-entity/list`, data)
}

/**
 * 审核数据汇总
 */
export const getCheckCount = (data?: any) => {
  return http('get', `/manage/examine/examine-count`, data)
}