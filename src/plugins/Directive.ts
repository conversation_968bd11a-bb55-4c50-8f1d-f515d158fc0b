import { Directive, nextTick } from 'vue'
import loading from './loading'
import { useAuth } from '@/hooks'

export const focus = {
  mounted: (el, binding, vnode) => {
    el.focus()
  }
}

/**
 * 自定义点击当前区域外部触发事件
 */
export const clickOutside: Directive = {
  mounted(el, binding) {
    function eventHandler(e) {
      if (el.contains(e.target)) {
        return false
      }
      // 如果绑定的参数是函数，正常情况也应该是函数，执行
      if (binding.value && typeof binding.value === 'function') {
        binding.value(e)
      }
    }
    // 用于销毁前注销事件监听
    el.__click_outside__ = eventHandler
    // 添加事件监听
    document.addEventListener('click', eventHandler)
  },
  beforeUnmount(el) {
    // 移除事件监听
    document.removeEventListener('click', el.__click_outside__)
    // 删除无用属性
    delete el.__click_outside__
  }
}

/**
 * 自定义按钮权限
 */
export const auth: Directive = {
  mounted(el, binding) {
    const { isAuth } = useAuth()
    if (!isAuth(binding.value) && el) {
      const events = ['click', 'mouseover', 'mouseout', 'keydown', 'keyup']
      events.forEach((eventType) => {
        el.removeEventListener(eventType, () => { })
      })
      // el.innerHTML = ''
      el.setAttribute('style', 'pointer-events: none !important; display: none !important;')
      // 这个是为了解决 a-space会自动添加一个div包裹，导致页面有个空余间距
      let parentNode = el.parentNode
      if (parentNode && parentNode.className.includes('ant-space-item') && parentNode.childNodes.length == 1) {
        parentNode.setAttribute('style', 'pointer-events: none !important; display: none !important;')
      }
    }
  }
}

export default {
  focus,
  clickOutside,
  loading,
  auth
}
