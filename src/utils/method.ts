import { message, Modal } from 'ant-design-vue'
import { isObject } from 'lodash-es'
import moment from 'moment'
import useClipboard from 'vue-clipboard3'
import { getConfig } from './common'
import { useAuth } from '@/hooks'

const { toClipboard } = useClipboard()

/**
 * 将小驼峰转化为中划线模式
 * @param str
 * @returns
 */
export function camelCaseToDash(str: string): string {
  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
}
/**
 * 将中划线转为小驼峰模式
 * @param str
 * @returns
 */
export function dashToCamelCase(str: string): string {
  return str.replace(/-([a-z])/g, (match, char) => char.toUpperCase())
}
/**
 * 获取字符串数组重复部分
 * @param { string[] } arr
 * @returns
 */
export function findDuplicates(arr: string[]) {
  const duplicates = []
  const seen: any = {}

  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    if (seen[item] !== undefined) {
      if (seen[item] === 1) {
        duplicates.push(item)
      }
      seen[item] = seen[item] + 1
    } else {
      seen[item] = 1
    }
  }

  return duplicates
}

/**
 * 延迟多久返回
 * @param ms 毫秒
 * @returns
 */
export function delay(ms: number) {
  return new Promise<void>((reslove) => {
    setTimeout(() => {
      reslove()
    }, ms || 500)
  })
}
/**
 * 判断是图片还是视频格式
 * @param {string} url
 * @returns {Object}
 */
export function checkFileFormat(fileValue: string, type: string | undefined) {
  var index = fileValue.lastIndexOf('.')
  const fileValueSuffix = fileValue.substring(index) // 截断"."之前的，得到后缀
  // /(.*)\.(mp4|rmvb|avi|ts)$/
  if (type == 'noGif') {
    if (/(.*)\.(jpg|jpeg|png|JPG|PNG)$/.test(fileValueSuffix)) {
      // 根据后缀，判断是否符合图片格式
      return 'noGif'
    }
    return
  }
  if (/(.*)\.(mp4)$/.test(fileValueSuffix)) {
    // 根据后缀，判断是否符合视频格式
    return 'video'
  }
  if (/(.*)\.(mp3|wma|wav|amr|m4a)$/.test(fileValueSuffix)) {
    // 根据后缀，判断是否符合视频格式
    return 'audio'
  }
  if (/(.*)\.(gif|jpg|jpeg|png|GIF|JPG|PNG)$/.test(fileValueSuffix)) {
    // 根据后缀，判断是否符合图片格式
    return 'image'
  }

  return false
}
export const debounce = function (fun: { (): void; apply?: any }, immediate: number, duration: number | undefined) {
  duration = duration || 1000
  let timer: number | null | undefined = null
  return () => {
    const that = this
    const args = arguments
    if (timer) {
      clearTimeout(timer)
    }
    if (immediate) {
      const callNow = !timer
      if (callNow) {
        fun.apply(that, args)
      }
      timer = setTimeout(() => {
        timer = null
      }, duration)
    } else {
      timer = setTimeout(() => {
        fun.apply(that, args)
      }, duration)
    }
  }
}

export const throttle = function (fn: { call: (arg0: any, arg1: any) => void }, duration = 1000) {
  // 节流函数
  let last: number
  let timer: number | null | undefined = null
  let now
  return () => {
    if (timer === null) {
      last = new Date().getTime()
      timer = setTimeout(() => {
        timer = null
        fn.call(this, ...arguments)
      }, duration)
    } else {
      now = new Date().getTime()
      if (now - last <= duration) {
        clearTimeout(timer)
        last = now
        timer = setTimeout(() => {
          timer = null
          fn.call(this, ...arguments)
        }, duration)
      }
    }
  }
}

/**
 * 通过图片地址获取图片信息
 * @param {Array} list 图片地址
 */
export async function getImageInfoByPath(list: string | any[]) {
  const result = []
  for (let i = 0; i < list.length; i++) {
    let info = await new Promise((resolve, reject) => {
      const v = list[i]
      let imgDom = document.createElement('img')
      imgDom.src = v
      imgDom.onload = function (e) {
        resolve({
          url: v,
          width: imgDom.width,
          height: imgDom.height,
          mph: Number(((imgDom.width / 750) * imgDom.height).toFixed(2))
        })
      }
      imgDom.onerror = function (e) {
        console.log('img load error', e)
        reject(new Error('请上传正确的图片'))
        // ElMessage.warning('请上传正确的图片')
      }
    })
    result.push(info)
  }
  return result
}

// 动态引入图片
export const requireImg = (image: any) => {
  return new URL(`../assets/images/${image}`, import.meta.url).href
}
// 格式化时间
export function formatDate(str: any, tpl = 'YYYY-MM-DD HH:mm:ss') {
  if (!str) return ''
  // 返回格式化后的字符串, str: Date类型数据
  return moment(str).format(tpl)
}

// 复制
export const copy = async (text: string, cb?: () => any) => {
  try {
    await toClipboard(text)
    cb ? cb() : message.success('复制成功！')
  } catch (error) {
    message.error('复制失败！')
  }
}

/**
 * 保留两位小数
 * @param {Number} num
 * @return number
 */
export const toDecimal = (num: any) => {
  let result = parseFloat(num)
  if (isNaN(result)) return result
  result = Math.floor(num * 100) / 100
  return result
}

//处理省市区参数
export const arrToAreas = (arr) => {
  let obj = {}
    ;[...new Set(arr.map((v) => v[0]))].forEach((v) => {
      obj[v] = {}
    })
  arr.forEach((v) => {
    let item = obj[v[0]]
    if (!item[v[1]]) item[v[1]] = []
    item[v[1]].push(Number(v[2]))
  })
  return obj
}

/**
 * table表格排序 返回规则
 */
export function tablePropSort(row: any) {
  let text = ''
  switch (row.order) {
    case 'ascend':
      text = 'asc'
      break
    case 'descend':
      text = 'desc'
      break
    default:
      text = ''
      break
  }
  return text
}

//生成随机字符 randomFlag 是否任意长度 min 任意长度最小位[固定位数] max 任意长度最大位
export const randomWord = (randomFlag, min, max) => {
  let str = '',
    range = min,
    arr = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      'a',
      'b',
      'c',
      'd',
      'e',
      'f',
      'g',
      'h',
      'i',
      'j',
      'k',
      'l',
      'm',
      'n',
      'o',
      'p',
      'q',
      'r',
      's',
      't',
      'u',
      'v',
      'w',
      'x',
      'y',
      'z',
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'I',
      'J',
      'K',
      'L',
      'M',
      'N',
      'O',
      'P',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
      'Z',
      '!',
      '@',
      '#',
      '$',
      '%',
      '&',
      '*',
      '?'
    ]

  if (randomFlag) {
    range = Math.round(Math.random() * (max - min)) + min // 任意长度
  }
  for (let i = 0; i < range; i++) {
    let pos = Math.round(Math.random() * (arr.length - 1))
    str += arr[pos]
  }
  return str
}

// 过滤对象中的无效数据
export const filterValueField = (data) => {
  const params = {}
  if (isObject(data)) {
    Object.keys(data).forEach((key) => {
      if (data[key] !== undefined && data[key] !== null) {
        params[key] = data[key]
      }
    })
  }
  return params
}

// 获取视频封面图
export const convertToImg = (mp4Url) => {
  const fileName = mp4Url.split('/').pop()
  const path = mp4Url.substring(0, mp4Url.lastIndexOf('/') + 1)

  let upload_type = getConfig('UPLOAD_TYPE') || 'oss'
  let img
  if (upload_type == 'cos') {
    img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_0.jpg`
  } else if (upload_type == 'oss') {
    img = `${path.replace('/original/', '/')}${fileName.split('.')[0]}_1.jpg`
  } else {
    img = ''
  }

  return img
}
/**
 * 判断传入的权限字符是否有权限
 * @param {string} permissionStr 权限字符
 * @returns {number} 有权限返回0，无权限点击取消返回1 无权限仍要导出返回2
 */
export async function checkMobilePermission(permissionStr: string): Promise<number> {
  const { isAuth } = useAuth()
  if (isAuth(permissionStr)) {
    return 0;
  } else {
    return new Promise((resolve) => {
      Modal.confirm({
        title: '提示',
        content: '当前账号无导出手机号权限，文档中将不会导出手机号信息，是否继续导出？',
        okText: '确定导出',
        cancelText: '取消',
        onOk: () => {
          resolve(2);
        },
        onCancel: () => {
          resolve(1);
        },
      });
    });
  }
}