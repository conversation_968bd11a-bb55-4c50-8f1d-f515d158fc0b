import { ref } from 'vue'
import { pendingCount, getCheckCount } from '@/api/common'
import { localStg } from '@/utils'
import { debounce } from 'lodash-es'
const timer = ref(true)
const number = ref(0)
const dataInfo = ref(null)
const checkCount = ref({}) as any
const getData = debounce(async () => {
  try {
    if (timer.value) {
      if (!localStg.get('token')) return
      const [pendingRes, checkRes] = await Promise.all([pendingCount(), getCheckCount()])
      number.value = pendingRes.data.count || 0
      dataInfo.value = pendingRes.data || null
      checkCount.value = checkRes.data || 0
      timer.value = false
      setTimeout(() => {
        timer.value = true
      }, 3000)
    }
  } catch (error) {
    console.error(error)
  }
}, 2000)
export function useMenuContentData() {
  return {
    getData,
    number,
    dataInfo,
    checkCount
  }
}
