import { Setting, SettingEnv } from '../types'

const hydSetting: Setting = {
  uiComponents: 'antd',
  mode: 'upAndDown',
  fontSize: 14,
  lineHeight: 1.6,
  borderRadius: 4,
  space: 16,
  height: 34,
  pageAnimation: 'fade-slide',
  layout: {
    headerHeight: 56,
    sidebarWidth: 249,
    minSidebarWidth: 90,
    foldSidebarWidth: 85,
    tagHeight: 30,
    isTag: false,
    layoutBgColor: '#EDEFF8',
    menuBgColor: 'transparent',
    headerBgColor: '#647DFF'
  },
  colors: {
    info: '#647dff',
    primary: '#647DFF',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    textColor: '#333333'
  },
  isMobileWidth: 600,
  env: {

    god: {
      TITLE: '好又多',
      LANUCH: ['dev', 'hyd'],
      NODE_ENV: 'dev',
      PROJECT: 'hyd',
      LAYOUT: 'template3',
      STORGE_TIME_OUT: 60 * 60 * 24 * 7,
      BASE_URL: '/',
      API_URL: 'https://tapi.hupozhidao.com',
      VITE_APP_ADMIN_WEB_URL: 'https://tad.hupozhidao.com',
      IS_PROXY: true,
      PROXY_URI: ['/dev'],
      ICON_ID: 'locl-icon-hyd',
      CAPTCHAAPPId: '2012878865',
      //Copyright 2023 - 2024. All Rights Reserved.
      PROPERTY_RIGHTS: 'Copyright 2023 - 2024. All Rights Reserved.',

      APPLET: {
        COS_URL: '',
        WRAPP_FILE_URL: '/wx_images',
        WRAPP_API: '',
        WEAPP_SERVER_UPLOAD: ''
      },

      COS: {
        url: 'https://tassets.hupozhidao.com',
        path: 'dev',
        headImgDir: '/wx_images/accupload'
      }
    },


    dev: {
      TITLE: '好又多',
      LANUCH: ['dev', 'hyd'],
      NODE_ENV: 'dev',
      PROJECT: 'hyd',
      LAYOUT: 'template3',
      STORGE_TIME_OUT: 60 * 60 * 24 * 7,
      BASE_URL: '/',
      API_URL: 'https://tapi.hupozhidao.com',
      VITE_APP_ADMIN_WEB_URL: 'https://tad.hupozhidao.com',
      IS_PROXY: true,
      PROXY_URI: ['/dev'],
      ICON_ID: 'locl-icon-hyd',
      CAPTCHAAPPId: '2012878865',
      PROPERTY_RIGHTS: 'Copyright 2023 - 2024. All Rights Reserved.',

      APPLET: {
        COS_URL: '',
        WRAPP_FILE_URL: '/wx_images',
        WRAPP_API: '',
        WEAPP_SERVER_UPLOAD: ''
      },

      COS: {
        url: 'https://tassets.hupozhidao.com',
        path: 'dev',
        headImgDir: '/wx_images/accupload'
      }
    },
    test: {
      TITLE: '好又多',
      LANUCH: ['test', 'hyd'],
      NODE_ENV: 'test',
      PROJECT: 'hyd',
      LAYOUT: 'template3',
      STORGE_TIME_OUT: 60 * 60 * 24 * 7,
      BASE_URL: process.env.BASE_URL,
      VITE_APP_ADMIN_WEB_URL: process.env.SHOP_WEB_URL,
      API_URL: process.env.API_URL,
      IS_PROXY: false,
      ICON_ID: 'locl-icon-hyd',
      CAPTCHAAPPId: '2012878865',
      PROPERTY_RIGHTS: 'Copyright 2023 - 2024. All Rights Reserved.',

      APPLET: {
        COS_URL: process.env.COS_URL,
        WRAPP_FILE_URL: '/wx_images',
        WRAPP_API: process.env.API_URL,
        WEAPP_SERVER_UPLOAD: process.env.WEAPP_SERVER_UPLOAD
      },
      UPLOAD_TYPE: (process.env.UPLOAD_TYPE as SettingEnv['UPLOAD_TYPE']) || 'cos',
      COS: {
        url: process.env.COS_URL,
        path: 'test',
        headImgDir: '/wx_images/accupload',
        put: {
          buket: process.env.COS_BUKET,
          region: process.env.COS_REGION,
          secretId: process.env.COS_SECRET_ID,
          secretKey: process.env.COS_SECRET_KEY,
          refreshSecretId: process.env.COS_REFRESH_SECRET_ID,
          refreshSecretKey: process.env.COS_REFRESH_SECRET_KEY
        }
      }
    },

    release: {
      TITLE: '好又多',
      LANUCH: ['prod', 'hyd'],
      NODE_ENV: 'prod',
      PROJECT: 'hyd',
      LAYOUT: 'template3',
      STORGE_TIME_OUT: 60 * 60 * 24 * 7,
      BASE_URL: process.env.BASE_URL,
      VITE_APP_ADMIN_WEB_URL: process.env.SHOP_WEB_URL,
      API_URL: process.env.API_URL,
      IS_PROXY: false,
      ICON_ID: 'locl-icon-hyd',
      CAPTCHAAPPId: '2012878865',
      PROPERTY_RIGHTS: 'Copyright 2023 - 2024. All Rights Reserved.',

      APPLET: {
        COS_URL: process.env.COS_URL,
        WRAPP_FILE_URL: '/wx_images',
        WRAPP_API: process.env.API_URL,
        WEAPP_SERVER_UPLOAD: ''
      },
      UPLOAD_TYPE: (process.env.UPLOAD_TYPE as SettingEnv['UPLOAD_TYPE']) || 'cos',
      COS: {
        url: process.env.COS_URL,
        path: 'test',
        headImgDir: '/wx_images/accupload',
        put: {
          buket: process.env.COS_BUKET,
          region: process.env.COS_REGION,
          secretId: process.env.COS_SECRET_ID,
          secretKey: process.env.COS_SECRET_KEY
        }
      }
    },
    prod: {
      TITLE: '好又多',
      LANUCH: ['prod', 'hyd'],
      NODE_ENV: 'prod',
      PROJECT: 'hyd',
      LAYOUT: 'template3',
      STORGE_TIME_OUT: 60 * 60 * 24 * 7,
      BASE_URL: process.env.BASE_URL,
      VITE_APP_ADMIN_WEB_URL: process.env.SHOP_WEB_URL,
      API_URL: process.env.API_URL,
      IS_PROXY: false,
      ICON_ID: 'locl-icon-hyd',
      CAPTCHAAPPId: '2012878865',
      PROPERTY_RIGHTS: 'Copyright 2023 - 2024. All Rights Reserved.',

      APPLET: {
        COS_URL: process.env.COS_URL,
        WRAPP_FILE_URL: '/wx_images',
        WRAPP_API: process.env.API_URL,
        WEAPP_SERVER_UPLOAD: process.env.WEAPP_SERVER_UPLOAD
      },
      UPLOAD_TYPE: (process.env.UPLOAD_TYPE as SettingEnv['UPLOAD_TYPE']) || 'cos',
      COS: {
        url: process.env.COS_URL,
        path: 'prod',
        headImgDir: '/wx_images/accupload',
        put: {
          buket: process.env.COS_BUKET,
          region: process.env.COS_REGION,
          secretId: process.env.COS_SECRET_ID,
          secretKey: process.env.COS_SECRET_KEY,
          refreshSecretId: process.env.COS_REFRESH_SECRET_ID,
          refreshSecretKey: process.env.COS_REFRESH_SECRET_KEY
        }
      }
    }
  },
  files: [
    {
      name: 'favicon',
      type: 'svg',
      assign: 'serverFile/favicon_hyd.svg',
      w: 45,
      h: 48
    },
    {
      name: 'logo',
      type: 'png',
      assign: 'serverFile/logo_hyd.png',
      w: 76,
      h: 26
    },
    {
      name: 'h5_logo',
      type: 'png',
      assign: 'serverFile/min_logo_hyd.png',
      w: 24,
      h: 26
    },
    {
      name: 'logo_white',
      type: 'png',
      assign: 'serverFile/logo_hyd.png',
      w: 76,
      h: 26
    },
    {
      name: 'logo_orange',
      type: 'png',
      assign: 'serverFile/logo_hyd.png',
      w: 76,
      h: 26
    }
  ],
  exetrnal: {
    css: [
      {
        src: 'assets/css/hyd.scss'
      }
    ]
  }
}
export default hydSetting
